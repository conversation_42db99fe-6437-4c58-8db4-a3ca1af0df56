<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reasoning Engine Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .reasoning-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .step {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
        }
        .step-header {
            font-weight: bold;
            color: #495057;
        }
        .step-content {
            color: #6c757d;
            font-size: 14px;
            margin-top: 5px;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .metric-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧠 Reasoning Engine Test Suite</h1>
        <p>This page tests the MVP 5 Reasoning Engine implementation</p>
        
        <div class="test-controls">
            <button class="test-button" onclick="testSimpleQuery()">Test Simple Query</button>
            <button class="test-button" onclick="testComplexQuery()">Test Complex Query</button>
            <button class="test-button" onclick="testMathematicalQuery()">Test Mathematical Query</button>
            <button class="test-button" onclick="testProgrammingQuery()">Test Programming Query</button>
            <button class="test-button" onclick="testAnalyticalQuery()">Test Analytical Query</button>
            <button class="test-button" onclick="runAllTests()">Run All Tests</button>
            <button class="test-button" onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="results"></div>
        
        <div id="reasoningDisplay" class="reasoning-display" style="display: none;">
            <h3>Reasoning Process Visualization</h3>
            <div id="reasoningContent"></div>
        </div>
        
        <div id="metricsDisplay" style="display: none;">
            <h3>Performance Metrics</h3>
            <div id="metricsContent" class="metrics"></div>
        </div>
    </div>

    <script type="module">
        // Import reasoning services (simulated for testing)
        const reasoningService = {
            async analyzeAndReason(query, context = {}) {
                console.log('🧠 Analyzing query:', query);
                
                // Simulate reasoning analysis
                const complexity = this.analyzeComplexity(query);
                const needsReasoning = complexity.score >= 30;
                
                if (!needsReasoning) {
                    return {
                        useReasoning: false,
                        complexity: complexity,
                        reason: 'Query is straightforward'
                    };
                }
                
                // Generate reasoning steps
                const reasoningSteps = this.generateSteps(query, complexity);
                const reasoningChain = this.buildChain(reasoningSteps);
                
                return {
                    useReasoning: true,
                    complexity: complexity,
                    reasoningSteps: reasoningSteps,
                    reasoningChain: reasoningChain,
                    enhancedPrompt: this.createEnhancedPrompt(query, reasoningChain),
                    metadata: {
                        timestamp: new Date().toISOString(),
                        stepCount: reasoningSteps.length,
                        estimatedDifficulty: complexity.level
                    }
                };
            },
            
            analyzeComplexity(query) {
                let score = 0;
                const length = query.length;
                
                // Length factor
                score += Math.min(length / 10, 20);
                
                // Complexity indicators
                if (/\b(how to|explain|analyze|compare)\b/i.test(query)) score += 15;
                if (/\b(step|process|method)\b/i.test(query)) score += 10;
                if (/\b(calculate|solve|equation)\b/i.test(query)) score += 20;
                if (/\b(code|program|algorithm)\b/i.test(query)) score += 15;
                if (/\?.*\?/.test(query)) score += 10; // Multiple questions
                
                let level = 'simple';
                if (score >= 70) level = 'very_complex';
                else if (score >= 50) level = 'complex';
                else if (score >= 30) level = 'moderate';
                
                return {
                    score: Math.round(score),
                    level: level,
                    reasoning: this.getComplexityReason(score)
                };
            },
            
            getComplexityReason(score) {
                if (score >= 70) return 'requires deep analysis and multiple reasoning steps';
                if (score >= 50) return 'involves complex concepts and structured thinking';
                if (score >= 30) return 'needs systematic approach and clear reasoning';
                return 'is straightforward and can be answered directly';
            },
            
            generateSteps(query, complexity) {
                const steps = [];
                
                // Always start with understanding
                steps.push({
                    id: 1,
                    type: 'understanding',
                    title: 'Problem Analysis',
                    description: 'Understanding the main question and requirements',
                    reasoning: 'First, I need to clearly understand what is being asked.',
                    confidence: 0.9
                });
                
                // Add steps based on complexity
                if (complexity.score >= 30) {
                    steps.push({
                        id: 2,
                        type: 'information',
                        title: 'Information Gathering',
                        description: 'Identifying required information and resources',
                        reasoning: 'I need to gather relevant information to solve this effectively.',
                        confidence: 0.8
                    });
                }
                
                if (complexity.score >= 50) {
                    steps.push({
                        id: 3,
                        type: 'solving',
                        title: 'Solution Development',
                        description: 'Working through the problem systematically',
                        reasoning: 'Now I can work through the solution step by step.',
                        confidence: 0.7
                    });
                }
                
                if (complexity.score >= 40) {
                    steps.push({
                        id: steps.length + 1,
                        type: 'validation',
                        title: 'Solution Validation',
                        description: 'Verifying the solution addresses the original problem',
                        reasoning: 'I need to ensure my solution actually solves the problem.',
                        confidence: 0.9
                    });
                }
                
                return steps;
            },
            
            buildChain(steps) {
                return {
                    introduction: "I'll approach this step-by-step to ensure a thorough response:",
                    steps: steps.map(step => ({
                        number: step.id,
                        title: step.title,
                        approach: step.reasoning
                    })),
                    conclusion: "This systematic approach will ensure a comprehensive answer."
                };
            },
            
            createEnhancedPrompt(query, chain) {
                return `I need to approach this systematically using reasoning.

Query: "${query}"

My approach:
${chain.steps.map(step => `${step.number}. ${step.title}: ${step.approach}`).join('\n')}

Now I'll work through each step to provide a detailed, well-reasoned response.`;
            }
        };

        const solutionValidator = {
            async validateSolution(query, solution, reasoningData) {
                console.log('🔍 Validating solution...');
                
                // Simulate validation
                const relevanceScore = this.calculateRelevance(query, solution);
                const qualityScore = this.assessQuality(solution);
                const completenessScore = this.checkCompleteness(query, solution);
                
                const overallScore = Math.round((relevanceScore + qualityScore + completenessScore) / 3);
                
                return {
                    passed: overallScore >= 70,
                    score: overallScore,
                    details: {
                        relevance: { score: relevanceScore },
                        quality: { score: qualityScore },
                        completeness: { score: completenessScore }
                    },
                    recommendations: overallScore < 70 ? ['Improve solution quality'] : ['Excellent solution']
                };
            },
            
            calculateRelevance(query, solution) {
                const queryWords = query.toLowerCase().split(/\s+/);
                const solutionWords = solution.toLowerCase().split(/\s+/);
                const overlap = queryWords.filter(word => solutionWords.includes(word));
                return Math.min(100, (overlap.length / queryWords.length) * 100 + 30);
            },
            
            assessQuality(solution) {
                let score = 60;
                if (solution.length > 100) score += 20;
                if (solution.includes('.')) score += 10;
                if (/\b(because|therefore|thus)\b/i.test(solution)) score += 10;
                return Math.min(100, score);
            },
            
            checkCompleteness(query, solution) {
                let score = 70;
                if (solution.length >= query.length) score += 15;
                if (solution.split('.').length > 2) score += 15;
                return Math.min(100, score);
            }
        };

        const reasoningMetrics = {
            collectMetrics(reasoningData, validationResult) {
                console.log('📊 Collecting metrics...');
                
                const reasoningQuality = reasoningData.useReasoning ? {
                    score: 75 + Math.random() * 20,
                    details: { stepCount: reasoningData.reasoningSteps.length }
                } : { score: 100, note: 'No reasoning needed' };
                
                const solutionQuality = validationResult ? {
                    score: validationResult.score
                } : { score: 75 };
                
                const efficiency = {
                    score: 80 + Math.random() * 15
                };
                
                const overallScore = Math.round(
                    (reasoningQuality.score * 0.3 + solutionQuality.score * 0.4 + efficiency.score * 0.3)
                );
                
                return {
                    overallScore,
                    reasoningQuality,
                    solutionQuality,
                    efficiency,
                    timestamp: new Date().toISOString()
                };
            }
        };

        // Test functions
        window.testSimpleQuery = async function() {
            await runTest("What is the capital of France?", "Simple Query Test");
        };

        window.testComplexQuery = async function() {
            await runTest("How can I design and implement a scalable microservices architecture for an e-commerce platform, considering security, performance, and maintainability?", "Complex Query Test");
        };

        window.testMathematicalQuery = async function() {
            await runTest("Calculate the compound interest for an investment of $10,000 at 5% annual rate for 10 years, and explain the mathematical formula step by step.", "Mathematical Query Test");
        };

        window.testProgrammingQuery = async function() {
            await runTest("Create a Python function that implements a binary search algorithm, explain how it works, and provide examples of its usage.", "Programming Query Test");
        };

        window.testAnalyticalQuery = async function() {
            await runTest("Analyze the pros and cons of remote work versus office work, considering productivity, work-life balance, company culture, and economic factors.", "Analytical Query Test");
        };

        window.runAllTests = async function() {
            addResult('🚀 Running comprehensive reasoning engine test suite...', 'info');
            
            const tests = [
                { query: "What is AI?", name: "Simple Query" },
                { query: "How to build a machine learning model for predicting customer behavior?", name: "Complex Query" },
                { query: "Solve the equation 2x + 5 = 15 and explain each step.", name: "Mathematical Query" },
                { query: "Write a JavaScript function to sort an array and explain the algorithm.", name: "Programming Query" },
                { query: "Compare the advantages and disadvantages of different database types.", name: "Analytical Query" }
            ];
            
            let passedTests = 0;
            let totalTests = tests.length;
            
            for (const test of tests) {
                const result = await runTest(test.query, test.name, false);
                if (result.success) passedTests++;
            }
            
            addResult(`✅ Test suite completed: ${passedTests}/${totalTests} tests passed (${Math.round(passedTests/totalTests*100)}%)`, 
                     passedTests === totalTests ? 'success' : 'warning');
        };

        async function runTest(query, testName, showDetails = true) {
            try {
                addResult(`🧪 Testing: ${testName}`, 'info');
                
                // Step 1: Reasoning Analysis
                const reasoningResult = await reasoningService.analyzeAndReason(query);
                
                if (showDetails) {
                    addResult(`🧠 Reasoning ${reasoningResult.useReasoning ? 'ENABLED' : 'DISABLED'} - Complexity: ${reasoningResult.complexity.level} (${reasoningResult.complexity.score}%)`, 
                             reasoningResult.useReasoning ? 'info' : 'warning');
                }
                
                // Step 2: Solution Validation (simulate)
                const mockSolution = `This is a simulated solution for: ${query.substring(0, 50)}...`;
                const validationResult = reasoningResult.useReasoning 
                    ? await solutionValidator.validateSolution(query, mockSolution, reasoningResult)
                    : null;
                
                if (validationResult && showDetails) {
                    addResult(`🔍 Validation: ${validationResult.passed ? 'PASSED' : 'FAILED'} (${validationResult.score}%)`, 
                             validationResult.passed ? 'success' : 'error');
                }
                
                // Step 3: Metrics Collection
                const metrics = reasoningResult.useReasoning 
                    ? reasoningMetrics.collectMetrics(reasoningResult, validationResult)
                    : null;
                
                if (metrics && showDetails) {
                    addResult(`📊 Overall Performance: ${metrics.overallScore}%`, 
                             metrics.overallScore >= 80 ? 'success' : metrics.overallScore >= 60 ? 'warning' : 'error');
                }
                
                // Display reasoning process
                if (reasoningResult.useReasoning && showDetails) {
                    displayReasoning(reasoningResult);
                    if (metrics) displayMetrics(metrics);
                }
                
                addResult(`✅ ${testName} completed successfully`, 'success');
                return { success: true, reasoning: reasoningResult, validation: validationResult, metrics };
                
            } catch (error) {
                addResult(`❌ ${testName} failed: ${error.message}`, 'error');
                return { success: false, error: error.message };
            }
        }

        function displayReasoning(reasoningData) {
            const display = document.getElementById('reasoningDisplay');
            const content = document.getElementById('reasoningContent');
            
            let html = `
                <div class="step">
                    <div class="step-header">Complexity Analysis</div>
                    <div class="step-content">
                        Level: ${reasoningData.complexity.level} (Score: ${reasoningData.complexity.score}%)<br>
                        Reasoning: This problem ${reasoningData.complexity.reasoning}
                    </div>
                </div>
            `;
            
            reasoningData.reasoningSteps.forEach(step => {
                html += `
                    <div class="step">
                        <div class="step-header">${step.id}. ${step.title}</div>
                        <div class="step-content">
                            <strong>Type:</strong> ${step.type}<br>
                            <strong>Description:</strong> ${step.description}<br>
                            <strong>Reasoning:</strong> ${step.reasoning}<br>
                            <strong>Confidence:</strong> ${Math.round(step.confidence * 100)}%
                        </div>
                    </div>
                `;
            });
            
            content.innerHTML = html;
            display.style.display = 'block';
        }

        function displayMetrics(metrics) {
            const display = document.getElementById('metricsDisplay');
            const content = document.getElementById('metricsContent');
            
            const html = `
                <div class="metric-card">
                    <div class="metric-value">${metrics.overallScore}%</div>
                    <div class="metric-label">Overall Score</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${Math.round(metrics.reasoningQuality.score)}%</div>
                    <div class="metric-label">Reasoning Quality</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${Math.round(metrics.solutionQuality.score)}%</div>
                    <div class="metric-label">Solution Quality</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${Math.round(metrics.efficiency.score)}%</div>
                    <div class="metric-label">Efficiency</div>
                </div>
            `;
            
            content.innerHTML = html;
            display.style.display = 'block';
        }

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultElement = document.createElement('div');
            resultElement.className = `status ${type}`;
            resultElement.innerHTML = message;
            resultsDiv.appendChild(resultElement);
        }

        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('reasoningDisplay').style.display = 'none';
            document.getElementById('metricsDisplay').style.display = 'none';
        };

        // Auto-run basic test on page load
        window.addEventListener('load', function() {
            addResult('🧠 Reasoning Engine Test Suite loaded', 'info');
            addResult('💡 Click any test button to verify reasoning capabilities', 'info');
        });
    </script>
</body>
</html>
