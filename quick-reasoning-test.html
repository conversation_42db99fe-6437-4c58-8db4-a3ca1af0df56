<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Reasoning Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            background: #2a2a2a;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .query-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #444;
            border-radius: 8px;
            background: #333;
            color: white;
            font-size: 16px;
            margin: 10px 0;
            min-height: 100px;
            resize: vertical;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        .test-button:hover {
            transform: translateY(-2px);
        }
        .result {
            background: #333;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .reasoning-step {
            background: #444;
            border: 1px solid #555;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .step-header {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
        }
        .complexity-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin: 5px;
        }
        .simple { background: #28a745; }
        .moderate { background: #ffc107; color: #000; }
        .complex { background: #fd7e14; }
        .very-complex { background: #dc3545; }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric {
            background: #444;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        .metric-label {
            font-size: 12px;
            color: #ccc;
            margin-top: 5px;
        }
        .sample-queries {
            background: #333;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .sample-query {
            background: #444;
            padding: 10px;
            margin: 8px 0;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .sample-query:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 Quick Reasoning Engine Test</h1>
        <p>Enter a query below to see the reasoning engine in action!</p>
        
        <div class="sample-queries">
            <h3>📝 Sample Queries (Click to Use):</h3>
            <div class="sample-query" onclick="useQuery(this.textContent)">
                How can I optimize the performance of a React application with large datasets?
            </div>
            <div class="sample-query" onclick="useQuery(this.textContent)">
                Explain the process of photosynthesis step by step and its importance in the ecosystem.
            </div>
            <div class="sample-query" onclick="useQuery(this.textContent)">
                Design a database schema for a social media platform and explain your design decisions.
            </div>
            <div class="sample-query" onclick="useQuery(this.textContent)">
                What are the key differences between machine learning and deep learning?
            </div>
            <div class="sample-query" onclick="useQuery(this.textContent)">
                Calculate the area of a circle with radius 5 and explain the formula.
            </div>
        </div>
        
        <textarea 
            id="queryInput" 
            class="query-input" 
            placeholder="Enter your query here... (Try complex, multi-step questions for best results)"
        ></textarea>
        
        <div>
            <button class="test-button" onclick="testReasoning()">🧠 Test Reasoning</button>
            <button class="test-button" onclick="clearResults()">🧹 Clear Results</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script type="module">
        // Import the actual reasoning service
        import reasoningService from './src/functions/services/reasoningService.js';
        import solutionValidator from './src/functions/services/solutionValidation.js';
        import reasoningMetrics from './src/functions/services/reasoningMetrics.js';

        window.useQuery = function(query) {
            document.getElementById('queryInput').value = query;
        };

        window.testReasoning = async function() {
            const query = document.getElementById('queryInput').value.trim();
            
            if (!query) {
                addResult('❌ Please enter a query to test', 'error');
                return;
            }

            addResult(`🔍 Testing query: "${query.substring(0, 100)}${query.length > 100 ? '...' : ''}"`, 'info');

            try {
                // Step 1: Analyze reasoning need
                const reasoningResult = await reasoningService.analyzeAndReason(query);
                
                displayComplexityAnalysis(reasoningResult.complexity);
                
                if (!reasoningResult.useReasoning) {
                    addResult('ℹ️ This query is straightforward and doesn\'t require complex reasoning', 'info');
                    addResult(`💡 Reason: ${reasoningResult.reason}`, 'info');
                    return;
                }

                // Step 2: Display reasoning process
                displayReasoningProcess(reasoningResult);
                
                // Step 3: Simulate solution and validate
                const mockSolution = generateMockSolution(query, reasoningResult);
                const validationResult = await solutionValidator.validateSolution(query, mockSolution, reasoningResult);
                
                displayValidationResults(validationResult);
                
                // Step 4: Collect metrics
                const metrics = reasoningMetrics.collectMetrics(reasoningResult, validationResult);
                displayMetrics(metrics);
                
                addResult('✅ Reasoning analysis complete!', 'success');

            } catch (error) {
                addResult(`❌ Error during reasoning test: ${error.message}`, 'error');
                console.error('Reasoning test error:', error);
            }
        };

        function displayComplexityAnalysis(complexity) {
            const badgeClass = complexity.level.replace('_', '-');
            const html = `
                <div class="result">
                    <h3>🎯 Complexity Analysis</h3>
                    <span class="complexity-badge ${badgeClass}">${complexity.level.replace('_', ' ').toUpperCase()}</span>
                    <p><strong>Score:</strong> ${complexity.score}/100</p>
                    <p><strong>Analysis:</strong> This problem ${complexity.reasoning}</p>
                </div>
            `;
            document.getElementById('results').innerHTML += html;
        }

        function displayReasoningProcess(reasoningResult) {
            let html = `
                <div class="result">
                    <h3>🧠 Reasoning Process (${reasoningResult.reasoningSteps.length} steps)</h3>
            `;

            // Show decomposition if available
            if (reasoningResult.decomposition) {
                html += `
                    <div class="reasoning-step">
                        <div class="step-header">🎯 Problem Decomposition</div>
                        <p><strong>Main Goal:</strong> ${reasoningResult.decomposition.mainGoal.target}</p>
                        <p><strong>Sub-problems:</strong> ${reasoningResult.decomposition.subProblems.length}</p>
                    </div>
                `;
            }

            // Show reasoning steps
            reasoningResult.reasoningSteps.forEach(step => {
                html += `
                    <div class="reasoning-step">
                        <div class="step-header">${step.id}. ${step.title}</div>
                        <p><strong>Type:</strong> ${step.type}</p>
                        <p><strong>Description:</strong> ${step.description}</p>
                        <p><strong>Reasoning:</strong> ${step.reasoning}</p>
                        <p><strong>Confidence:</strong> ${Math.round(step.confidence * 100)}%</p>
                    </div>
                `;
            });

            html += '</div>';
            document.getElementById('results').innerHTML += html;
        }

        function displayValidationResults(validationResult) {
            if (!validationResult) {
                addResult('⚠️ Validation result not available', 'warning');
                return;
            }

            const statusColor = validationResult.passed ? '#28a745' : '#dc3545';
            const statusText = validationResult.passed ? 'PASSED' : 'FAILED';

            const html = `
                <div class="result">
                    <h3>🔍 Solution Validation</h3>
                    <p><strong>Status:</strong> <span style="color: ${statusColor}">${statusText}</span></p>
                    <p><strong>Overall Score:</strong> ${validationResult.score}%</p>
                    <div class="metrics">
                        <div class="metric">
                            <div class="metric-value">${validationResult.details?.relevance?.score || 'N/A'}</div>
                            <div class="metric-label">Relevance</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${validationResult.details?.quality?.score || 'N/A'}</div>
                            <div class="metric-label">Quality</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${validationResult.details?.completeness?.score || 'N/A'}</div>
                            <div class="metric-label">Completeness</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${validationResult.details?.reasoning?.score || 'N/A'}</div>
                            <div class="metric-label">Reasoning</div>
                        </div>
                    </div>
                    <p><strong>Recommendations:</strong></p>
                    <ul>
                        ${(validationResult.recommendations || ['No specific recommendations']).map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
            `;
            document.getElementById('results').innerHTML += html;
        }

        function displayMetrics(metrics) {
            const html = `
                <div class="result">
                    <h3>📊 Performance Metrics</h3>
                    <div class="metrics">
                        <div class="metric">
                            <div class="metric-value">${metrics.overallScore}%</div>
                            <div class="metric-label">Overall Score</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${Math.round(metrics.reasoningQuality.score)}%</div>
                            <div class="metric-label">Reasoning Quality</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${Math.round(metrics.solutionQuality.score)}%</div>
                            <div class="metric-label">Solution Quality</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${Math.round(metrics.efficiency.score)}%</div>
                            <div class="metric-label">Efficiency</div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('results').innerHTML += html;
        }

        function generateMockSolution(query, reasoningResult) {
            // Generate a realistic mock solution based on the query and reasoning
            const solutions = {
                'react': 'To optimize React performance with large datasets, implement virtualization, use React.memo for component memoization, optimize re-renders with useCallback and useMemo, implement pagination or infinite scrolling, and consider using a state management library like Redux for efficient data handling.',
                'photosynthesis': 'Photosynthesis occurs in two main stages: light-dependent reactions in the thylakoids and light-independent reactions (Calvin cycle) in the stroma. Plants capture light energy, convert CO2 and water into glucose, and release oxygen as a byproduct.',
                'database': 'For a social media platform, design tables for users, posts, comments, likes, follows, and messages. Use foreign keys for relationships, implement indexing for performance, consider denormalization for read-heavy operations, and plan for horizontal scaling.',
                'machine learning': 'Machine learning uses algorithms to find patterns in data, while deep learning is a subset that uses neural networks with multiple layers. Deep learning excels at complex pattern recognition but requires more data and computational resources.',
                'circle': 'The area of a circle with radius 5 is π × r² = π × 5² = 25π ≈ 78.54 square units. The formula A = πr² comes from integrating the circumference formula over the radius.'
            };

            // Find the most relevant solution
            const queryLower = query.toLowerCase();
            for (const [key, solution] of Object.entries(solutions)) {
                if (queryLower.includes(key)) {
                    return solution;
                }
            }

            // Default solution
            return `This is a comprehensive response addressing the query: "${query.substring(0, 100)}...". The solution follows the ${reasoningResult.reasoningSteps.length}-step reasoning process to ensure accuracy and completeness.`;
        }

        function addResult(message, type = 'info') {
            const colors = {
                info: '#667eea',
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107'
            };

            const html = `
                <div class="result" style="border-left-color: ${colors[type]}">
                    ${message}
                </div>
            `;
            document.getElementById('results').innerHTML += html;
        }

        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        // Auto-load message
        window.addEventListener('load', function() {
            addResult('🚀 Quick Reasoning Test ready! Try the sample queries or enter your own.', 'info');
        });
    </script>
</body>
</html>
