# 🧪 AGI Playground - Testing Examples Guide

## Overview

This guide provides comprehensive testing examples for the AGI Playground, demonstrating how to validate all system components and features.

## Quick Start Testing

### Browser Testing (Recommended)

**Step 1: Access the Multi-Agent Tester**
1. Open your browser to `http://localhost:5173/`
2. Click "Multi-Agent Tester" in the navigation
3. Wait for "Browser tests ready" status

**Step 2: Run Quick Test**
```
1. Select "⚡ Quick Browser Test" from dropdown
2. Click "▶️ Run Test"
3. Watch logs for real-time progress
4. Check results panel for success/failure
```

**Expected Output:**
```
✅ Created agents: research_agent_001, analysis_agent_002
✅ Coordination: Success
✅ Synthesis: Success
🎉 Quick browser test completed successfully!
```

## Individual Agent Testing

### 🔍 Research Agent Test

**Test Case 1: Basic Research**
```javascript
// In browser console or Multi-Agent Tester
const researchTask = {
  type: 'research_synthesis',
  description: 'Research the latest developments in quantum computing',
  requirements: {
    depth: 'comprehensive',
    focus_areas: ['hardware', 'software', 'applications'],
    timeframe: 'last_year'
  }
}

const result = await specializedAgents.executeTask('research', researchTask)
console.log('Research Result:', result)
```

**Expected Result:**
```javascript
{
  success: true,
  content: "Quantum computing has seen significant advances...",
  key_findings: [
    "IBM's 1000-qubit processor breakthrough",
    "Google's quantum error correction progress",
    "Commercial applications in cryptography"
  ],
  sources: [
    { title: "Quantum Computing Advances 2024", credibility: 0.9 },
    // ... more sources
  ],
  confidence: 0.87,
  research_quality: 'high'
}
```

**Test Case 2: Fact Checking**
```javascript
const factCheckTask = {
  type: 'fact_verification',
  description: 'Verify claims about renewable energy adoption rates',
  requirements: {
    claims: [
      "Solar energy costs dropped 70% in the last decade",
      "Wind power generates 25% of global electricity"
    ],
    verification_level: 'strict'
  }
}

const result = await specializedAgents.executeTask('research', factCheckTask)
```

### 💻 Coding Agent Test

**Test Case 1: React Component Generation**
```javascript
const codingTask = {
  type: 'code_generation',
  description: 'Create a responsive dashboard component',
  requirements: {
    language: 'javascript',
    framework: 'react',
    features: ['responsive_design', 'dark_mode', 'data_visualization'],
    styling: 'tailwind_css',
    testing: true
  }
}

const result = await specializedAgents.executeTask('coding', codingTask)
console.log('Generated Code:', result.code)
console.log('Test Cases:', result.tests)
```

**Expected Result:**
```javascript
{
  success: true,
  code: `
import React, { useState, useEffect } from 'react'
import { Chart } from 'chart.js'

const Dashboard = ({ data, darkMode = false }) => {
  // Complete component implementation
  return (
    <div className={darkMode ? 'dark' : ''}>
      {/* Responsive dashboard layout */}
    </div>
  )
}

export default Dashboard
  `,
  tests: `
import { render, screen } from '@testing-library/react'
import Dashboard from './Dashboard'

describe('Dashboard Component', () => {
  test('renders dashboard elements', () => {
    // Test implementation
  })
})
  `,
  documentation: "## Dashboard Component\n\nA responsive dashboard...",
  quality_score: 0.92
}
```

**Test Case 2: API Development**
```javascript
const apiTask = {
  type: 'api_development',
  description: 'Create REST API for task management',
  requirements: {
    language: 'javascript',
    framework: 'express',
    database: 'mongodb',
    authentication: 'jwt',
    endpoints: ['tasks', 'users', 'projects'],
    documentation: 'swagger'
  }
}

const result = await specializedAgents.executeTask('coding', apiTask)
```

### 📊 Analysis Agent Test

**Test Case 1: Data Trend Analysis**
```javascript
const analysisTask = {
  type: 'data_analysis',
  description: 'Analyze website traffic trends',
  requirements: {
    data: [
      { month: 'Jan', visitors: 10000, conversions: 250 },
      { month: 'Feb', visitors: 12000, conversions: 300 },
      { month: 'Mar', visitors: 15000, conversions: 450 },
      // ... more data
    ],
    analysis_type: 'trend_and_forecast',
    metrics: ['growth_rate', 'conversion_rate', 'seasonality']
  }
}

const result = await specializedAgents.executeTask('analysis', analysisTask)
console.log('Analysis:', result.analysis)
console.log('Insights:', result.insights)
```

**Expected Result:**
```javascript
{
  success: true,
  analysis: {
    trends: [
      "Steady 20% month-over-month growth in visitors",
      "Conversion rate improving from 2.5% to 3.0%",
      "Strong upward trajectory with seasonal peaks"
    ],
    growth_rate: 0.20,
    conversion_optimization: "15% improvement potential identified",
    seasonality: "Peak traffic in Q4, lowest in Q1"
  },
  insights: [
    "Mobile traffic growing faster than desktop",
    "Blog content driving 40% of new visitors",
    "Email campaigns showing highest conversion rates"
  ],
  forecasts: {
    next_month: { visitors: 18000, confidence: 0.85 },
    next_quarter: { visitors: 65000, confidence: 0.78 }
  },
  recommendations: [
    "Increase mobile optimization efforts",
    "Expand content marketing strategy",
    "A/B test email campaign frequency"
  ],
  confidence: 0.89
}
```

**Test Case 2: Statistical Analysis**
```javascript
const statsTask = {
  type: 'statistical_analysis',
  description: 'Analyze A/B test results for landing page',
  requirements: {
    data: {
      control: { visitors: 1000, conversions: 25 },
      variant: { visitors: 1000, conversions: 35 }
    },
    analysis_type: 'hypothesis_testing',
    confidence_level: 0.95
  }
}

const result = await specializedAgents.executeTask('analysis', statsTask)
```

### 🎨 Creative Agent Test

**Test Case 1: Marketing Content Creation**
```javascript
const creativeTask = {
  type: 'content_creation',
  description: 'Create marketing campaign for AI productivity tool',
  requirements: {
    target_audience: 'small_business_owners',
    tone: 'professional_yet_approachable',
    channels: ['email', 'social_media', 'blog'],
    key_messages: ['time_saving', 'easy_to_use', 'affordable'],
    content_types: ['headlines', 'copy', 'call_to_action']
  }
}

const result = await specializedAgents.executeTask('creative', creativeTask)
console.log('Creative Content:', result.content)
```

**Expected Result:**
```javascript
{
  success: true,
  content: {
    headlines: [
      "Save 10 Hours a Week with AI-Powered Productivity",
      "The Small Business Owner's Secret to Getting More Done",
      "Finally, AI That Actually Makes Your Life Easier"
    ],
    email_copy: "Dear [Name], Imagine having an AI assistant that...",
    social_media: {
      linkedin: "Small business owners are discovering...",
      twitter: "🚀 New AI tool saves SMBs 10+ hours/week...",
      facebook: "Running a small business is tough enough..."
    },
    blog_outline: [
      "Introduction: The Small Business Time Crunch",
      "How AI Can Transform Your Daily Operations",
      "Real Success Stories from Our Users",
      "Getting Started: Your First Week with AI"
    ],
    call_to_action: [
      "Start Your Free 14-Day Trial Today",
      "See How Much Time You Could Save",
      "Join 10,000+ Business Owners Already Saving Time"
    ]
  },
  creativity_score: 0.88,
  brand_alignment: 0.92,
  target_relevance: 0.85,
  emotional_impact: 0.79
}
```

**Test Case 2: Brainstorming Session**
```javascript
const brainstormTask = {
  type: 'brainstorming',
  description: 'Generate innovative features for fitness app',
  requirements: {
    focus_area: 'user_engagement',
    innovation_level: 'high',
    target_users: 'busy_professionals',
    constraints: ['mobile_first', 'privacy_focused'],
    idea_count: 10
  }
}

const result = await specializedAgents.executeTask('creative', brainstormTask)
```

### 🎯 Coordinator Agent Test

**Test Case 1: Project Coordination**
```javascript
const coordinatorTask = {
  type: 'task_coordination',
  description: 'Coordinate development of mobile app MVP',
  requirements: {
    agents: ['research', 'analysis', 'coding', 'creative'],
    timeline: '8_weeks',
    deliverables: [
      'market_research',
      'user_personas',
      'technical_architecture',
      'ui_design',
      'mvp_implementation'
    ],
    constraints: ['budget_limited', 'fast_timeline']
  }
}

const result = await specializedAgents.executeTask('coordinator', coordinatorTask)
console.log('Coordination Plan:', result.plan)
```

**Expected Result:**
```javascript
{
  success: true,
  plan: {
    phases: [
      {
        name: "Discovery Phase",
        duration: "2 weeks",
        agents: ["research", "analysis"],
        deliverables: ["market_research", "user_personas"],
        parallel_execution: true
      },
      {
        name: "Design Phase",
        duration: "2 weeks",
        agents: ["creative", "coding"],
        deliverables: ["ui_design", "technical_architecture"],
        dependencies: ["Discovery Phase"]
      },
      {
        name: "Development Phase",
        duration: "3 weeks",
        agents: ["coding"],
        deliverables: ["mvp_implementation"],
        dependencies: ["Design Phase"]
      },
      {
        name: "Testing & Launch",
        duration: "1 week",
        agents: ["analysis", "coordinator"],
        deliverables: ["testing_results", "launch_plan"],
        dependencies: ["Development Phase"]
      }
    ],
    total_duration: "8 weeks",
    critical_path: ["Discovery → Design → Development → Launch"],
    resource_allocation: {
      research: "25%",
      analysis: "20%",
      coding: "40%",
      creative: "15%"
    }
  },
  risk_assessment: [
    { risk: "Technical complexity", probability: 0.3, impact: "medium" },
    { risk: "Timeline pressure", probability: 0.6, impact: "high" },
    { risk: "Resource availability", probability: 0.2, impact: "low" }
  ],
  optimization_suggestions: [
    "Parallelize research and analysis phases",
    "Start technical architecture during research phase",
    "Prepare testing environment early"
  ],
  coordination_quality: 0.91
}
```

## Multi-Agent Collaboration Testing

### Test Case 1: Business Plan Creation

**Scenario:** Create comprehensive business plan for tech startup

```javascript
const collaborationTask = {
  description: 'Create comprehensive business plan for AI-powered customer service startup',
  requirements: {
    domains: ['research', 'analysis', 'creative', 'coding'],
    sections: [
      'executive_summary',
      'market_analysis',
      'competitive_landscape',
      'technical_architecture',
      'financial_projections',
      'marketing_strategy',
      'implementation_plan'
    ],
    quality: 'investor_ready',
    timeline: '1_week'
  }
}

const result = await agentCoordinationSystem.coordinateTask(collaborationTask)
```

**Expected Workflow:**
```
1. Research Agent → Market research, competitor analysis
2. Analysis Agent → Financial modeling, market sizing
3. Coding Agent → Technical architecture, development plan
4. Creative Agent → Marketing strategy, brand positioning
5. Coordinator Agent → Integration, executive summary
```

**Expected Result:**
```javascript
{
  success: true,
  coordinationId: 'coord_001',
  result: {
    content: "# Business Plan: AI Customer Service Startup...",
    sections: {
      executive_summary: "...",
      market_analysis: "...",
      technical_architecture: "...",
      // ... all sections
    },
    quality_score: 0.89,
    investor_readiness: 0.92
  },
  collaboration_metrics: {
    agents_involved: 5,
    total_time: 3600, // seconds
    coordination_efficiency: 0.87,
    result_coherence: 0.91
  }
}
```

### Test Case 2: Product Launch Campaign

**Scenario:** Coordinate complete product launch campaign

```javascript
const launchTask = {
  description: 'Coordinate product launch campaign for new SaaS platform',
  requirements: {
    product: 'project_management_saas',
    target_market: 'small_to_medium_businesses',
    launch_date: '2024-03-01',
    budget: 50000,
    channels: ['digital_marketing', 'content_marketing', 'pr', 'partnerships']
  }
}

const result = await agentCoordinationSystem.coordinateTask(launchTask)
```

## Performance Testing

### Load Testing

**Test Case 1: Concurrent Agent Execution**
```javascript
const performanceTest = async () => {
  const startTime = Date.now()
  
  // Create multiple concurrent tasks
  const tasks = [
    specializedAgents.executeTask('research', { description: 'Task 1' }),
    specializedAgents.executeTask('analysis', { description: 'Task 2' }),
    specializedAgents.executeTask('creative', { description: 'Task 3' }),
    specializedAgents.executeTask('coding', { description: 'Task 4' }),
    specializedAgents.executeTask('coordinator', { description: 'Task 5' })
  ]
  
  const results = await Promise.allSettled(tasks)
  const endTime = Date.now()
  
  console.log(`Executed ${tasks.length} concurrent tasks in ${endTime - startTime}ms`)
  console.log(`Success rate: ${results.filter(r => r.status === 'fulfilled').length}/${tasks.length}`)
}

await performanceTest()
```

**Expected Performance:**
- **Execution Time:** < 10 seconds for 5 concurrent tasks
- **Success Rate:** > 90%
- **Memory Usage:** < 100MB
- **CPU Usage:** < 50%

### Stress Testing

**Test Case 2: High-Volume Task Processing**
```javascript
const stressTest = async () => {
  const taskCount = 20
  const batchSize = 5
  const results = []
  
  for (let i = 0; i < taskCount; i += batchSize) {
    const batch = []
    for (let j = 0; j < batchSize && i + j < taskCount; j++) {
      batch.push(
        specializedAgents.executeTask('research', {
          description: `Stress test task ${i + j + 1}`
        })
      )
    }
    
    const batchResults = await Promise.allSettled(batch)
    results.push(...batchResults)
    
    // Small delay between batches
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  const successCount = results.filter(r => r.status === 'fulfilled').length
  console.log(`Stress test: ${successCount}/${taskCount} tasks successful`)
}

await stressTest()
```

## Error Handling Testing

### Test Case 1: Network Failure Simulation

```javascript
const errorTest = async () => {
  try {
    // Simulate network failure
    const failingTask = {
      type: 'research_synthesis',
      description: 'This task will simulate a network failure',
      requirements: { simulate_failure: true }
    }
    
    const result = await specializedAgents.executeTask('research', failingTask)
    console.log('Error handling test result:', result)
  } catch (error) {
    console.log('Expected error caught:', error.message)
  }
}

await errorTest()
```

### Test Case 2: Invalid Input Handling

```javascript
const invalidInputTest = async () => {
  const invalidTasks = [
    { description: '' }, // Empty description
    { description: null }, // Null description
    { type: 'invalid_type' }, // Invalid type
    { requirements: 'not_an_object' } // Invalid requirements
  ]
  
  for (const task of invalidTasks) {
    try {
      await specializedAgents.executeTask('research', task)
    } catch (error) {
      console.log(`Invalid input handled: ${error.message}`)
    }
  }
}

await invalidInputTest()
```

## Integration Testing

### Test Case 1: End-to-End Workflow

```javascript
const e2eTest = async () => {
  console.log('Starting end-to-end workflow test...')
  
  // Step 1: Create agents
  const researchAgent = agentFramework.createAgent('research')
  const analysisAgent = agentFramework.createAgent('analysis')
  console.log('✅ Agents created')
  
  // Step 2: Execute research task
  const researchResult = await specializedAgents.executeTask('research', {
    description: 'Research AI market trends'
  })
  console.log('✅ Research completed')
  
  // Step 3: Analyze research results
  const analysisResult = await specializedAgents.executeTask('analysis', {
    description: 'Analyze the research findings',
    input_data: researchResult.content
  })
  console.log('✅ Analysis completed')
  
  // Step 4: Synthesize results
  const synthesis = await agentResultSynthesis.synthesizeResults([
    { agentType: 'research', ...researchResult },
    { agentType: 'analysis', ...analysisResult }
  ], 'AI market analysis')
  console.log('✅ Synthesis completed')
  
  console.log('🎉 End-to-end test successful!')
  return synthesis
}

await e2eTest()
```

## Browser Console Testing Commands

### Quick Commands

```javascript
// Initialize test environment
await multiAgentTests.init()

// Run quick test
await multiAgentTests.quick()

// Test all agent types
await multiAgentTests.agents()

// Test collaboration
await multiAgentTests.collaboration()

// Performance test
await multiAgentTests.performance()

// Complete test suite
await multiAgentTests.complete()
```

### Advanced Testing

```javascript
// Custom agent test
const customTest = async () => {
  const agent = agentFramework.createAgent('research')
  const result = await specializedAgents.executeTask('research', {
    description: 'Your custom research task here'
  })
  console.log('Custom test result:', result)
}

await customTest()

// Performance monitoring
const metrics = agentPerformanceMonitoring.getPerformanceReport('5m')
console.log('Performance metrics:', metrics)

// System health check
const health = agentFramework.getFrameworkStats()
console.log('System health:', health)
```

## Troubleshooting Test Failures

### Common Issues and Solutions

1. **"Browser tests not ready"**
   - Wait for initialization to complete
   - Refresh the page and try again
   - Check console for error messages

2. **"Agent creation failed"**
   - Verify API keys are configured
   - Check network connectivity
   - Try demo mode first

3. **"Synthesis failed"**
   - Check agent results are valid
   - Verify task description is clear
   - Try simpler synthesis strategy

4. **"Performance test timeout"**
   - Reduce concurrent task count
   - Check system resources
   - Increase timeout values

### Debug Mode

```javascript
// Enable debug logging
localStorage.setItem('agi_debug', 'true')
location.reload()

// View debug logs
console.log(window.agiDebugLogs)

// Monitor agent communications
agentCommunicationProtocol.enableLogging()
```

---

*This testing guide provides comprehensive examples for validating all aspects of the AGI Playground system.*
