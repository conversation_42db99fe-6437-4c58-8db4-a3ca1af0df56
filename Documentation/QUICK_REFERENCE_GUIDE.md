# ⚡ AGI Playground - Quick Reference Guide

## 🚀 Getting Started (5 Minutes)

### 1. Installation
```bash
npm install
npm run dev
# Open http://localhost:5173/
```

### 2. Configuration
```env
# .env file
VITE_GEMINI_API_KEY=your_api_key_here
```

### 3. First Test
1. Go to "Multi-Agent Tester"
2. Select "⚡ Quick Browser Test"
3. Click "▶️ Run Test"

---

## 🎯 Navigation Menu

| Icon | Page | Purpose |
|------|------|---------|
| 🧠 | AGI Systems | Main dashboard and overview |
| ▶️ | AGI Playground | AI chat interface with multi-mode support |
| 👥 | Multi-Agent Tester | Test and demonstrate multi-agent system |
| 📖 | Blog AGI | AI-powered blog and content creation |
| 🛒 | Marketplace | AI tools and services marketplace |
| 🎯 | Research | Research tools and web search |
| 🌐 | Network | Collaboration and networking features |
| ⚙️ | Settings | System configuration and preferences |

---

## 🤖 Agent Types Quick Reference

| Agent | Icon | Specialization | Best For |
|-------|------|----------------|----------|
| Research | 🔍 | Information gathering | Market research, fact-checking, data collection |
| Coding | 💻 | Software development | Code generation, debugging, technical docs |
| Analysis | 📊 | Data analysis | Statistics, trends, logical reasoning |
| Creative | 🎨 | Content creation | Marketing, brainstorming, storytelling |
| Coordinator | 🎯 | Task orchestration | Project management, workflow optimization |

---

## 🧪 Testing Quick Commands

### Browser Console Commands
```javascript
// Initialize tests
await multiAgentTests.init()

// Quick test (30 seconds)
await multiAgentTests.quick()

// Test all agents (2 minutes)
await multiAgentTests.agents()

// Test collaboration (1 minute)
await multiAgentTests.collaboration()

// Performance test (1 minute)
await multiAgentTests.performance()

// Complete suite (5 minutes)
await multiAgentTests.complete()
```

### Command Line Tests
```bash
# Quick test
npm run test:quick

# Full test suite
npm run test

# Specific components
npm run test:agents
npm run test:coordination
npm run test:communication
npm run test:synthesis
```

---

## 💬 Chat Modes

| Mode | Icon | Purpose | Example Use |
|------|------|---------|-------------|
| Chat | 💬 | General conversation | "Explain quantum computing" |
| Research | 🔍 | Web search & analysis | "Research AI market trends" |
| Coding | 💻 | Programming help | "Create a React component" |
| Creative | 🎨 | Content generation | "Write a marketing email" |
| Analysis | 📊 | Data analysis | "Analyze this sales data" |

---

## 🔧 API Configuration

### Gemini AI (Recommended)
```env
VITE_GEMINI_API_KEY=your_gemini_key
```
- Get key: [Google AI Studio](https://makersuite.google.com/app/apikey)
- Features: High quality, fast, cost-effective

### OpenAI (Alternative)
```env
VITE_OPENAI_API_KEY=your_openai_key
```
- Get key: [OpenAI Platform](https://platform.openai.com/api-keys)
- Features: GPT models, established ecosystem

### SerpAPI (Optional)
```env
VITE_SERPAPI_KEY=your_serpapi_key
```
- Get key: [SerpAPI](https://serpapi.com/)
- Features: Real web search results

---

## 🎨 Multi-Agent Task Examples

### Simple Tasks (Single Agent)
```javascript
// Research task
"Research the latest AI developments in healthcare"

// Coding task
"Create a user authentication system in React"

// Analysis task
"Analyze website traffic trends and provide insights"

// Creative task
"Write a compelling product description for an AI tool"
```

### Complex Tasks (Multi-Agent)
```javascript
// Business plan creation
"Create a comprehensive business plan for an AI startup including market research, financial projections, and technical architecture"

// Product launch campaign
"Develop a complete product launch strategy including market analysis, technical requirements, and marketing materials"

// Competitive analysis
"Conduct thorough competitive analysis with market positioning recommendations and technical differentiation strategy"
```

---

## 📊 Performance Metrics

### Expected Performance
| Metric | Target | Good | Needs Attention |
|--------|--------|------|-----------------|
| Response Time | < 3s | < 5s | > 10s |
| Success Rate | > 95% | > 90% | < 85% |
| Agent Availability | > 98% | > 95% | < 90% |
| Synthesis Quality | > 0.85 | > 0.80 | < 0.75 |

### Monitoring Commands
```javascript
// Get performance report
const metrics = agentPerformanceMonitoring.getPerformanceReport('5m')

// Check system health
const health = agentFramework.getFrameworkStats()

// View active agents
const agents = agentFramework.getActiveAgents()
```

---

## 🛠️ Troubleshooting

### Common Issues

| Issue | Symptoms | Solution |
|-------|----------|----------|
| No API Key | "No API key found" error | Add API key to .env file |
| Network Error | Requests failing | Check internet connection |
| Slow Performance | Long response times | Check system resources |
| Test Failures | Tests not passing | Try demo mode first |
| Browser Issues | UI not loading | Clear cache, refresh page |

### Debug Commands
```javascript
// Enable debug mode
localStorage.setItem('agi_debug', 'true')
location.reload()

// View debug logs
console.log(window.agiDebugLogs)

// Check system status
console.log(agentFramework.getSystemStatus())
```

---

## 🎯 Best Practices

### Task Design
- ✅ Be specific and clear
- ✅ Provide context and requirements
- ✅ Define success criteria
- ❌ Avoid vague or ambiguous requests

### Agent Selection
- ✅ Match agent expertise to task type
- ✅ Use multiple agents for complex tasks
- ✅ Consider task dependencies
- ❌ Don't overload single agents

### Performance Optimization
- ✅ Monitor response times
- ✅ Use caching when appropriate
- ✅ Parallelize independent tasks
- ❌ Don't run too many concurrent tasks

---

## 📚 Documentation Files

| File | Purpose | When to Use |
|------|---------|-------------|
| `AGI_PLAYGROUND_USER_MANUAL.md` | Complete user guide | Learning the system |
| `MULTI_AGENT_SYSTEM_GUIDE.md` | Technical deep dive | Understanding architecture |
| `TESTING_EXAMPLES_GUIDE.md` | Testing examples | Validating functionality |
| `QUICK_REFERENCE_GUIDE.md` | Quick reference | Daily usage |

---

## 🔗 Useful Links

### Development
- **Local Server:** http://localhost:5173/
- **Multi-Agent Tester:** http://localhost:5173/ → "Multi-Agent Tester"
- **AGI Playground:** http://localhost:5173/ → "AGI Playground"

### API Documentation
- **Gemini AI:** [Google AI Documentation](https://ai.google.dev/)
- **OpenAI:** [OpenAI API Reference](https://platform.openai.com/docs)
- **SerpAPI:** [SerpAPI Documentation](https://serpapi.com/search-api)

---

## ⚡ Quick Start Checklist

- [ ] Install dependencies (`npm install`)
- [ ] Configure API keys (`.env` file)
- [ ] Start development server (`npm run dev`)
- [ ] Open browser to http://localhost:5173/
- [ ] Navigate to "Multi-Agent Tester"
- [ ] Run "Quick Browser Test"
- [ ] Explore "AGI Playground" chat interface
- [ ] Try multi-agent collaboration tasks
- [ ] Review performance metrics
- [ ] Read full documentation for advanced features

---

## 🎉 Success Indicators

You know the system is working when:
- ✅ Browser tests pass with > 90% success rate
- ✅ Agents respond within 5 seconds
- ✅ Multi-agent tasks complete successfully
- ✅ Chat interface provides relevant responses
- ✅ Performance metrics show healthy status
- ✅ No error messages in console
- ✅ All navigation pages load correctly

---

*This quick reference provides essential information for daily use of the AGI Playground system.*
