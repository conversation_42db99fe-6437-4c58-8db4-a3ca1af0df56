<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔌 WebSocket Connection Test</h1>
        <p>This page tests WebSocket connectivity for Vite HMR (Hot Module Replacement)</p>
        
        <button class="test-button" onclick="testWebSocket()">Test WebSocket Connection</button>
        <button class="test-button" onclick="testViteHMR()">Test Vite HMR</button>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultElement = document.createElement('div');
            resultElement.className = `status ${type}`;
            resultElement.innerHTML = message;
            resultsDiv.appendChild(resultElement);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testWebSocket() {
            addResult('🔍 Testing WebSocket connection...', 'info');
            
            try {
                // Test basic WebSocket connection
                const ws = new WebSocket('ws://localhost:5173');
                
                ws.onopen = function(event) {
                    addResult('✅ WebSocket connection opened successfully!', 'success');
                    ws.close();
                };
                
                ws.onerror = function(error) {
                    addResult('❌ WebSocket connection failed: ' + error.message, 'error');
                };
                
                ws.onclose = function(event) {
                    if (event.wasClean) {
                        addResult('🔌 WebSocket connection closed cleanly', 'info');
                    } else {
                        addResult('⚠️ WebSocket connection closed unexpectedly (Code: ' + event.code + ')', 'error');
                    }
                };
                
                // Timeout after 5 seconds
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        addResult('⏰ WebSocket connection timeout', 'error');
                        ws.close();
                    }
                }, 5000);
                
            } catch (error) {
                addResult('❌ WebSocket test failed: ' + error.message, 'error');
            }
        }

        function testViteHMR() {
            addResult('🔍 Testing Vite HMR WebSocket...', 'info');
            
            try {
                // Test Vite HMR WebSocket connection
                const viteWs = new WebSocket('ws://localhost:5173/vite-hmr');
                
                viteWs.onopen = function(event) {
                    addResult('✅ Vite HMR WebSocket connected successfully!', 'success');
                    addResult('🔥 Hot Module Replacement should be working', 'success');
                    viteWs.close();
                };
                
                viteWs.onerror = function(error) {
                    addResult('❌ Vite HMR WebSocket failed: ' + error.message, 'error');
                    addResult('💡 Try refreshing the page or restarting the dev server', 'info');
                };
                
                viteWs.onclose = function(event) {
                    if (event.wasClean) {
                        addResult('🔌 Vite HMR WebSocket closed cleanly', 'info');
                    } else {
                        addResult('⚠️ Vite HMR WebSocket closed unexpectedly (Code: ' + event.code + ')', 'error');
                    }
                };
                
                // Timeout after 5 seconds
                setTimeout(() => {
                    if (viteWs.readyState === WebSocket.CONNECTING) {
                        addResult('⏰ Vite HMR WebSocket connection timeout', 'error');
                        viteWs.close();
                    }
                }, 5000);
                
            } catch (error) {
                addResult('❌ Vite HMR test failed: ' + error.message, 'error');
            }
        }

        // Auto-test on page load
        window.addEventListener('load', function() {
            addResult('🚀 WebSocket test page loaded', 'info');
            addResult('📡 Server should be running on http://localhost:5173', 'info');
            
            // Check if we're on the right port
            if (window.location.port !== '5173' && window.location.hostname === 'localhost') {
                addResult('⚠️ Warning: This page is not on port 5173. WebSocket tests may fail.', 'error');
                addResult('💡 Open this page at: http://localhost:5173/test-websocket.html', 'info');
            }
        });
    </script>
</body>
</html>
