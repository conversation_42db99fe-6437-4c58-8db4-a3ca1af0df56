AGI Playground - File Management Test
=====================================

This is a sample text file to test the file management functionality in the AGI Playground.

Features to test:
- File upload
- File viewing
- File editing
- File download
- File deletion

Sample data:
Name: <PERSON>
Age: 30
City: New York
Occupation: Software Developer

Sample JSON data:
{
  "name": "<PERSON>",
  "age": 25,
  "skills": ["JavaScript", "React", "Node.js"],
  "active": true
}

Sample CSV data:
Name,Age,City,Occupation
Alice,28,<PERSON>,<PERSON>,32,<PERSON>,<PERSON>,29,Tokyo,Analyst

This file can be used to test:
1. Upload functionality
2. Content viewing
3. Text editing
4. File operations

Last updated: 2025-01-24
