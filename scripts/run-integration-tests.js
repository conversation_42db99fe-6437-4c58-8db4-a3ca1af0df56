#!/usr/bin/env node
// Integration test runner for MCP agent system
import { IntegrationTests } from '../src/agents/tests/IntegrationTests.js'

async function main() {
  try {
    console.log('🚀 Starting MCP Agent Integration Tests...')
    console.log('Time:', new Date().toISOString())
    console.log('=' .repeat(60))

    const results = await IntegrationTests.runWeek1And2Tests()

    console.log('\n' + '=' .repeat(60))
    console.log('📊 Final Results:')
    
    if (results.summary) {
      console.log(`✅ Tests Passed: ${results.summary.passed}`)
      console.log(`❌ Tests Failed: ${results.summary.failed}`)
      console.log(`📈 Success Rate: ${results.summary.successRate}%`)
      console.log(`⏱️  Duration: ${Math.round(results.duration / 1000)}s`)

      if (results.summary.successRate >= 95) {
        console.log('\n🎉 INTEGRATION TESTS PASSED!')
        console.log('✅ Ready to proceed to next phase')
        process.exit(0)
      } else {
        console.log('\n❌ INTEGRATION TESTS FAILED!')
        console.log('🔧 Please fix failing tests before proceeding')
        process.exit(1)
      }
    } else {
      console.log('❌ Tests failed to complete')
      if (results.error) {
        console.log('Error:', results.error)
      }
      process.exit(1)
    }
  } catch (error) {
    console.error('💥 Test runner failed:', error)
    process.exit(1)
  }
}

main()
