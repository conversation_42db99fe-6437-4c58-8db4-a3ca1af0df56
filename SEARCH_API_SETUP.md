# Web Search API Setup Guide

The AGI Playground includes web search functionality that can use real search APIs for accurate, up-to-date results.

## Current Status

✅ **Working**: Simulated search results (fallback)  
⚠️ **Needs Setup**: Real search API integration

## Quick Setup Options

### Option 1: SerpAPI (Recommended)

SerpAPI provides Google search results via API.

1. **Get API Key**:
   - Visit [SerpAPI.com](https://serpapi.com)
   - Sign up for free account (100 searches/month free)
   - Get your API key from dashboard

2. **Configure Environment**:
   ```bash
   # Add to your .env file
   VITE_SERPAPI_KEY=your_serpapi_key_here
   ```

3. **Restart Development Server**:
   ```bash
   npm run dev
   ```

### Option 2: Google Custom Search API

1. **Setup Google Cloud Project**:
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Create new project or select existing
   - Enable Custom Search API

2. **Create Custom Search Engine**:
   - Visit [Google Custom Search](https://cse.google.com)
   - Create new search engine
   - Get Search Engine ID

3. **Get API Key**:
   - In Google Cloud Console, create API key
   - Restrict to Custom Search API

4. **Configure Environment**:
   ```bash
   # Add to your .env file
   VITE_GOOGLE_SEARCH_API_KEY=your_api_key
   VITE_GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id
   ```

## Testing Search Functionality

Once configured, test with queries like:
- "latest AI news today"
- "current weather in Paris"
- "what is happening in technology"

## Fallback Behavior

Without API keys, the system provides:
- ✅ Realistic simulated results
- ✅ Query-specific content
- ✅ Clear indication of simulation
- ✅ Instructions for real API setup

## Troubleshooting

### No Results Returned
- Check API key in .env file
- Verify API key is valid
- Check browser console for errors
- Ensure development server restarted after .env changes

### CORS Errors
- SerpAPI handles CORS automatically
- For other APIs, may need proxy setup

### Rate Limiting
- Free tiers have limited requests
- Implement caching for production use
- Consider upgrading API plan for heavy usage

## Implementation Details

The search system tries methods in this order:
1. **SerpAPI** (if key available) - Best results
2. **DuckDuckGo HTML** (CORS proxy) - Free but limited
3. **Simulated Results** (always works) - For demo/testing

## Production Recommendations

For production deployment:
- Use SerpAPI or Google Custom Search
- Implement result caching
- Add rate limiting
- Monitor API usage
- Consider multiple API fallbacks
