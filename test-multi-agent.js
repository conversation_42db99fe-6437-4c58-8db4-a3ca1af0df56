#!/usr/bin/env node

// Multi-Agent System Test Script
// Run this script to test the multi-agent collaboration system

import { runAllTests, runSpecificTestSuite, showTestCoverage, runSmokeTest } from './src/functions/testRunner.js'
import { runFullDemo, runQuickDemo } from './src/functions/demos/multiAgentDemo.js'

// Parse command line arguments
const args = process.argv.slice(2)
const command = args[0] || 'help'

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

// Colored console output
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  header: (msg) => console.log(`${colors.bright}${colors.cyan}🚀 ${msg}${colors.reset}`),
  separator: () => console.log(`${colors.cyan}${'='.repeat(60)}${colors.reset}`)
}

// Show help
function showHelp() {
  log.header('Multi-Agent System Test Runner')
  log.separator()
  console.log('')
  console.log('Usage: node test-multi-agent.js [command]')
  console.log('')
  console.log('Commands:')
  console.log('  help              Show this help message')
  console.log('  quick             Run quick demo')
  console.log('  demo              Run full demonstration')
  console.log('  smoke             Run smoke test')
  console.log('  test              Run all tests')
  console.log('  coverage          Show test coverage')
  console.log('')
  console.log('Test Suites:')
  console.log('  agents            Test agent framework')
  console.log('  specialized       Test specialized agents')
  console.log('  coordination      Test coordination system')
  console.log('  communication     Test communication protocol')
  console.log('  decomposition     Test task decomposition')
  console.log('  monitoring        Test performance monitoring')
  console.log('  synthesis         Test result synthesis')
  console.log('  integration       Test integration scenarios')
  console.log('')
  console.log('Examples:')
  console.log('  node test-multi-agent.js quick')
  console.log('  node test-multi-agent.js test')
  console.log('  node test-multi-agent.js agents')
  console.log('')
}

// Format duration
function formatDuration(ms) {
  if (ms < 1000) return `${ms}ms`
  if (ms < 60000) return `${Math.round(ms / 1000)}s`
  return `${Math.round(ms / 60000)}m ${Math.round((ms % 60000) / 1000)}s`
}

// Run command
async function runCommand(command) {
  const startTime = Date.now()
  
  try {
    switch (command) {
      case 'help':
      case '--help':
      case '-h':
        showHelp()
        break

      case 'quick':
        log.header('Running Quick Demo')
        log.separator()
        const quickResult = await runQuickDemo()
        if (quickResult) {
          log.success('Quick demo completed successfully!')
        } else {
          log.error('Quick demo failed')
          process.exit(1)
        }
        break

      case 'demo':
        log.header('Running Full Demonstration')
        log.separator()
        const demoResults = await runFullDemo()
        const successful = demoResults.filter(r => r.success).length
        const total = demoResults.length
        
        console.log('')
        log.info(`Demo Results: ${successful}/${total} successful`)
        
        if (successful === total) {
          log.success('All demonstrations completed successfully!')
        } else {
          log.warning(`${total - successful} demonstration(s) had issues`)
          demoResults.filter(r => !r.success).forEach(result => {
            log.error(`${result.name}: ${result.error}`)
          })
        }
        break

      case 'smoke':
        log.header('Running Smoke Test')
        log.separator()
        const smokeResult = await runSmokeTest()
        if (smokeResult) {
          log.success('Smoke test passed!')
        } else {
          log.error('Smoke test failed')
          process.exit(1)
        }
        break

      case 'test':
        log.header('Running Complete Test Suite')
        log.separator()
        const testResults = await runAllTests()
        
        console.log('')
        log.info(`Test Results: ${testResults.summary.totalPassed}/${testResults.summary.totalTests} passed`)
        log.info(`Success Rate: ${testResults.summary.successRate}%`)
        log.info(`Duration: ${formatDuration(testResults.summary.totalDuration)}`)
        
        if (testResults.summary.totalFailed === 0) {
          log.success('All tests passed!')
        } else {
          log.error(`${testResults.summary.totalFailed} test(s) failed`)
          testResults.failedTests.forEach(test => {
            log.error(`${test.suite} > ${test.test}: ${test.error}`)
          })
          process.exit(1)
        }
        break

      case 'coverage':
        log.header('Test Coverage Report')
        log.separator()
        const coverage = showTestCoverage()
        console.log('')
        log.success('Coverage report generated')
        break

      // Test suites
      case 'agents':
        await runTestSuite('agent_framework')
        break

      case 'specialized':
        await runTestSuite('specialized_agents')
        break

      case 'coordination':
        await runTestSuite('coordination_system')
        break

      case 'communication':
        await runTestSuite('communication_protocol')
        break

      case 'decomposition':
        await runTestSuite('task_decomposition')
        break

      case 'monitoring':
        await runTestSuite('performance_monitoring')
        break

      case 'synthesis':
        await runTestSuite('result_synthesis')
        break

      case 'integration':
        await runTestSuite('integration')
        break

      default:
        log.error(`Unknown command: ${command}`)
        console.log('')
        showHelp()
        process.exit(1)
    }

    const duration = Date.now() - startTime
    console.log('')
    log.info(`Completed in ${formatDuration(duration)}`)

  } catch (error) {
    log.error(`Command failed: ${error.message}`)
    console.error(error.stack)
    process.exit(1)
  }
}

// Run specific test suite
async function runTestSuite(suiteName) {
  log.header(`Running ${suiteName} Test Suite`)
  log.separator()
  
  const result = await runSpecificTestSuite(suiteName)
  
  console.log('')
  log.info(`Suite Results: ${result.passed}/${result.passed + result.failed} passed`)
  log.info(`Duration: ${formatDuration(result.duration)}`)
  
  if (result.failed === 0) {
    log.success(`${suiteName} tests passed!`)
  } else {
    log.error(`${result.failed} test(s) failed in ${suiteName}`)
    result.tests.filter(t => !t.success).forEach(test => {
      log.error(`${test.name}: ${test.error}`)
    })
    process.exit(1)
  }
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  log.error(`Uncaught exception: ${error.message}`)
  console.error(error.stack)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  log.error(`Unhandled rejection: ${reason}`)
  console.error(reason)
  process.exit(1)
})

// Run the command
runCommand(command)
