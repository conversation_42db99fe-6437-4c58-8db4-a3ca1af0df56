/*
Language: ERB (Embedded Ruby)
Requires: xml.js, ruby.js
Author: <PERSON> <luca<PERSON><PERSON><PERSON>@gmail.com>
Contributors: <PERSON><PERSON><PERSON> <<EMAIL>>
Description: "Bridge" language defining fragments of Ruby in HTML within <% .. %>
Website: https://ruby-doc.org/stdlib-2.6.5/libdoc/erb/rdoc/ERB.html
Category: template
*/

/** @type LanguageFn */
function erb(hljs) {
  return {
    name: 'ERB',
    subLanguage: 'xml',
    contains: [
      hljs.COMMENT('<%#', '%>'),
      {
        begin: '<%[%=-]?',
        end: '[%-]?%>',
        subLanguage: 'ruby',
        excludeBegin: true,
        excludeEnd: true
      }
    ]
  };
}

export { erb as default };
