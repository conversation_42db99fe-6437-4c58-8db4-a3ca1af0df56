pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: 3024
  Author: <PERSON> (http://github.com/idleberg)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme 3024
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #090300  Default Background
base01  #3a3432  Lighter Background (Used for status bars, line number and folding marks)
base02  #4a4543  Selection Background
base03  #5c5855  Comments, Invisibles, Line Highlighting
base04  #807d7c  Dark Foreground (Used for status bars)
base05  #a5a2a2  Default Foreground, Caret, Delimiters, Operators
base06  #d6d5d4  Light Foreground (Not often used)
base07  #f7f7f7  Light Background (Not often used)
base08  #db2d20  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #e8bbd0  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #fded02  Classes, Markup Bold, Search Text Background
base0B  #01a252  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #b5e4f4  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #01a0e4  Functions, Methods, Attribute IDs, Headings
base0E  #a16a94  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #cdab53  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #a5a2a2;
  background: #090300
}
.hljs::selection,
.hljs ::selection {
  background-color: #4a4543;
  color: #a5a2a2
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #5c5855 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #5c5855
}
/* base04 - #807d7c -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #807d7c
}
/* base05 - #a5a2a2 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #a5a2a2
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #db2d20
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #e8bbd0
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #fded02
}
.hljs-strong {
  font-weight: bold;
  color: #fded02
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #01a252
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #b5e4f4
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #01a0e4
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #a16a94
}
.hljs-emphasis {
  color: #a16a94;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #cdab53
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}