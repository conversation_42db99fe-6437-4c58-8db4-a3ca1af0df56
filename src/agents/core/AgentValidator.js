// AgentValidator - Validates agent configurations using JSON schemas
import Ajv from 'ajv'
import addFormats from 'ajv-formats'
import fs from 'fs/promises'
import path from 'path'

export class AgentValidator {
  constructor() {
    this.ajv = new Ajv({ allErrors: true, verbose: true })
    addFormats(this.ajv)
    this.schemas = new Map()
    this.initialized = false
  }

  async initialize() {
    if (this.initialized) return

    try {
      const schemaDir = path.join(process.cwd(), 'src/agents/schemas')
      const agentSchema = await this.loadSchema(schemaDir, 'agent-config.schema.json')
      const toolSchema = await this.loadSchema(schemaDir, 'tool-spec.schema.json')

      this.schemas.set('agent-config', agentSchema)
      this.schemas.set('tool-spec', toolSchema)

      this.ajv.addSchema(agentSchema, 'agent-config')
      this.ajv.addSchema(toolSchema, 'tool-spec')

      this.initialized = true
    } catch (error) {
      throw new Error(`Validator initialization failed: ${error.message}`)
    }
  }

  async loadSchema(schemaDir, filename) {
    const schemaPath = path.join(schemaDir, filename)
    const schemaContent = await fs.readFile(schemaPath, 'utf8')
    return JSON.parse(schemaContent)
  }

  static async validate(data, schemaName) {
    const validator = new AgentValidator()
    await validator.initialize()
    return validator.validateData(data, schemaName)
  }

  validateData(data, schemaName) {
    if (!this.initialized) {
      throw new Error('Validator not initialized')
    }

    const validate = this.ajv.getSchema(schemaName)
    if (!validate) {
      throw new Error(`Schema not found: ${schemaName}`)
    }

    const valid = validate(data)
    if (!valid) {
      const errors = validate.errors.map(error => ({
        path: error.instancePath || error.schemaPath,
        message: error.message,
        value: error.data
      }))

      throw new ValidationError(`Validation failed for ${schemaName}`, errors)
    }

    return data
  }
}

export class ValidationError extends Error {
  constructor(message, errors = []) {
    super(message)
    this.name = 'ValidationError'
    this.errors = errors
  }
}
