// BaseAgent - Foundation class for all agent types
import { AgentValidator } from './AgentValidator.js'
import { PerformanceTracker } from './PerformanceTracker.js'

export class BaseAgent {
  constructor(config, mcpClient = null) {
    this.config = config
    this.mcpClient = mcpClient
    this.tools = new Map()
    this.performance = new PerformanceTracker()
    this.id = this.generateId()
    this.status = 'initialized'
    this.capabilities = config?.agent?.capabilities || []
  }

  generateId() {
    const name = this.config?.agent?.metadata?.name || 'unknown'
    const timestamp = Date.now()
    const random = Math.random().toString(36).substr(2, 9)
    return `${name}_${timestamp}_${random}`
  }

  async initialize() {
    try {
      this.performance.start()
      this.status = 'ready'
      return { success: true, agentId: this.id }
    } catch (error) {
      this.status = 'error'
      throw new Error(`Agent initialization failed: ${error.message}`)
    }
  }

  async executeTask(task) {
    if (this.status !== 'ready') {
      throw new Error(`Agent not ready. Current status: ${this.status}`)
    }

    const startTime = Date.now()
    this.status = 'executing'

    try {
      // Basic input validation
      if (!task || typeof task !== 'object') {
        throw new Error('Invalid task: must be an object')
      }

      if (!task.description || typeof task.description !== 'string') {
        throw new Error('Invalid task: description is required')
      }

      // Call the abstract method that subclasses must implement
      const result = await this.processTask(task)

      // Record successful execution
      const duration = Date.now() - startTime
      this.performance.recordExecution(duration, true, { taskType: task.type })

      this.status = 'ready'
      return result
    } catch (error) {
      // Record failed execution
      const duration = Date.now() - startTime
      this.performance.recordExecution(duration, false, {
        taskType: task.type,
        error: error.message
      })

      this.status = 'error'
      throw error
    }
  }

  // Abstract method - must be implemented by subclasses
  async processTask(task) {
    throw new Error('processTask must be implemented by subclass')
  }

  getStatus() {
    return {
      id: this.id,
      status: this.status,
      capabilities: this.capabilities,
      performance: this.performance.getStats(),
      toolsLoaded: Array.from(this.tools.keys()),
      lastActivity: this.performance.getLastActivity()
    }
  }

  hasCapability(capability) {
    return this.capabilities.includes(capability)
  }

  static getSupportedCapabilities() {
    // To be overridden by subclasses
    return []
  }
}
