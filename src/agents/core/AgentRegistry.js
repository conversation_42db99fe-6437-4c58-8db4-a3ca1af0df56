// AgentRegistry - Manages agent instances and types
export class AgentRegistry {
  constructor() {
    this.agents = new Map()
    this.agentTypes = new Map()
    this.initialized = false
  }

  async initialize() {
    if (this.initialized) return

    // Register built-in agent types
    await this.registerBuiltInAgents()
    this.initialized = true
  }

  async registerBuiltInAgents() {
    // Register test agent
    try {
      const { TestAgent } = await import('../types/test/TestAgent.js')
      this.registerAgentType('test', TestAgent)
      console.log('✅ Registered test agent type')
    } catch (error) {
      console.warn('Failed to register test agent type:', error.message)
    }
  }

  registerAgentType(typeName, agentClass) {
    if (this.agentTypes.has(typeName)) {
      throw new Error(`Agent type ${typeName} is already registered`)
    }

    // Validate that the class extends BaseAgent
    if (!this.isValidAgentClass(agentClass)) {
      throw new Error(`Invalid agent class for type ${typeName}`)
    }

    this.agentTypes.set(typeName, agentClass)
  }

  isValidAgentClass(agentClass) {
    // Check if class has required methods
    const prototype = agentClass.prototype
    return (
      typeof prototype.processTask === 'function' &&
      typeof prototype.initialize === 'function' &&
      typeof prototype.getStatus === 'function'
    )
  }

  async createAgent(typeName, config = null, mcpClient = null) {
    if (!this.agentTypes.has(typeName)) {
      throw new Error(`Unknown agent type: ${typeName}`)
    }

    // Load config if not provided
    if (!config) {
      const { ConfigLoader } = await import('./ConfigLoader.js')
      config = await ConfigLoader.loadAgentConfig(typeName)
    }

    const AgentClass = this.agentTypes.get(typeName)
    const agent = new AgentClass(config, mcpClient)

    // Initialize the agent
    await agent.initialize()

    // Register the instance
    this.agents.set(agent.id, {
      agent,
      type: typeName,
      createdAt: new Date(),
      lastUsed: new Date()
    })

    return agent
  }

  getAgent(agentId) {
    const agentInfo = this.agents.get(agentId)
    return agentInfo ? agentInfo.agent : null
  }

  getAllAgents() {
    return Array.from(this.agents.values()).map(info => info.agent)
  }

  getAgentsByType(typeName) {
    return Array.from(this.agents.values())
      .filter(info => info.type === typeName)
      .map(info => info.agent)
  }

  removeAgent(agentId) {
    return this.agents.delete(agentId)
  }

  getRegisteredTypes() {
    return Array.from(this.agentTypes.keys())
  }

  getStats() {
    const typeStats = {}

    for (const [agentId, info] of this.agents) {
      const type = info.type
      if (!typeStats[type]) {
        typeStats[type] = { count: 0, active: 0 }
      }
      typeStats[type].count++

      if (info.agent.status === 'ready') {
        typeStats[type].active++
      }
    }

    return {
      totalAgents: this.agents.size,
      registeredTypes: this.agentTypes.size,
      typeStats
    }
  }
}

// Create singleton instance
export const agentRegistry = new AgentRegistry()
