// ConfigLoader - Loads and validates agent configurations
import yaml from 'js-yaml'
import fs from 'fs/promises'
import path from 'path'
import { AgentValidator } from './AgentValidator.js'

export class ConfigLoader {
  static async loadAgentConfig(agentType) {
    const configPath = path.join(
      process.cwd(),
      'src/agents/types',
      agentType,
      'config.yaml'
    )

    try {
      const configContent = await fs.readFile(configPath, 'utf8')
      const config = yaml.load(configContent)

      // Validate configuration
      await AgentValidator.validate(config, 'agent-config')

      return config
    } catch (error) {
      throw new Error(`Failed to load config for ${agentType}: ${error.message}`)
    }
  }

  static async getAllAgentConfigs() {
    const agentTypes = ['research', 'coding', 'analysis', 'creative', 'coordinator', 'test']
    const configs = {}

    for (const type of agentTypes) {
      try {
        configs[type] = await this.loadAgentConfig(type)
      } catch (error) {
        console.warn(`Failed to load config for ${type}:`, error.message)
      }
    }

    return configs
  }

  static async validateConfig(config) {
    return await AgentValidator.validate(config, 'agent-config')
  }

  static async loadToolSpec(toolName, version = 'latest') {
    // This would load tool specifications from a registry
    // For now, return a mock tool spec
    return {
      name: toolName,
      version: version === 'latest' ? '1.0.0' : version,
      description: `Mock tool specification for ${toolName}`,
      capabilities: ['general'],
      transport: 'stdio'
    }
  }

  static async validateToolSpec(toolSpec) {
    return await AgentValidator.validate(toolSpec, 'tool-spec')
  }

  static getAvailableAgentTypes() {
    return ['research', 'coding', 'analysis', 'creative', 'coordinator', 'test']
  }

  static async configExists(agentType) {
    const configPath = path.join(
      process.cwd(),
      'src/agents/types',
      agentType,
      'config.yaml'
    )

    try {
      await fs.access(configPath)
      return true
    } catch {
      return false
    }
  }
}
