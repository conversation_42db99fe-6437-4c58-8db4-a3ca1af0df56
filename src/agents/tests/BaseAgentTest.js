// BaseAgentTest - Basic test suite for BaseAgent functionality
import { TestAgent } from '../types/test/TestAgent.js'
import { ConfigLoader } from '../core/ConfigLoader.js'

export class BaseAgentTest {
  static async runBasicTests() {
    const results = []

    try {
      // Test 1: Agent creation
      const config = await ConfigLoader.loadAgentConfig('test')
      const agent = new TestAgent(config)
      results.push({ test: 'agent_creation', success: true })

      // Test 2: Agent initialization
      await agent.initialize()
      results.push({ test: 'agent_initialization', success: true })

      // Test 3: Task execution
      const task = {
        description: 'Test task for validation',
        requirements: { test: true }
      }
      const result = await agent.executeTask(task)
      results.push({
        test: 'task_execution',
        success: result.success,
        details: result
      })

      // Test 4: Status reporting
      const status = agent.getStatus()
      results.push({
        test: 'status_reporting',
        success: status.id === agent.id,
        details: status
      })

      // Test 5: Performance tracking
      const stats = agent.performance.getStats()
      results.push({
        test: 'performance_tracking',
        success: stats.totalExecutions >= 1,
        details: stats
      })

    } catch (error) {
      results.push({
        test: 'error_occurred',
        success: false,
        error: error.message
      })
    }

    return results
  }

  static async runErrorHandlingTests() {
    const results = []

    try {
      const config = await ConfigLoader.loadAgentConfig('test')
      const agent = new TestAgent(config)
      await agent.initialize()

      // Test invalid task
      try {
        await agent.executeTask(null)
        results.push({ test: 'null_task_handling', success: false, error: 'Should have thrown error' })
      } catch (error) {
        results.push({ test: 'null_task_handling', success: true, details: error.message })
      }

      // Test empty task
      try {
        await agent.executeTask({})
        results.push({ test: 'empty_task_handling', success: false, error: 'Should have thrown error' })
      } catch (error) {
        results.push({ test: 'empty_task_handling', success: true, details: error.message })
      }

    } catch (error) {
      results.push({
        test: 'error_test_setup_failed',
        success: false,
        error: error.message
      })
    }

    return results
  }

  static async runAllTests() {
    console.log('🧪 Running BaseAgent tests...')
    
    const basicTests = await this.runBasicTests()
    const errorTests = await this.runErrorHandlingTests()
    
    const allResults = [...basicTests, ...errorTests]
    const passed = allResults.filter(r => r.success).length
    const total = allResults.length
    
    console.log(`✅ Tests passed: ${passed}/${total}`)
    
    return {
      passed,
      total,
      successRate: (passed / total) * 100,
      results: allResults
    }
  }
}
