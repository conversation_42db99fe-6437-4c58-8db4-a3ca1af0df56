// AgentTestFramework - Comprehensive testing framework for agents
export class AgentTestFramework {
  constructor() {
    this.testResults = []
    this.currentSuite = null
  }

  async runAgentTests(agentType) {
    this.currentSuite = {
      name: `${agentType}_agent_tests`,
      startTime: Date.now(),
      tests: [],
      passed: 0,
      failed: 0
    }

    console.log(`🧪 Running tests for ${agentType} agent...`)

    // Run test suite
    await this.testAgentCreation(agentType)
    await this.testAgentInitialization(agentType)
    await this.testTaskExecution(agentType)
    await this.testErrorHandling(agentType)
    await this.testPerformanceTracking(agentType)

    // Finalize suite
    this.currentSuite.endTime = Date.now()
    this.currentSuite.duration = this.currentSuite.endTime - this.currentSuite.startTime
    this.testResults.push(this.currentSuite)

    return this.currentSuite
  }

  async testAgentCreation(agentType) {
    await this.runTest('agent_creation', async () => {
      const { agentRegistry } = await import('../core/AgentRegistry.js')
      await agentRegistry.initialize()

      const agent = await agentRegistry.createAgent(agentType)

      if (!agent || !agent.id) {
        throw new Error('Agent creation failed - no agent or ID returned')
      }

      if (agent.status !== 'ready') {
        throw new Error(`Agent not ready after creation. Status: ${agent.status}`)
      }

      return { agentId: agent.id, status: agent.status }
    })
  }

  async testAgentInitialization(agentType) {
    await this.runTest('agent_initialization', async () => {
      const { agentRegistry } = await import('../core/AgentRegistry.js')
      const agent = await agentRegistry.createAgent(agentType)

      const status = agent.getStatus()

      if (!status.id || !status.capabilities || !Array.isArray(status.capabilities)) {
        throw new Error('Agent status incomplete after initialization')
      }

      return status
    })
  }

  async testTaskExecution(agentType) {
    await this.runTest('task_execution', async () => {
      const { agentRegistry } = await import('../core/AgentRegistry.js')
      const agent = await agentRegistry.createAgent(agentType)

      const testTask = {
        description: `Test task for ${agentType} agent`,
        requirements: { test: true, agentType }
      }

      const result = await agent.executeTask(testTask)

      if (!result || typeof result.success !== 'boolean') {
        throw new Error('Invalid task execution result')
      }

      return result
    })
  }

  async testErrorHandling(agentType) {
    await this.runTest('error_handling', async () => {
      const { agentRegistry } = await import('../core/AgentRegistry.js')
      const agent = await agentRegistry.createAgent(agentType)

      // Test invalid task
      try {
        await agent.executeTask(null)
        throw new Error('Should have thrown error for null task')
      } catch (error) {
        if (!error.message.includes('Invalid task')) {
          throw new Error('Wrong error message for invalid task')
        }
      }

      // Test empty task
      try {
        await agent.executeTask({})
        throw new Error('Should have thrown error for empty task')
      } catch (error) {
        if (!error.message.includes('description is required')) {
          throw new Error('Wrong error message for empty task')
        }
      }

      return { errorHandlingWorking: true }
    })
  }

  async testPerformanceTracking(agentType) {
    await this.runTest('performance_tracking', async () => {
      const { agentRegistry } = await import('../core/AgentRegistry.js')
      const agent = await agentRegistry.createAgent(agentType)

      // Execute a few tasks to generate performance data
      for (let i = 0; i < 3; i++) {
        await agent.executeTask({
          description: `Performance test task ${i + 1}`,
          requirements: { test: true }
        })
      }

      const stats = agent.performance.getStats()

      if (stats.totalExecutions !== 3) {
        throw new Error(`Expected 3 executions, got ${stats.totalExecutions}`)
      }

      if (stats.successRate !== 1) {
        throw new Error(`Expected 100% success rate, got ${stats.successRate}`)
      }

      return stats
    })
  }

  async runTest(testName, testFunction) {
    const test = {
      name: testName,
      startTime: Date.now(),
      success: false,
      error: null,
      result: null
    }

    try {
      test.result = await testFunction()
      test.success = true
      this.currentSuite.passed++
      console.log(`  ✅ ${testName}`)
    } catch (error) {
      test.error = error.message
      test.success = false
      this.currentSuite.failed++
      console.log(`  ❌ ${testName}: ${error.message}`)
    }

    test.endTime = Date.now()
    test.duration = test.endTime - test.startTime
    this.currentSuite.tests.push(test)
  }

  generateReport() {
    const totalTests = this.testResults.reduce((sum, suite) => sum + suite.tests.length, 0)
    const totalPassed = this.testResults.reduce((sum, suite) => sum + suite.passed, 0)
    const totalFailed = this.testResults.reduce((sum, suite) => sum + suite.failed, 0)

    return {
      summary: {
        totalSuites: this.testResults.length,
        totalTests,
        totalPassed,
        totalFailed,
        successRate: totalTests > 0 ? (totalPassed / totalTests) * 100 : 0
      },
      suites: this.testResults,
      failedTests: this.testResults.flatMap(suite =>
        suite.tests.filter(test => !test.success)
      )
    }
  }
}
