// IntegrationTests - Comprehensive integration testing for Week 1-2
import { AgentTestFramework } from './AgentTestFramework.js'
import { agentRegistry } from '../core/AgentRegistry.js'
import { BaseAgentTest } from './BaseAgentTest.js'

export class IntegrationTests {
  static async runWeek1And2Tests() {
    console.log('🚀 Running Week 1-2 Integration Tests...')
    console.log('=' .repeat(50))

    const results = {
      startTime: Date.now(),
      tests: [],
      summary: null
    }

    try {
      // Test 1: Basic agent functionality
      console.log('\n📋 Testing basic agent functionality...')
      const basicTests = await BaseAgentTest.runAllTests()
      results.tests.push({ name: 'basic_functionality', results: basicTests })

      // Test 2: Agent registry functionality
      console.log('\n📋 Testing agent registry...')
      const registryTests = await this.testAgentRegistry()
      results.tests.push({ name: 'agent_registry', results: registryTests })

      // Test 3: Configuration system
      console.log('\n📋 Testing configuration system...')
      const configTests = await this.testConfigurationSystem()
      results.tests.push({ name: 'configuration_system', results: configTests })

      // Test 4: Performance tracking
      console.log('\n📋 Testing performance tracking...')
      const performanceTests = await this.testPerformanceSystem()
      results.tests.push({ name: 'performance_tracking', results: performanceTests })

      // Test 5: Comprehensive agent tests
      console.log('\n📋 Running comprehensive agent tests...')
      const framework = new AgentTestFramework()
      const agentTests = await framework.runAgentTests('test')
      results.tests.push({ name: 'comprehensive_agent_tests', results: agentTests })

      results.endTime = Date.now()
      results.duration = results.endTime - results.startTime
      results.summary = this.generateSummary(results)

      console.log('\n📊 Integration Test Summary:')
      console.log(`Total Duration: ${Math.round(results.duration / 1000)}s`)
      console.log(`Tests Passed: ${results.summary.passed}/${results.summary.total}`)
      console.log(`Success Rate: ${results.summary.successRate}%`)

      if (results.summary.successRate >= 95) {
        console.log('✅ Week 1-2 integration tests PASSED!')
        console.log('🎉 Ready to proceed to Week 3-4: MCP Tools Integration')
      } else {
        console.log('❌ Week 1-2 integration tests FAILED!')
        console.log('🔧 Please fix failing tests before proceeding')
      }

      return results

    } catch (error) {
      console.error('❌ Integration tests failed:', error)
      results.error = error.message
      return results
    }
  }

  static async testAgentRegistry() {
    const tests = []

    try {
      // Initialize registry
      await agentRegistry.initialize()
      tests.push({ test: 'registry_initialization', success: true })

      // Test agent creation
      const agent = await agentRegistry.createAgent('test')
      tests.push({ test: 'agent_creation_via_registry', success: !!agent })

      // Test agent retrieval
      const retrievedAgent = agentRegistry.getAgent(agent.id)
      tests.push({ test: 'agent_retrieval', success: retrievedAgent === agent })

      // Test stats
      const stats = agentRegistry.getStats()
      tests.push({ test: 'registry_stats', success: stats.totalAgents >= 1 })

    } catch (error) {
      tests.push({ test: 'registry_error', success: false, error: error.message })
    }

    return tests
  }

  static async testConfigurationSystem() {
    const tests = []

    try {
      const { ConfigLoader } = await import('../core/ConfigLoader.js')

      // Test loading test agent config
      const config = await ConfigLoader.loadAgentConfig('test')
      tests.push({ test: 'config_loading', success: !!config })

      // Test config validation
      const { AgentValidator } = await import('../core/AgentValidator.js')
      await AgentValidator.validate(config, 'agent-config')
      tests.push({ test: 'config_validation', success: true })

    } catch (error) {
      tests.push({ test: 'config_error', success: false, error: error.message })
    }

    return tests
  }

  static async testPerformanceSystem() {
    const tests = []

    try {
      const { PerformanceTracker } = await import('../core/PerformanceTracker.js')

      const tracker = new PerformanceTracker()
      tracker.start()

      // Record some test executions
      tracker.recordExecution(100, true)
      tracker.recordExecution(150, true)
      tracker.recordExecution(200, false)

      const stats = tracker.getStats()

      tests.push({ test: 'performance_tracking', success: stats.totalExecutions === 3 })
      tests.push({ test: 'success_rate_calculation', success: Math.abs(stats.successRate - 0.667) < 0.01 })
      tests.push({ test: 'average_duration', success: stats.averageDuration === 150 })

    } catch (error) {
      tests.push({ test: 'performance_error', success: false, error: error.message })
    }

    return tests
  }

  static generateSummary(results) {
    let totalTests = 0
    let passedTests = 0

    for (const testGroup of results.tests) {
      if (Array.isArray(testGroup.results)) {
        totalTests += testGroup.results.length
        passedTests += testGroup.results.filter(t => t.success).length
      } else if (testGroup.results.tests) {
        totalTests += testGroup.results.tests.length
        passedTests += testGroup.results.passed
      } else if (testGroup.results.total) {
        totalTests += testGroup.results.total
        passedTests += testGroup.results.passed
      }
    }

    return {
      total: totalTests,
      passed: passedTests,
      failed: totalTests - passedTests,
      successRate: totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0
    }
  }
}
