agent:
  metadata:
    name: "creative-agent"
    version: "2.0.0"
    description: "Advanced creative agent with content generation and image processing capabilities"
    author: "AGI Hub Team"
    license: "MIT"
    tags: ["creative", "content", "images"]

  capabilities:
    - content_generation
    - image_processing

  mcp_tools:
    required:
      - name: "content-generator"
        version: ">=1.0.0"
        config:
          max_length: 10000
          timeout: 60000
    optional:
      - name: "image-processor"
        version: ">=1.0.0"
        fallback: "basic_image_operations"

  performance:
    max_execution_time: 180000
    max_memory_usage: "1GB"
    max_concurrent_tasks: 3
    rate_limits:
      api_calls_per_minute: 50
