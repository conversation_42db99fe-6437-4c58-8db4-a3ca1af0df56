agent:
  metadata:
    name: "coordinator-agent"
    version: "2.0.0"
    description: "Advanced coordinator agent with workflow management and task coordination capabilities"
    author: "AGI Hub Team"
    license: "MIT"
    tags: ["coordination", "workflow", "management"]

  capabilities:
    - workflow_management
    - task_coordination

  mcp_tools:
    required:
      - name: "workflow-manager"
        version: ">=1.0.0"
        config:
          max_workflows: 10
          timeout: 120000
    optional:
      - name: "task-scheduler"
        version: ">=1.0.0"
        fallback: "basic_scheduling"

  performance:
    max_execution_time: 240000
    max_memory_usage: "512MB"
    max_concurrent_tasks: 5
    rate_limits:
      api_calls_per_minute: 100
