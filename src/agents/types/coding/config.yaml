agent:
  metadata:
    name: "coding-agent"
    version: "2.0.0"
    description: "Advanced coding agent with code execution and git management capabilities"
    author: "AGI Hub Team"
    license: "MIT"
    tags: ["coding", "development", "git"]

  capabilities:
    - code_execution
    - code_analysis
    - git_management

  mcp_tools:
    required:
      - name: "code-executor"
        version: ">=1.0.0"
        config:
          timeout: 60000
          sandbox: true
    optional:
      - name: "git-manager"
        version: ">=1.0.0"
        fallback: "basic_git_operations"

  performance:
    max_execution_time: 600000
    max_memory_usage: "1GB"
    max_concurrent_tasks: 2
    rate_limits:
      api_calls_per_minute: 30
