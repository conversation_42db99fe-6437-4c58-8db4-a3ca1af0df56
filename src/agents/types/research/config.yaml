agent:
  metadata:
    name: "research-agent"
    version: "2.0.0"
    description: "Advanced research agent with web search and document analysis capabilities"
    author: "AGI Hub Team"
    license: "MIT"
    tags: ["research", "web-search", "analysis"]

  capabilities:
    - web_search
    - document_analysis
    - fact_checking
    - source_verification

  mcp_tools:
    required:
      - name: "web-search"
        version: ">=1.0.0"
        config:
          max_results: 20
          timeout: 30000
    optional:
      - name: "fact-checker"
        version: ">=1.0.0"
        fallback: "internal_verification"

  performance:
    max_execution_time: 300000
    max_memory_usage: "512MB"
    max_concurrent_tasks: 3
    rate_limits:
      api_calls_per_minute: 60
