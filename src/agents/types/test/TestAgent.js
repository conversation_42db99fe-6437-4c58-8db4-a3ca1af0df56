// TestAgent - Simple test agent for validating BaseAgent functionality
import { BaseAgent } from '../../core/BaseAgent.js'

export class TestAgent extends BaseAgent {
  constructor(config, mcpClient) {
    super(config, mcpClient)
  }

  async processTask(task) {
    // Simple test implementation
    const { description, requirements = {} } = task

    // Simulate some processing time
    await new Promise(resolve => setTimeout(resolve, 100))

    return {
      success: true,
      content: `Test agent processed: ${description}`,
      agent_id: this.id,
      processing_time: 100,
      requirements_received: requirements
    }
  }

  static getSupportedCapabilities() {
    return ['general']
  }
}
