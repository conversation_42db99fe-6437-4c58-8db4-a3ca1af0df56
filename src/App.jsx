import { useState, useEffect } from 'react'
import { But<PERSON> } from './components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './components/ui/card'
import { Badge } from './components/ui/badge'
import { Input } from './components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './components/ui/select'
import { Brain, Zap, Search, Star, Clock, Users, TrendingUp, Sparkles, ShoppingCart, BookOpen, Play, Settings, ChevronRight, Globe, Cpu, Network, Target, Rocket, Menu } from 'lucide-react'
import AGIPlayground from './functions/components/AGIPlayground'
import MultiAgentTester from './functions/components/MultiAgentTester'
import './App.css'

function App() {
  const [currentPage, setCurrentPage] = useState('home')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [autoUpdate, setAutoUpdate] = useState(false)
  const [lastUpdate, setLastUpdate] = useState(new Date())
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  // États pour le Blog AGI
  const [blogPosts, setBlogPosts] = useState([])
  const [selectedPost, setSelectedPost] = useState(null)
  const [isGeneratingPost, setIsGeneratingPost] = useState(false)
  
  // États pour la Marketplace
  const [marketplaceItems, setMarketplaceItems] = useState([])
  const [marketplaceFilter, setMarketplaceFilter] = useState('all')

  // Données simulées pour les systèmes AGI
  const [agiSystems] = useState([
    {
      id: 1,
      name: 'Neural Reasoning Engine',
      status: 'Active',
      category: 'Research',
      description: 'Système AGI avancé capable de raisonnement logique et d\'apprentissage adaptatif',
      operations: 1247,
      efficiency: 94,
      capabilities: ['Logical Reasoning', 'Pattern Recognition'],
      lastUpdate: '2 hours ago'
    },
    {
      id: 2,
      name: 'Autonomous Problem Solver',
      status: 'Active',
      category: 'Development',
      description: 'AGI spécialisé dans la résolution autonome de problèmes complexes',
      operations: 858,
      efficiency: 89,
      capabilities: ['Complex Problem Solving', 'Multi-domain Analysis'],
      lastUpdate: '1 hour ago'
    },
    {
      id: 3,
      name: 'Creative Intelligence System',
      status: 'Active',
      category: 'Content',
      description: 'Système AGI focalisé sur la créativité et l\'innovation',
      operations: 623,
      efficiency: 87,
      capabilities: ['Creative Generation', 'Artistic Synthesis'],
      lastUpdate: '3 hours ago'
    },
    {
      id: 4,
      name: 'Multi-Modal Perception AGI',
      status: 'Archived',
      category: 'Research',
      description: 'AGI avec capacités de perception et compréhension multi-modales',
      operations: 342,
      efficiency: 76,
      capabilities: ['Visual Processing', 'Audio Analysis'],
      lastUpdate: '1 day ago'
    },
    {
      id: 5,
      name: 'Collaborative Intelligence Hub',
      status: 'Active',
      category: 'Development',
      description: 'Système AGI conçu pour la collaboration avec humains et autres IA',
      operations: 1089,
      efficiency: 92,
      capabilities: ['Human-AI Collaboration', 'Multi-Agent Coordination'],
      lastUpdate: '30 minutes ago'
    },
    {
      id: 6,
      name: 'Adaptive Learning Core',
      status: 'Active',
      category: 'Research',
      description: 'AGI avec capacités d\'apprentissage continu et d\'adaptation',
      operations: 734,
      efficiency: 88,
      capabilities: ['Continuous Learning', 'Knowledge Transfer'],
      lastUpdate: '45 minutes ago'
    },
    {
      id: 7,
      name: 'OpenAGI Reasoning Module',
      status: 'Active',
      category: 'Research',
      description: 'External AGI system from OpenAGI platform with advanced reasoning capabilities',
      operations: 1500,
      efficiency: 96,
      capabilities: ['Advanced Reasoning', 'Multi-step Planning'],
      lastUpdate: '15 minutes ago'
    },
    {
      id: 8,
      name: 'SuperAGI Collaborative Agent',
      status: 'Active',
      category: 'Development',
      description: 'Multi-agent system from SuperAGI framework for team coordination',
      operations: 1800,
      efficiency: 91,
      capabilities: ['Team Coordination', 'Task Distribution'],
      lastUpdate: '5 minutes ago'
    }
  ])

  // Fonctions pour le Blog AGI
  const generateBlogPost = async () => {
    setIsGeneratingPost(true)
    
    // Simulation de génération d'article
    setTimeout(() => {
      const newPost = {
        id: Date.now(),
        title: `Les Dernières Avancées en AGI - ${new Date().toLocaleDateString()}`,
        excerpt: 'Découvrez les développements révolutionnaires dans le domaine de l\'Intelligence Artificielle Générale...',
        content: `# Les Dernières Avancées en AGI

L'Intelligence Artificielle Générale (AGI) continue de progresser à un rythme effréné. Aujourd'hui, nous explorons les dernières innovations qui façonnent l'avenir de cette technologie révolutionnaire.

## Nouveaux Modèles de Raisonnement

Les récents développements dans les architectures neuronales permettent désormais aux systèmes AGI de...

## Impact sur l'Innovation

Ces avancées ouvrent de nouvelles perspectives pour l'humanité...`,
        author: 'AGI Hub AI',
        publishedAt: new Date(),
        tags: ['AGI', 'Innovation', 'Technologie', 'Avenir'],
        readTime: '5 min',
        views: Math.floor(Math.random() * 1000) + 100
      }
      
      setBlogPosts(prev => [newPost, ...prev])
      setIsGeneratingPost(false)
    }, 3000)
  }

  // Initialisation des articles de blog
  useEffect(() => {
    const initialPosts = [
      {
        id: 1,
        title: 'L\'Avenir de l\'AGI : Perspectives 2025',
        excerpt: 'Une analyse approfondie des tendances émergentes en Intelligence Artificielle Générale...',
        content: '# L\'Avenir de l\'AGI : Perspectives 2025\n\nL\'année 2025 marque un tournant décisif...',
        author: 'AGI Hub AI',
        publishedAt: new Date(Date.now() - 86400000),
        tags: ['AGI', 'Futur', 'Prédictions'],
        readTime: '8 min',
        views: 1247
      },
      {
        id: 2,
        title: 'Éthique et AGI : Défis et Solutions',
        excerpt: 'Comment naviguer les questions éthiques complexes liées au développement de l\'AGI...',
        content: '# Éthique et AGI : Défis et Solutions\n\nLe développement responsable de l\'AGI...',
        author: 'AGI Hub AI',
        publishedAt: new Date(Date.now() - 172800000),
        tags: ['Éthique', 'AGI', 'Responsabilité'],
        readTime: '6 min',
        views: 892
      }
    ]
    setBlogPosts(initialPosts)
  }, [])

  // Initialisation de la Marketplace
  useEffect(() => {
    const marketplaceData = [
      {
        id: 1,
        name: 'OpenAGI Reasoning Engine',
        provider: 'OpenAGI',
        category: 'Reasoning',
        description: 'Moteur de raisonnement avancé avec capacités logiques multi-niveaux',
        price: 'Gratuit',
        rating: 4.8,
        users: 15420,
        features: ['Raisonnement logique', 'Inférence causale', 'Planification stratégique']
      },
      {
        id: 2,
        name: 'SuperAGI Multi-Agent System',
        provider: 'SuperAGI',
        category: 'Collaboration',
        description: 'Système multi-agents pour coordination et collaboration intelligente',
        price: 'Freemium',
        rating: 4.6,
        users: 8930,
        features: ['Coordination d\'équipe', 'Distribution de tâches', 'Communication inter-agents']
      },
      {
        id: 3,
        name: 'DeepMind Gemini Integration',
        provider: 'Google DeepMind',
        category: 'Multimodal',
        description: 'Intégration avancée pour traitement multimodal et compréhension contextuelle',
        price: 'Premium',
        rating: 4.9,
        users: 23150,
        features: ['Traitement multimodal', 'Compréhension contextuelle', 'Génération créative']
      }
    ]
    setMarketplaceItems(marketplaceData)
  }, [])

  // Générateur de contenu AGI simplifié
  const generateNewSystem = () => {
    const names = ['Quantum Reasoning Core', 'Adaptive Neural Network', 'Cognitive Processing Unit', 'Intelligent Decision Engine', 'Advanced Learning System']
    const descriptions = [
      'Système AGI révolutionnaire avec capacités quantiques',
      'Réseau neuronal auto-adaptatif pour apprentissage continu',
      'Unité de traitement cognitif haute performance',
      'Moteur de décision intelligent multi-critères',
      'Système d\'apprentissage avancé avec mémoire persistante'
    ]
    
    const newSystem = {
      id: agiSystems.length + 1,
      name: names[Math.floor(Math.random() * names.length)],
      status: 'Active',
      category: ['Research', 'Development', 'Content'][Math.floor(Math.random() * 3)],
      description: descriptions[Math.floor(Math.random() * descriptions.length)],
      operations: Math.floor(Math.random() * 2000) + 500,
      efficiency: Math.floor(Math.random() * 20) + 80,
      capabilities: ['Advanced AI', 'Machine Learning'],
      lastUpdate: 'Just now'
    }
    
    // Mise à jour des statistiques
    setLastUpdate(new Date())
  }

  // Auto-update simulation
  useEffect(() => {
    if (autoUpdate) {
      const interval = setInterval(() => {
        setLastUpdate(new Date())
      }, 10000) // Update every 10 seconds
      
      return () => clearInterval(interval)
    }
  }, [autoUpdate])

  // Filtrage des systèmes AGI
  const filteredSystems = agiSystems.filter(system => {
    const matchesSearch = system.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         system.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || system.status.toLowerCase() === statusFilter
    const matchesCategory = categoryFilter === 'all' || system.category.toLowerCase() === categoryFilter
    
    return matchesSearch && matchesStatus && matchesCategory
  })

  // Calcul des statistiques
  const totalSystems = agiSystems.length
  const activeSystems = agiSystems.filter(s => s.status === 'Active').length
  const totalOperations = agiSystems.reduce((sum, s) => sum + s.operations, 0)
  const averageEfficiency = Math.round(agiSystems.reduce((sum, s) => sum + s.efficiency, 0) / agiSystems.length * 10) / 10

  // Navigation
  const navigation = [
    { id: 'home', label: 'AGI Systems', icon: Brain },
    { id: 'playground', label: 'AGI Playground', icon: Play },
    { id: 'multi-agent', label: 'Multi-Agent Tester', icon: Users },
    { id: 'blog', label: 'Blog AGI', icon: BookOpen },
    { id: 'marketplace', label: 'Marketplace', icon: ShoppingCart },
    { id: 'research', label: 'Research', icon: Target },
    { id: 'network', label: 'Network', icon: Network },
    { id: 'settings', label: 'Settings', icon: Settings }
  ]

  // Navigation Header Component
  const NavigationHeader = () => (
    <header className="nav-header border-b border-white/10 backdrop-blur-sm bg-white/5">
      {/* Skip link for keyboard navigation */}
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Brain className="h-8 w-8 text-purple-400" />
            <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
              AGI Hub
            </h1>
          </div>
          <nav className="hidden md:flex items-center space-x-6">
            {navigation.map((item) => {
              const Icon = item.icon
              return (
                <Button
                  key={item.id}
                  variant={currentPage === item.id ? "default" : "ghost"}
                  onClick={() => setCurrentPage(item.id)}
                  className={`flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent ${
                    currentPage === item.id
                      ? "bg-white text-black hover:bg-gray-100 shadow-lg"
                      : "text-white border border-white/50 hover:bg-white hover:text-black !hover:bg-white !hover:text-black hover:shadow-lg backdrop-blur-sm"
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.label}</span>
                </Button>
              )
            })}
          </nav>
          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="icon"
            className="mobile-nav-button md:hidden text-white"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            <Menu className="h-6 w-6" />
          </Button>
        </div>
      </div>
      {/* Mobile Navigation Menu */}
      {mobileMenuOpen && (
        <div className="mobile-menu md:hidden bg-white/10 backdrop-blur-sm border-t border-white/10">
          <div className="container mx-auto px-4 py-4">
            <nav className="flex flex-col space-y-2">
              {navigation.map((item) => {
                const Icon = item.icon
                return (
                  <Button
                    key={item.id}
                    variant={currentPage === item.id ? "default" : "ghost"}
                    onClick={() => {
                      setCurrentPage(item.id)
                      setMobileMenuOpen(false)
                    }}
                    className={`flex items-center space-x-2 justify-start ${
                      currentPage === item.id
                        ? "bg-white text-black hover:bg-gray-100 shadow-lg"
                        : "text-white border border-white/50 hover:bg-white hover:text-black hover:shadow-lg backdrop-blur-sm"
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{item.label}</span>
                  </Button>
                )
              })}
            </nav>
          </div>
        </div>
      )}
    </header>
  )

  // Composant AGI Playground
  const AGIPlaygroundPage = () => <AGIPlayground NavigationHeader={NavigationHeader} />

  // Composant Multi-Agent Tester
  const MultiAgentTesterPage = () => (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      <NavigationHeader />
      <div className="container mx-auto px-4 py-8">
        <MultiAgentTester />
      </div>
    </div>
  )

  // Composant Blog AGI
  const BlogPage = () => (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      <NavigationHeader />
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <BookOpen className="h-12 w-12 text-purple-400 mr-4" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
              Blog AGI
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Découvrez les dernières actualités et analyses sur l'Intelligence Artificielle Générale
          </p>
        </div>

        {/* Blog Controls */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <Card className="bg-white/10 backdrop-blur-sm border-white/20 flex-1">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-yellow-400" />
                  <span className="font-semibold">Génération Automatique</span>
                </div>
                <Button 
                  onClick={generateBlogPost}
                  disabled={isGeneratingPost}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {isGeneratingPost ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Génération...
                    </div>
                  ) : (
                    'Générer un Article'
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardContent className="p-4">
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-blue-400" />
                  <span>{blogPosts.length} Articles</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-green-400" />
                  <span>{blogPosts.reduce((sum, post) => sum + post.views, 0)} Vues</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-yellow-400" />
                  <span>Quotidien</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Blog Posts */}
        {selectedPost ? (
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardHeader>
              <div className="flex items-center justify-between">
                <Button 
                  variant="ghost" 
                  onClick={() => setSelectedPost(null)}
                  className="text-purple-400 hover:text-purple-300"
                >
                  ← Retour aux articles
                </Button>
                <div className="flex items-center gap-4 text-sm text-gray-400">
                  <span>{selectedPost.readTime}</span>
                  <span>{selectedPost.views} vues</span>
                </div>
              </div>
              <CardTitle className="text-3xl text-white mt-4">{selectedPost.title}</CardTitle>
              <CardDescription className="text-gray-300 text-lg">
                Par {selectedPost.author} • {selectedPost.publishedAt.toLocaleDateString()}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="prose prose-invert max-w-none">
                <div className="whitespace-pre-wrap text-gray-200 leading-relaxed">
                  {selectedPost.content}
                </div>
              </div>
              <div className="flex flex-wrap gap-2 mt-6">
                {selectedPost.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="bg-purple-600/20 text-purple-300">
                    {tag}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6">
            {blogPosts.length === 0 ? (
              <Card className="bg-white/10 backdrop-blur-sm border-white/20">
                <CardContent className="p-8 text-center">
                  <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400 text-lg">Aucun article disponible</p>
                  <p className="text-gray-500">Générez votre premier article avec l'IA</p>
                </CardContent>
              </Card>
            ) : (
              blogPosts.map((post) => (
                <Card key={post.id} className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/15 transition-colors cursor-pointer">
                  <CardContent className="p-6" onClick={() => setSelectedPost(post)}>
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-white mb-2">{post.title}</h3>
                        <p className="text-gray-300 mb-3">{post.excerpt}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-400">
                          <span>Par {post.author}</span>
                          <span>{post.publishedAt.toLocaleDateString()}</span>
                          <span>{post.readTime}</span>
                          <span>{post.views} vues</span>
                        </div>
                      </div>
                      <ChevronRight className="h-5 w-5 text-gray-400 ml-4" />
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {post.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="bg-purple-600/20 text-purple-300">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  )

  // Composant Marketplace
  const MarketplacePage = () => (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      <NavigationHeader />
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <ShoppingCart className="h-12 w-12 text-purple-400 mr-4" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
              AGI Marketplace
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Découvrez et intégrez les meilleurs systèmes AGI du monde entier
          </p>
        </div>

        {/* Marketplace Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardContent className="p-6 text-center">
              <Globe className="h-8 w-8 text-blue-400 mx-auto mb-2" />
              <p className="text-2xl font-bold text-white">{marketplaceItems.length}</p>
              <p className="text-gray-300">Systèmes AGI</p>
            </CardContent>
          </Card>
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 text-green-400 mx-auto mb-2" />
              <p className="text-2xl font-bold text-white">47.5K</p>
              <p className="text-gray-300">Utilisateurs Actifs</p>
            </CardContent>
          </Card>
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardContent className="p-6 text-center">
              <Star className="h-8 w-8 text-yellow-400 mx-auto mb-2" />
              <p className="text-2xl font-bold text-white">4.7</p>
              <p className="text-gray-300">Note Moyenne</p>
            </CardContent>
          </Card>
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardContent className="p-6 text-center">
              <TrendingUp className="h-8 w-8 text-purple-400 mx-auto mb-2" />
              <p className="text-2xl font-bold text-white">+23%</p>
              <p className="text-gray-300">Croissance</p>
            </CardContent>
          </Card>
        </div>

        {/* Marketplace Items */}
        <div className="grid gap-6">
          {marketplaceItems.map((item) => (
            <Card key={item.id} className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/15 transition-colors">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-xl font-bold text-white">{item.name}</h3>
                      <Badge variant="secondary" className="bg-blue-600/20 text-blue-300">
                        {item.category}
                      </Badge>
                      <Badge variant="outline" className="border-green-400 text-green-400">
                        {item.price}
                      </Badge>
                    </div>
                    <p className="text-gray-300 mb-3">{item.description}</p>
                    <div className="flex items-center gap-4 text-sm text-gray-400 mb-4">
                      <span>Par {item.provider}</span>
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span>{item.rating}</span>
                      </div>
                      <span>{item.users.toLocaleString()} utilisateurs</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {item.features.map((feature, index) => (
                        <Badge key={index} variant="secondary" className="bg-purple-600/20 text-purple-300">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className="ml-6 flex flex-col gap-2">
                    <Button className="bg-purple-600 hover:bg-purple-700">
                      Intégrer
                    </Button>
                    <Button variant="outline" className="border-white/50 text-white hover:bg-white hover:text-black hover:border-white backdrop-blur-sm">
                      Détails
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Coming Soon Section */}
        <Card className="bg-white/10 backdrop-blur-sm border-white/20 mt-8">
          <CardContent className="p-8 text-center">
            <Rocket className="h-16 w-16 text-purple-400 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-white mb-2">Plus de Systèmes AGI Bientôt</h3>
            <p className="text-gray-300 mb-4">
              Nous travaillons avec les meilleurs fournisseurs d'AGI pour vous apporter encore plus d'options
            </p>
            <Button className="bg-purple-600 hover:bg-purple-700">
              Être Notifié
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )

  // Composant Coming Soon
  const ComingSoonPage = ({ title }) => (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      <NavigationHeader />
      <div className="flex items-center justify-center" style={{ minHeight: 'calc(100vh - 80px)' }}>
        <div className="text-center">
          <div className="mb-8">
            <Brain className="h-24 w-24 text-purple-400 mx-auto mb-4" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent mb-4">
              {title}
            </h1>
            <p className="text-xl text-gray-300 mb-8">Bientôt disponible</p>
            <p className="text-gray-400 max-w-md mx-auto mb-8">
              Cette fonctionnalité est en cours de développement. Nous travaillons dur pour vous l'apporter bientôt.
            </p>
            <Button
              onClick={() => setCurrentPage('home')}
              className="bg-purple-600 hover:bg-purple-700"
            >
              Retour à l'accueil
            </Button>
          </div>
        </div>
      </div>
    </div>
  )

  // Gestion de la navigation
  if (currentPage === 'playground') {
    return <AGIPlaygroundPage />
  }

  if (currentPage === 'multi-agent') {
    return <MultiAgentTesterPage />
  }

  if (currentPage === 'blog') {
    return <BlogPage />
  }

  if (currentPage === 'marketplace') {
    return <MarketplacePage />
  }

  if (currentPage !== 'home') {
    return <ComingSoonPage title={navigation.find(nav => nav.id === currentPage)?.label || 'Page'} />
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      <NavigationHeader />
      {/* Page Content */}
      {currentPage === 'playground' ? (
        <AGIPlaygroundPage />
      ) : currentPage === 'blog' ? (
        <BlogPage />
      ) : currentPage === 'marketplace' ? (
        <MarketplacePage />
      ) : currentPage !== 'home' ? (
        <ComingSoonPage title={navigation.find(nav => nav.id === currentPage)?.label || 'Page'} />
      ) : (
        // Home page content (remove duplicate header/nav here)
        <>
          {/* Hero Section */}
          <section id="main-content" className="section-spacing-lg text-center">
            <div className="content-max-width container-padding">
              <div className="mb-8">
                <Brain className="h-20 w-20 text-purple-400 mx-auto mb-6" />
                <h2 className="mobile-heading font-bold mb-6 bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
                  The Future of AGI is Here
                </h2>
                <p className="text-xl text-white max-w-3xl mx-auto mb-8 text-contrast-high">
                  Explore, develop, and deploy Artificial General Intelligence systems that transcend traditional AI limitations
                </p>
                <div className="mobile-flex mobile-spacing gap-4 justify-center">
                  <Button
                    size="lg"
                    className="mobile-friendly bg-white text-black hover:bg-gray-100 px-8 py-3 font-semibold min-w-[180px] shadow-lg border-2 border-transparent"
                    onClick={() => setCurrentPage('playground')}
                  >
                    <Sparkles className="mr-2 h-5 w-5 flex-shrink-0" />
                    Start Building
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="mobile-friendly bg-transparent text-white px-8 py-3 font-semibold min-w-[180px] backdrop-blur-sm"
                    onClick={() => setCurrentPage('research')}
                  >
                    <BookOpen className="mr-2 h-5 w-5 flex-shrink-0" />
                    Explore Research
                  </Button>
                </div>
              </div>
            </div>
          </section>

          {/* Stats Section */}
          <section className="section-spacing-sm">
            <div className="content-max-width container-padding">
              <Card className="bg-gradient-to-r from-purple-600/20 to-cyan-600/20 backdrop-blur-sm border-white/20 mb-8">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Zap className="h-8 w-8 text-yellow-400" />
                      <div>
                        <h3 className="text-xl font-bold text-white">AI Automation</h3>
                        <p className="text-gray-300">Last Update: {lastUpdate.toLocaleTimeString()}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <Button
                        variant={autoUpdate ? "default" : "outline"}
                        onClick={() => setAutoUpdate(!autoUpdate)}
                        className={autoUpdate ? "bg-green-600 hover:bg-green-700" : "border-white/50 text-black hover:bg-white hover:text-black hover:border-white backdrop-blur-sm"}
                      >
                        Auto-Update {autoUpdate ? 'ON' : 'OFF'}
                      </Button>
                      <Button 
                        onClick={generateNewSystem}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Sparkles className="mr-2 h-4 w-4" />
                        Generate System
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* AGI Systems Library */}
          <section className="section-spacing">
            <div className="content-max-width container-padding">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
                  AGI Systems Library
                </h2>
                <p className="text-xl text-gray-300">
                  Discover and interact with cutting-edge AGI systems
                </p>
              </div>

              {/* Search and Filters */}
              <div className="flex flex-col md:flex-row gap-4 mb-8">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search AGI systems..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                    />
                  </div>
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full md:w-48 bg-white/10 border-white/20 text-white">
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-full md:w-48 bg-white/10 border-white/20 text-white">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="research">Research</SelectItem>
                    <SelectItem value="development">Development</SelectItem>
                    <SelectItem value="content">Content</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* AGI Systems Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 grid-spacing">
                {filteredSystems.map((system) => (
                  <Card key={system.id} className="bg-white/10 border-white/20 text-white hover:shadow-lg transition-shadow group">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Cpu className="h-5 w-5 text-purple-400" />
                        {system.name}
                        <Badge variant="outline" className={`ml-2 ${system.status === 'Active' ? 'border-green-400 text-green-300' : 'border-gray-400 text-gray-300'}`}>{system.status}</Badge>
                      </CardTitle>
                      <CardDescription className="text-gray-300">
                        {system.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex flex-wrap gap-2">
                          {system.capabilities.map((capability, index) => (
                            <Badge key={index} variant="outline" className="border-purple-400 text-purple-300">
                              {capability}
                            </Badge>
                          ))}
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-gray-400">Ops: <span className="text-white font-semibold">{system.operations}</span></p>
                          </div>
                          <div>
                            <p className="text-gray-400">Eff: <span className="text-white font-semibold">{system.efficiency}%</span></p>
                          </div>
                        </div>
                        <Button className="w-full bg-purple-600 hover:bg-purple-700 group-hover:bg-purple-500 transition-colors">
                          Learn More
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </section>

          {/* Footer */}
          <footer className="border-t border-white/10 py-8 mt-16">
            <div className="container mx-auto px-4 text-center">
              <p className="text-gray-400">© 2025 AGI Hub. All rights reserved.</p>
            </div>
          </footer>
        </>
      )}
    </div>
  )
}

export default App

