// Tool Service for orchestrating external capabilities
import webSearchTool from '../tools/webSearch.js'
import codeExecutionTool from '../tools/codeExecution.js'
import fileOperationsTool from '../tools/fileOperations.js'

class ToolService {
  constructor() {
    this.tools = new Map()
    this.toolUsageHistory = []
    this.maxHistoryLength = 100
    this.initializeTools()
  }

  // Initialize available tools
  initializeTools() {
    this.registerTool(webSearchTool)
    this.registerTool(codeExecutionTool)
    this.registerTool(fileOperationsTool)
    
    console.log('🔧 Tool service initialized with', this.tools.size, 'tools')
  }

  // Register a new tool
  registerTool(tool) {
    if (!tool.name || !tool.description) {
      throw new Error('Tool must have name and description')
    }
    
    this.tools.set(tool.name, tool)
    console.log(`🔧 Registered tool: ${tool.name}`)
  }

  // Get all available tools
  getAvailableTools() {
    return Array.from(this.tools.values()).map(tool => ({
      name: tool.name,
      description: tool.description,
      metadata: tool.getMetadata ? tool.getMetadata() : null
    }))
  }

  // Analyze user input to determine which tools to use
  analyzeInput(input) {
    const analysis = {
      input: input,
      suggestedTools: [],
      confidence: {},
      reasoning: []
    }

    console.log('🔍 Analyzing input for tools:', input)

    // Check each tool's shouldUse method
    for (const [name, tool] of this.tools) {
      console.log(`🔍 Checking tool: ${name}`)

      if (tool.shouldUse && tool.shouldUse(input)) {
        analysis.suggestedTools.push(name)
        analysis.confidence[name] = this.calculateConfidence(input, tool)
        analysis.reasoning.push(`${name}: ${this.getReasoningForTool(input, tool)}`)
        console.log(`✅ Tool ${name} suggested with confidence: ${analysis.confidence[name]}`)
      } else {
        console.log(`❌ Tool ${name} not suggested`)
      }
    }

    // Sort by confidence
    analysis.suggestedTools.sort((a, b) => analysis.confidence[b] - analysis.confidence[a])

    console.log('🔍 Final tool analysis:', analysis)
    return analysis
  }

  // Calculate confidence score for tool usage
  calculateConfidence(input, tool) {
    let confidence = 0.3 // Base confidence

    // Check for specific keywords
    const inputLower = input.toLowerCase()

    if (tool.name === 'web_search') {
      const searchKeywords = ['search', 'find', 'current', 'latest', 'news', 'what is', 'who is', 'when', 'where', 'weather', 'price', 'today', 'now']
      const matches = searchKeywords.filter(keyword => inputLower.includes(keyword))
      confidence += matches.length * 0.15

      console.log(`🔍 Web search keywords found: ${matches.join(', ')} (${matches.length} matches)`)
    }

    if (tool.name === 'code_execution') {
      const codeKeywords = ['run', 'execute', 'test', 'try', 'check', 'validate']
      const matches = codeKeywords.filter(keyword => inputLower.includes(keyword))
      confidence += matches.length * 0.15

      // Higher confidence if code is present
      if (inputLower.includes('function') || inputLower.includes('def ') || inputLower.includes('console.log')) {
        confidence += 0.3
      }
    }

    if (tool.name === 'file_operations') {
      const fileKeywords = ['file', 'csv', 'json', 'save', 'load', 'read', 'write']
      const matches = fileKeywords.filter(keyword => inputLower.includes(keyword))
      confidence += matches.length * 0.12
    }

    const finalConfidence = Math.min(confidence, 1.0) // Cap at 1.0
    console.log(`🔧 Confidence for ${tool.name}: ${finalConfidence}`)
    return finalConfidence
  }

  // Get reasoning for why a tool was suggested
  getReasoningForTool(input, tool) {
    const inputLower = input.toLowerCase()
    
    switch (tool.name) {
      case 'web_search':
        if (inputLower.includes('current') || inputLower.includes('latest')) {
          return 'Request for current/latest information'
        }
        if (inputLower.includes('search') || inputLower.includes('find')) {
          return 'Explicit search request'
        }
        return 'May benefit from web search'
        
      case 'code_execution':
        if (inputLower.includes('run') || inputLower.includes('execute')) {
          return 'Explicit execution request'
        }
        if (inputLower.includes('test') || inputLower.includes('validate')) {
          return 'Code testing/validation needed'
        }
        return 'Code execution may be helpful'
        
      case 'file_operations':
        if (inputLower.includes('file') || inputLower.includes('csv')) {
          return 'File operation explicitly mentioned'
        }
        if (inputLower.includes('save') || inputLower.includes('load')) {
          return 'Data persistence operation'
        }
        return 'File handling may be needed'
        
      default:
        return 'Tool usage suggested'
    }
  }

  // Execute a specific tool
  async executeTool(toolName, ...args) {
    try {
      const tool = this.tools.get(toolName)
      if (!tool) {
        throw new Error(`Tool not found: ${toolName}`)
      }

      console.log(`🔧 Executing tool: ${toolName}`)
      
      // Record tool usage
      const usage = {
        toolName: toolName,
        timestamp: new Date().toISOString(),
        args: args
      }

      let result
      
      // Execute the tool based on its interface
      if (tool.execute) {
        result = await tool.execute(...args)
      } else if (tool.search && toolName === 'web_search') {
        result = await tool.search(...args)
      } else if (tool.handleFileOperation && toolName === 'file_operations') {
        result = await tool.handleFileOperation(...args)
      } else {
        throw new Error(`Tool ${toolName} does not have a valid execution method`)
      }

      usage.result = result
      usage.success = result.success !== false
      
      // Add to history
      this.toolUsageHistory.push(usage)
      if (this.toolUsageHistory.length > this.maxHistoryLength) {
        this.toolUsageHistory.shift()
      }

      console.log(`✅ Tool execution completed: ${toolName}`)
      return result

    } catch (error) {
      console.error(`❌ Tool execution failed: ${toolName}`, error)
      
      const errorResult = {
        success: false,
        error: error.message,
        toolName: toolName
      }

      // Record failed usage
      this.toolUsageHistory.push({
        toolName: toolName,
        timestamp: new Date().toISOString(),
        args: args,
        result: errorResult,
        success: false
      })

      return errorResult
    }
  }

  // Execute multiple tools in sequence
  async executeMultipleTools(toolExecutions) {
    const results = []
    
    for (const execution of toolExecutions) {
      const { toolName, args } = execution
      const result = await this.executeTool(toolName, ...args)
      results.push({
        toolName,
        result,
        success: result.success !== false
      })
      
      // Stop on first failure if specified
      if (execution.stopOnFailure && !result.success) {
        break
      }
    }
    
    return results
  }

  // Get tool usage statistics
  getUsageStats() {
    const stats = {
      totalExecutions: this.toolUsageHistory.length,
      successfulExecutions: this.toolUsageHistory.filter(usage => usage.success).length,
      toolUsageCounts: {},
      recentExecutions: this.toolUsageHistory.slice(-10)
    }

    // Count usage per tool
    this.toolUsageHistory.forEach(usage => {
      stats.toolUsageCounts[usage.toolName] = (stats.toolUsageCounts[usage.toolName] || 0) + 1
    })

    return stats
  }

  // Clear tool usage history
  clearHistory() {
    this.toolUsageHistory = []
    console.log('🗑️ Tool usage history cleared')
  }

  // Get tool by name
  getTool(toolName) {
    return this.tools.get(toolName)
  }

  // Check if tool is available
  isToolAvailable(toolName) {
    return this.tools.has(toolName)
  }

  // Format tool results for AI consumption
  formatToolResults(toolName, result) {
    const tool = this.tools.get(toolName)
    
    if (tool && tool.formatResultsForAI) {
      return tool.formatResultsForAI(result)
    }
    
    // Default formatting
    if (!result.success) {
      return `Tool execution failed: ${result.error}`
    }
    
    return `Tool "${toolName}" executed successfully:\n${JSON.stringify(result, null, 2)}`
  }

  // Create tool execution plan
  createExecutionPlan(input) {
    const analysis = this.analyzeInput(input)
    
    const plan = {
      input: input,
      recommendedTools: analysis.suggestedTools.slice(0, 3), // Top 3 tools
      confidence: analysis.confidence,
      reasoning: analysis.reasoning,
      executionStrategy: this.determineExecutionStrategy(analysis),
      timestamp: new Date().toISOString()
    }

    return plan
  }

  // Determine execution strategy
  determineExecutionStrategy(analysis) {
    if (analysis.suggestedTools.length === 0) {
      return 'no_tools'
    }
    
    if (analysis.suggestedTools.length === 1) {
      return 'single_tool'
    }
    
    // Check if tools can be combined
    const hasSearch = analysis.suggestedTools.includes('web_search')
    const hasCode = analysis.suggestedTools.includes('code_execution')
    const hasFile = analysis.suggestedTools.includes('file_operations')
    
    if (hasSearch && hasCode) {
      return 'search_then_code'
    }
    
    if (hasFile && hasCode) {
      return 'file_then_code'
    }
    
    return 'sequential'
  }
}

// Create singleton instance
const toolService = new ToolService()
export default toolService
