// Specialized Agents - Domain-specific AI agents with specialized capabilities
import geminiService from './geminiService.js'
import toolService from './toolService.js'
import reasoningService from './reasoningService.js'
import agentFramework from './agentFramework.js'
import { CreativeAgent, CoordinatorAgent } from './creativeCoordinatorAgents.js'

class SpecializedAgents {
  constructor() {
    this.name = 'specialized_agents'
    this.version = '1.0.0'
    this.agentImplementations = new Map()
  }

  // Initialize specialized agents
  async initialize() {
    console.log('🎯 Initializing Specialized Agents...')

    try {
      // Register agent implementations
      this.registerAgentImplementations()

      console.log('✅ Specialized Agents initialized successfully')
      return true
    } catch (error) {
      console.error('❌ Failed to initialize Specialized Agents:', error)
      return false
    }
  }

  // Register implementations for each agent type
  registerAgentImplementations() {
    this.agentImplementations.set('research', new ResearchAgent())
    this.agentImplementations.set('coding', new CodingAgent())
    this.agentImplementations.set('analysis', new AnalysisAgent())
    this.agentImplementations.set('creative', new CreativeAgent())
    this.agentImplementations.set('coordinator', new CoordinatorAgent())

    console.log('✅ Registered all specialized agent implementations')
  }

  // Execute task with appropriate specialized agent
  async executeTask(agentType, task, context = {}) {
    const implementation = this.agentImplementations.get(agentType)
    if (!implementation) {
      throw new Error(`No implementation found for agent type: ${agentType}`)
    }

    console.log(`🎯 Executing ${agentType} task: ${task.description}`)

    try {
      const startTime = Date.now()
      const result = await implementation.executeTask(task, context)
      const completionTime = Date.now() - startTime

      return {
        ...result,
        agentType,
        completionTime,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      console.error(`❌ ${agentType} agent task failed:`, error)
      return {
        success: false,
        error: error.message,
        agentType,
        timestamp: new Date().toISOString()
      }
    }
  }

  // Get agent implementation
  getAgentImplementation(agentType) {
    return this.agentImplementations.get(agentType)
  }
}

// Research Agent - Specializes in information gathering and research
class ResearchAgent {
  constructor() {
    this.name = 'Research Agent'
    this.capabilities = ['web_search', 'data_analysis', 'fact_checking', 'source_verification']
    this.tools = ['web_search', 'knowledge_base']
  }

  async executeTask(task, context = {}) {
    console.log('🔍 Research Agent executing task...')

    const { type, query, requirements = {} } = task

    switch (type) {
      case 'web_search':
        return await this.performWebSearch(query, requirements)
      case 'fact_checking':
        return await this.performFactCheck(query, requirements)
      case 'source_verification':
        return await this.verifySource(query, requirements)
      case 'research_synthesis':
        return await this.synthesizeResearch(query, requirements, context)
      default:
        return await this.performGeneralResearch(query, requirements)
    }
  }

  async performWebSearch(query, requirements) {
    try {
      console.log(`🔍 Performing web search: ${query}`)

      // Use tool service for web search
      const searchResults = await toolService.executeTool('web_search', { query })

      // Analyze and filter results
      const analyzedResults = await this.analyzeSearchResults(searchResults, requirements)

      return {
        success: true,
        type: 'web_search',
        query,
        results: analyzedResults,
        summary: this.generateSearchSummary(analyzedResults),
        sources: this.extractSources(analyzedResults),
        confidence: this.calculateConfidence(analyzedResults)
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'web_search'
      }
    }
  }

  async performFactCheck(query, requirements) {
    console.log(`✅ Performing fact check: ${query}`)

    try {
      // Search for information about the claim
      const searchResults = await this.performWebSearch(query, {
        ...requirements,
        focus: 'fact_checking'
      })

      // Analyze credibility of sources
      const credibilityAnalysis = this.analyzeSourceCredibility(searchResults.sources)

      // Generate fact check result
      const factCheckResult = await this.generateFactCheckResult(query, searchResults, credibilityAnalysis)

      return {
        success: true,
        type: 'fact_check',
        query,
        result: factCheckResult,
        credibility: credibilityAnalysis,
        evidence: searchResults.results,
        confidence: factCheckResult.confidence
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'fact_check'
      }
    }
  }

  async verifySource(source, requirements) {
    console.log(`🔍 Verifying source: ${source}`)

    try {
      // Analyze source credibility
      const credibilityScore = this.calculateSourceCredibility(source)

      // Check for bias indicators
      const biasAnalysis = this.analyzeBias(source)

      // Verify publication date and relevance
      const relevanceScore = this.calculateRelevance(source, requirements)

      return {
        success: true,
        type: 'source_verification',
        source,
        credibility: credibilityScore,
        bias: biasAnalysis,
        relevance: relevanceScore,
        overall_score: (credibilityScore + relevanceScore) / 2,
        recommendations: this.generateSourceRecommendations(credibilityScore, biasAnalysis, relevanceScore)
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'source_verification'
      }
    }
  }

  async synthesizeResearch(query, requirements, context) {
    console.log(`📊 Synthesizing research: ${query}`)

    try {
      // Gather information from multiple sources
      const searchResults = await this.performWebSearch(query, requirements)

      // Combine with context from other agents
      const combinedData = this.combineResearchData(searchResults, context)

      // Generate comprehensive synthesis
      const synthesis = await this.generateResearchSynthesis(query, combinedData, requirements)

      return {
        success: true,
        type: 'research_synthesis',
        query,
        synthesis,
        sources: combinedData.sources,
        key_findings: synthesis.keyFindings,
        recommendations: synthesis.recommendations,
        confidence: synthesis.confidence
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'research_synthesis'
      }
    }
  }

  async performGeneralResearch(query, requirements) {
    console.log(`📚 Performing general research: ${query}`)

    try {
      // Determine research approach based on query
      const researchPlan = this.createResearchPlan(query, requirements)

      // Execute research steps
      const results = []
      for (const step of researchPlan.steps) {
        const stepResult = await this.executeResearchStep(step)
        results.push(stepResult)
      }

      // Compile final research report
      const report = this.compileResearchReport(query, results, requirements)

      return {
        success: true,
        type: 'general_research',
        query,
        plan: researchPlan,
        results,
        report,
        confidence: report.confidence
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'general_research'
      }
    }
  }

  // Helper methods for research agent
  async analyzeSearchResults(searchResults, requirements) {
    // Filter and rank results based on relevance and credibility
    if (!searchResults || !searchResults.results) return []

    return searchResults.results.map(result => ({
      ...result,
      relevanceScore: this.calculateRelevance(result, requirements),
      credibilityScore: this.calculateSourceCredibility(result.url || result.source),
      summary: result.snippet || result.description || ''
    })).sort((a, b) => (b.relevanceScore + b.credibilityScore) - (a.relevanceScore + a.credibilityScore))
  }

  generateSearchSummary(results) {
    if (!results || results.length === 0) {
      return 'No relevant results found.'
    }

    const topResults = results.slice(0, 3)
    return `Found ${results.length} relevant results. Key findings: ${topResults.map(r => r.summary).join('; ')}`
  }

  extractSources(results) {
    if (!results) return []

    return results.map(result => ({
      url: result.url || result.link,
      title: result.title,
      credibility: result.credibilityScore,
      relevance: result.relevanceScore
    }))
  }

  calculateConfidence(results) {
    if (!results || results.length === 0) return 0

    const avgCredibility = results.reduce((sum, r) => sum + (r.credibilityScore || 0), 0) / results.length
    const avgRelevance = results.reduce((sum, r) => sum + (r.relevanceScore || 0), 0) / results.length

    return Math.round((avgCredibility + avgRelevance) / 2)
  }

  analyzeSourceCredibility(sources) {
    if (!sources) return { score: 0, analysis: 'No sources to analyze' }

    const credibilityScores = sources.map(source => this.calculateSourceCredibility(source.url))
    const avgScore = credibilityScores.reduce((sum, score) => sum + score, 0) / credibilityScores.length

    return {
      score: Math.round(avgScore),
      analysis: avgScore > 70 ? 'High credibility sources' : avgScore > 40 ? 'Mixed credibility' : 'Low credibility sources',
      breakdown: sources.map((source, index) => ({
        source: source.url,
        score: credibilityScores[index]
      }))
    }
  }

  calculateSourceCredibility(url) {
    if (!url) return 0

    // Simple credibility scoring based on domain
    const domain = url.toLowerCase()

    // High credibility domains
    if (domain.includes('edu') || domain.includes('gov') ||
        domain.includes('wikipedia') || domain.includes('scholar.google')) {
      return 90
    }

    // Medium credibility domains
    if (domain.includes('org') || domain.includes('reuters') ||
        domain.includes('bbc') || domain.includes('npr')) {
      return 75
    }

    // Lower credibility for social media and blogs
    if (domain.includes('twitter') || domain.includes('facebook') ||
        domain.includes('blog') || domain.includes('medium')) {
      return 40
    }

    // Default credibility
    return 60
  }

  analyzeBias(source) {
    // Simple bias analysis
    return {
      score: 50, // Neutral by default
      indicators: [],
      analysis: 'No significant bias indicators detected'
    }
  }

  calculateRelevance(result, requirements) {
    if (!result || !requirements) return 50

    let relevance = 50

    // Check if result contains query terms
    const query = requirements.query || ''
    if (query && result.title && result.title.toLowerCase().includes(query.toLowerCase())) {
      relevance += 20
    }

    if (query && result.snippet && result.snippet.toLowerCase().includes(query.toLowerCase())) {
      relevance += 15
    }

    // Check for specific requirements
    if (requirements.focus) {
      const focus = requirements.focus.toLowerCase()
      if (result.title && result.title.toLowerCase().includes(focus)) {
        relevance += 15
      }
    }

    return Math.min(100, relevance)
  }

  generateSourceRecommendations(credibility, bias, relevance) {
    const recommendations = []

    if (credibility < 50) {
      recommendations.push('Consider finding additional sources with higher credibility')
    }

    if (bias.score > 70 || bias.score < 30) {
      recommendations.push('Look for sources with different perspectives to balance potential bias')
    }

    if (relevance < 60) {
      recommendations.push('Search for more specific sources related to your query')
    }

    if (recommendations.length === 0) {
      recommendations.push('Source appears reliable and relevant')
    }

    return recommendations
  }

  async generateFactCheckResult(query, searchResults, credibilityAnalysis) {
    // Use Gemini to analyze the fact check
    const prompt = `
    Analyze this claim for factual accuracy: "${query}"

    Based on search results: ${JSON.stringify(searchResults.summary)}
    Source credibility: ${credibilityAnalysis.analysis}

    Provide a fact check result with:
    1. Verdict (True/False/Partially True/Unverified)
    2. Explanation
    3. Confidence level (0-100)
    4. Key evidence
    `

    try {
      const response = await geminiService.generateResponse(prompt, 'analysis')

      return {
        verdict: this.extractVerdict(response.content),
        explanation: response.content,
        confidence: this.extractConfidence(response.content),
        evidence: searchResults.results.slice(0, 3)
      }
    } catch (error) {
      return {
        verdict: 'Unverified',
        explanation: 'Unable to verify due to analysis error',
        confidence: 0,
        evidence: []
      }
    }
  }

  extractVerdict(content) {
    const verdictPatterns = {
      'true': /\b(true|accurate|correct|verified)\b/i,
      'false': /\b(false|incorrect|inaccurate|debunked)\b/i,
      'partially_true': /\b(partially|partly|mixed|some truth)\b/i
    }

    for (const [verdict, pattern] of Object.entries(verdictPatterns)) {
      if (pattern.test(content)) {
        return verdict
      }
    }

    return 'unverified'
  }

  extractConfidence(content) {
    const confidenceMatch = content.match(/confidence[:\s]*(\d+)/i)
    return confidenceMatch ? parseInt(confidenceMatch[1]) : 50
  }

  combineResearchData(searchResults, context) {
    return {
      searchResults,
      contextData: context,
      sources: [
        ...searchResults.sources,
        ...(context.sources || [])
      ],
      combinedFindings: [
        ...(searchResults.results || []),
        ...(context.findings || [])
      ]
    }
  }

  async generateResearchSynthesis(query, combinedData, requirements) {
    const prompt = `
    Synthesize research findings for: "${query}"

    Data sources: ${JSON.stringify(combinedData.sources.slice(0, 5))}
    Key findings: ${JSON.stringify(combinedData.combinedFindings.slice(0, 3))}

    Provide a comprehensive synthesis with:
    1. Key findings
    2. Main conclusions
    3. Recommendations
    4. Areas needing further research
    5. Confidence assessment
    `

    try {
      const response = await geminiService.generateResponse(prompt, 'analysis')

      return {
        content: response.content,
        keyFindings: this.extractKeyFindings(response.content),
        recommendations: this.extractRecommendations(response.content),
        confidence: this.extractConfidence(response.content)
      }
    } catch (error) {
      return {
        content: 'Unable to generate synthesis due to analysis error',
        keyFindings: [],
        recommendations: [],
        confidence: 0
      }
    }
  }

  extractKeyFindings(content) {
    // Extract bullet points or numbered items as key findings
    const findings = content.match(/(?:^|\n)(?:\d+\.|[-*])\s*(.+)/gm)
    return findings ? findings.map(f => f.replace(/^[\n\d\.\-\*\s]+/, '').trim()).slice(0, 5) : []
  }

  extractRecommendations(content) {
    // Look for recommendation sections
    const recSection = content.match(/recommendations?[:\n](.*?)(?:\n\n|\n[A-Z]|$)/is)
    if (recSection) {
      const recs = recSection[1].match(/(?:^|\n)(?:\d+\.|[-*])\s*(.+)/gm)
      return recs ? recs.map(r => r.replace(/^[\n\d\.\-\*\s]+/, '').trim()) : []
    }
    return []
  }

  createResearchPlan(query, requirements) {
    return {
      query,
      approach: 'comprehensive',
      steps: [
        { type: 'web_search', description: 'Search for current information' },
        { type: 'source_verification', description: 'Verify source credibility' },
        { type: 'synthesis', description: 'Synthesize findings' }
      ],
      estimatedTime: '5-10 minutes'
    }
  }

  async executeResearchStep(step) {
    // Simplified step execution
    return {
      step: step.type,
      status: 'completed',
      result: `Completed ${step.description}`,
      timestamp: new Date().toISOString()
    }
  }

  compileResearchReport(query, results, requirements) {
    return {
      query,
      summary: `Research completed for: ${query}`,
      findings: results.map(r => r.result),
      confidence: 75,
      completedSteps: results.length,
      timestamp: new Date().toISOString()
    }
  }
}

// Coding Agent - Specializes in software development and programming
class CodingAgent {
  constructor() {
    this.name = 'Coding Agent'
    this.capabilities = ['code_generation', 'code_review', 'debugging', 'testing', 'documentation']
    this.tools = ['code_execution', 'file_operations', 'syntax_validation']
  }

  async executeTask(task, context = {}) {
    console.log('💻 Coding Agent executing task...')

    const { type, description, requirements = {} } = task

    switch (type) {
      case 'code_generation':
        return await this.generateCode(description, requirements)
      case 'code_review':
        return await this.reviewCode(description, requirements)
      case 'debugging':
        return await this.debugCode(description, requirements)
      case 'testing':
        return await this.generateTests(description, requirements)
      case 'documentation':
        return await this.generateDocumentation(description, requirements)
      default:
        return await this.performGeneralCoding(description, requirements)
    }
  }

  async generateCode(description, requirements) {
    console.log(`💻 Generating code: ${description}`)

    try {
      const prompt = this.buildCodeGenerationPrompt(description, requirements)
      const response = await geminiService.generateResponse(prompt, 'coding')

      const code = this.extractCode(response.content)
      const explanation = this.extractExplanation(response.content)

      return {
        success: true,
        type: 'code_generation',
        description,
        code,
        explanation,
        language: requirements.language || 'javascript',
        quality_score: this.assessCodeQuality(code),
        suggestions: this.generateCodeSuggestions(code)
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'code_generation'
      }
    }
  }

  async reviewCode(codeToReview, requirements) {
    console.log(`🔍 Reviewing code...`)

    try {
      const prompt = this.buildCodeReviewPrompt(codeToReview, requirements)
      const response = await geminiService.generateResponse(prompt, 'analysis')

      const review = this.parseCodeReview(response.content)

      return {
        success: true,
        type: 'code_review',
        original_code: codeToReview,
        review,
        overall_score: review.overall_score,
        issues: review.issues,
        suggestions: review.suggestions,
        strengths: review.strengths
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'code_review'
      }
    }
  }

  async debugCode(description, requirements) {
    console.log(`🐛 Debugging code: ${description}`)

    try {
      const { code, error_message } = requirements
      const prompt = this.buildDebuggingPrompt(code, error_message, description)
      const response = await geminiService.generateResponse(prompt, 'problem_solving')

      const debugResult = this.parseDebugResult(response.content)

      return {
        success: true,
        type: 'debugging',
        original_code: code,
        error_message,
        diagnosis: debugResult.diagnosis,
        fixed_code: debugResult.fixed_code,
        explanation: debugResult.explanation,
        prevention_tips: debugResult.prevention_tips
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'debugging'
      }
    }
  }

  async generateTests(description, requirements) {
    console.log(`🧪 Generating tests: ${description}`)

    try {
      const { code, test_framework } = requirements
      const prompt = this.buildTestGenerationPrompt(code, test_framework, description)
      const response = await geminiService.generateResponse(prompt, 'coding')

      const tests = this.extractTests(response.content)

      return {
        success: true,
        type: 'testing',
        original_code: code,
        test_code: tests.code,
        test_cases: tests.cases,
        framework: test_framework || 'jest',
        coverage_estimate: tests.coverage_estimate,
        explanation: tests.explanation
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'testing'
      }
    }
  }

  async generateDocumentation(description, requirements) {
    console.log(`📚 Generating documentation: ${description}`)

    try {
      const { code, doc_type } = requirements
      const prompt = this.buildDocumentationPrompt(code, doc_type, description)
      const response = await geminiService.generateResponse(prompt, 'documentation')

      const documentation = this.parseDocumentation(response.content)

      return {
        success: true,
        type: 'documentation',
        original_code: code,
        documentation: documentation.content,
        doc_type: doc_type || 'api',
        sections: documentation.sections,
        examples: documentation.examples
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'documentation'
      }
    }
  }

  // Helper methods for coding agent
  buildCodeGenerationPrompt(description, requirements) {
    const { language = 'javascript', style = 'modern', complexity = 'medium' } = requirements

    return `Generate ${language} code for: ${description}

Requirements:
- Language: ${language}
- Style: ${style}
- Complexity: ${complexity}
- Include comments and explanations
- Follow best practices
- Provide working, tested code

Please provide:
1. Complete code implementation
2. Explanation of the approach
3. Usage examples
4. Any important notes or considerations`
  }

  buildCodeReviewPrompt(code, requirements) {
    return `Review this code for quality, best practices, and potential issues:

\`\`\`
${code}
\`\`\`

Please analyze:
1. Code quality and readability
2. Performance considerations
3. Security issues
4. Best practice adherence
5. Potential bugs or edge cases
6. Suggestions for improvement

Provide an overall score (1-10) and detailed feedback.`
  }

  buildDebuggingPrompt(code, errorMessage, description) {
    return `Debug this code that's producing an error:

Problem: ${description}
Error: ${errorMessage}

Code:
\`\`\`
${code}
\`\`\`

Please:
1. Identify the root cause of the error
2. Provide the corrected code
3. Explain what was wrong and why
4. Suggest ways to prevent similar issues`
  }

  buildTestGenerationPrompt(code, framework, description) {
    return `Generate comprehensive tests for this code:

Description: ${description}
Framework: ${framework || 'jest'}

Code to test:
\`\`\`
${code}
\`\`\`

Please provide:
1. Complete test suite
2. Test cases covering normal and edge cases
3. Mock data if needed
4. Explanation of test strategy`
  }

  buildDocumentationPrompt(code, docType, description) {
    return `Generate ${docType || 'API'} documentation for this code:

Purpose: ${description}

Code:
\`\`\`
${code}
\`\`\`

Please provide:
1. Clear documentation with examples
2. Parameter descriptions
3. Return value explanations
4. Usage examples
5. Any important notes or warnings`
  }

  extractCode(content) {
    // Extract code blocks from response
    const codeBlocks = content.match(/```[\s\S]*?```/g)
    if (codeBlocks && codeBlocks.length > 0) {
      return codeBlocks[0].replace(/```\w*\n?/, '').replace(/```$/, '').trim()
    }
    return content
  }

  extractExplanation(content) {
    // Extract explanation text (non-code parts)
    const withoutCode = content.replace(/```[\s\S]*?```/g, '[CODE_BLOCK]')
    return withoutCode.replace(/\[CODE_BLOCK\]/g, '').trim()
  }

  assessCodeQuality(code) {
    let score = 50 // Base score

    // Check for comments
    if (code.includes('//') || code.includes('/*')) score += 10

    // Check for proper naming
    if (/[a-z][A-Z]/.test(code)) score += 10 // camelCase

    // Check for error handling
    if (code.includes('try') || code.includes('catch')) score += 15

    // Check for functions/modularity
    if (code.includes('function') || code.includes('=>')) score += 10

    // Check length (not too short, not too long)
    if (code.length > 50 && code.length < 1000) score += 5

    return Math.min(100, score)
  }

  generateCodeSuggestions(code) {
    const suggestions = []

    if (!code.includes('//') && !code.includes('/*')) {
      suggestions.push('Add comments to explain complex logic')
    }

    if (!code.includes('try') && !code.includes('catch')) {
      suggestions.push('Consider adding error handling')
    }

    if (code.length > 500) {
      suggestions.push('Consider breaking into smaller functions')
    }

    return suggestions
  }

  parseCodeReview(content) {
    // Extract overall score
    const scoreMatch = content.match(/(?:score|rating)[:\s]*(\d+)/i)
    const overall_score = scoreMatch ? parseInt(scoreMatch[1]) : 7

    // Extract issues (look for negative indicators)
    const issues = []
    const issuePatterns = [
      /issue[s]?[:\s]*(.+?)(?:\n|$)/gi,
      /problem[s]?[:\s]*(.+?)(?:\n|$)/gi,
      /bug[s]?[:\s]*(.+?)(?:\n|$)/gi
    ]

    issuePatterns.forEach(pattern => {
      const matches = [...content.matchAll(pattern)]
      matches.forEach(match => issues.push(match[1].trim()))
    })

    // Extract suggestions
    const suggestions = []
    const suggestionPatterns = [
      /suggest[ion]*[s]?[:\s]*(.+?)(?:\n|$)/gi,
      /recommend[ation]*[s]?[:\s]*(.+?)(?:\n|$)/gi,
      /improve[ment]*[s]?[:\s]*(.+?)(?:\n|$)/gi
    ]

    suggestionPatterns.forEach(pattern => {
      const matches = [...content.matchAll(pattern)]
      matches.forEach(match => suggestions.push(match[1].trim()))
    })

    // Extract strengths
    const strengths = []
    const strengthPatterns = [
      /good[:\s]*(.+?)(?:\n|$)/gi,
      /strength[s]?[:\s]*(.+?)(?:\n|$)/gi,
      /well[:\s]*(.+?)(?:\n|$)/gi
    ]

    strengthPatterns.forEach(pattern => {
      const matches = [...content.matchAll(pattern)]
      matches.forEach(match => strengths.push(match[1].trim()))
    })

    return {
      overall_score,
      issues: issues.slice(0, 5),
      suggestions: suggestions.slice(0, 5),
      strengths: strengths.slice(0, 3),
      full_review: content
    }
  }

  parseDebugResult(content) {
    return {
      diagnosis: this.extractSection(content, 'diagnosis|cause|problem'),
      fixed_code: this.extractCode(content),
      explanation: this.extractSection(content, 'explanation|fix|solution'),
      prevention_tips: this.extractSection(content, 'prevention|avoid|tips')
    }
  }

  extractTests(content) {
    const testCode = this.extractCode(content)

    // Count test cases
    const testCaseCount = (testCode.match(/it\(|test\(|describe\(/g) || []).length

    return {
      code: testCode,
      cases: testCaseCount,
      coverage_estimate: Math.min(90, testCaseCount * 15),
      explanation: this.extractExplanation(content)
    }
  }

  parseDocumentation(content) {
    // Extract sections
    const sections = []
    const sectionMatches = content.match(/#{1,3}\s*(.+)/g)
    if (sectionMatches) {
      sections.push(...sectionMatches.map(s => s.replace(/#{1,3}\s*/, '')))
    }

    // Extract examples
    const examples = []
    const exampleBlocks = content.match(/```[\s\S]*?```/g)
    if (exampleBlocks) {
      examples.push(...exampleBlocks)
    }

    return {
      content,
      sections,
      examples
    }
  }

  extractSection(content, pattern) {
    const regex = new RegExp(`(?:${pattern})[:\n]([\\s\\S]*?)(?:\n\n|\n[A-Z]|$)`, 'i')
    const match = content.match(regex)
    return match ? match[1].trim() : ''
  }

  async performGeneralCoding(description, requirements) {
    // Default to code generation for general coding tasks
    return await this.generateCode(description, requirements)
  }
}

// Analysis Agent - Specializes in data analysis and logical reasoning
class AnalysisAgent {
  constructor() {
    this.name = 'Analysis Agent'
    this.capabilities = ['data_analysis', 'pattern_recognition', 'logical_reasoning', 'problem_solving']
    this.tools = ['reasoning_engine', 'data_processing']
  }

  async executeTask(task, context = {}) {
    console.log('📊 Analysis Agent executing task...')

    const { type, data, question, requirements = {} } = task

    switch (type) {
      case 'data_analysis':
        return await this.analyzeData(data, requirements)
      case 'pattern_recognition':
        return await this.recognizePatterns(data, requirements)
      case 'logical_reasoning':
        return await this.performLogicalReasoning(question, requirements)
      case 'problem_solving':
        return await this.solveProblem(question, requirements, context)
      case 'trend_analysis':
        return await this.analyzeTrends(data, requirements)
      default:
        return await this.performGeneralAnalysis(data || question, requirements)
    }
  }

  async analyzeData(data, requirements) {
    console.log(`📊 Analyzing data...`)

    try {
      // Use reasoning service for complex analysis
      const reasoningResult = await reasoningService.analyzeAndReason(
        `Analyze this data: ${JSON.stringify(data)}`,
        { mode: 'analysis', requirements }
      )

      const analysis = await this.generateDataAnalysis(data, requirements, reasoningResult)

      return {
        success: true,
        type: 'data_analysis',
        data,
        analysis,
        insights: analysis.insights,
        patterns: analysis.patterns,
        recommendations: analysis.recommendations,
        confidence: analysis.confidence
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'data_analysis'
      }
    }
  }

  async recognizePatterns(data, requirements) {
    console.log(`🔍 Recognizing patterns...`)

    try {
      const patterns = this.identifyPatterns(data)
      const significance = this.assessPatternSignificance(patterns, data)

      return {
        success: true,
        type: 'pattern_recognition',
        data,
        patterns: patterns.map((pattern, index) => ({
          ...pattern,
          significance: significance[index]
        })),
        summary: this.generatePatternSummary(patterns),
        confidence: this.calculatePatternConfidence(patterns)
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'pattern_recognition'
      }
    }
  }

  async performLogicalReasoning(question, requirements) {
    console.log(`🧠 Performing logical reasoning: ${question}`)

    try {
      // Use reasoning service for structured thinking
      const reasoningResult = await reasoningService.analyzeAndReason(question, {
        mode: 'logical_reasoning',
        requirements
      })

      const logicalAnalysis = await this.generateLogicalAnalysis(question, reasoningResult, requirements)

      return {
        success: true,
        type: 'logical_reasoning',
        question,
        reasoning_steps: reasoningResult.reasoningSteps,
        conclusion: logicalAnalysis.conclusion,
        premises: logicalAnalysis.premises,
        logical_validity: logicalAnalysis.validity,
        confidence: logicalAnalysis.confidence
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'logical_reasoning'
      }
    }
  }

  async solveProblem(problem, requirements, context) {
    console.log(`🎯 Solving problem: ${problem}`)

    try {
      // Break down the problem
      const problemBreakdown = this.breakdownProblem(problem, requirements)

      // Generate solution approach
      const solutionApproach = await this.generateSolutionApproach(problemBreakdown, context)

      // Execute solution steps
      const solution = await this.executeSolutionSteps(solutionApproach, context)

      return {
        success: true,
        type: 'problem_solving',
        problem,
        breakdown: problemBreakdown,
        approach: solutionApproach,
        solution,
        verification: this.verifySolution(solution, problem),
        confidence: solution.confidence
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'problem_solving'
      }
    }
  }

  async analyzeTrends(data, requirements) {
    console.log(`📈 Analyzing trends...`)

    try {
      const trends = this.identifyTrends(data)
      const projections = this.generateProjections(trends, requirements)

      return {
        success: true,
        type: 'trend_analysis',
        data,
        trends,
        projections,
        insights: this.generateTrendInsights(trends, projections),
        confidence: this.calculateTrendConfidence(trends)
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'trend_analysis'
      }
    }
  }

  // Helper methods for analysis agent
  async generateDataAnalysis(data, requirements, reasoningResult) {
    const prompt = `Analyze this data comprehensively:

Data: ${JSON.stringify(data, null, 2)}
Requirements: ${JSON.stringify(requirements)}
Reasoning approach: ${reasoningResult.useReasoning ? 'systematic analysis' : 'direct analysis'}

Provide:
1. Key insights and findings
2. Patterns and correlations
3. Statistical summary
4. Recommendations based on analysis
5. Confidence level in findings`

    try {
      const response = await geminiService.generateResponse(prompt, 'analysis')

      return {
        content: response.content,
        insights: this.extractInsights(response.content),
        patterns: this.extractPatterns(response.content),
        recommendations: this.extractRecommendations(response.content),
        confidence: this.extractConfidence(response.content)
      }
    } catch (error) {
      return {
        content: 'Analysis could not be completed',
        insights: [],
        patterns: [],
        recommendations: [],
        confidence: 0
      }
    }
  }

  identifyPatterns(data) {
    const patterns = []

    if (Array.isArray(data)) {
      // Look for numerical patterns
      if (data.every(item => typeof item === 'number')) {
        patterns.push(...this.findNumericalPatterns(data))
      }

      // Look for sequence patterns
      patterns.push(...this.findSequencePatterns(data))

      // Look for frequency patterns
      patterns.push(...this.findFrequencyPatterns(data))
    }

    return patterns
  }

  findNumericalPatterns(numbers) {
    const patterns = []

    // Check for arithmetic progression
    if (numbers.length >= 3) {
      const differences = []
      for (let i = 1; i < numbers.length; i++) {
        differences.push(numbers[i] - numbers[i-1])
      }

      if (differences.every(diff => diff === differences[0])) {
        patterns.push({
          type: 'arithmetic_progression',
          description: `Arithmetic sequence with common difference ${differences[0]}`,
          confidence: 0.9
        })
      }
    }

    // Check for geometric progression
    if (numbers.length >= 3 && numbers.every(n => n !== 0)) {
      const ratios = []
      for (let i = 1; i < numbers.length; i++) {
        ratios.push(numbers[i] / numbers[i-1])
      }

      if (ratios.every(ratio => Math.abs(ratio - ratios[0]) < 0.01)) {
        patterns.push({
          type: 'geometric_progression',
          description: `Geometric sequence with common ratio ${ratios[0].toFixed(2)}`,
          confidence: 0.9
        })
      }
    }

    return patterns
  }

  findSequencePatterns(data) {
    const patterns = []

    // Look for repeating subsequences
    if (data.length >= 6) {
      for (let len = 2; len <= data.length / 2; len++) {
        const subsequence = data.slice(0, len)
        let isRepeating = true

        for (let i = len; i < data.length; i += len) {
          const nextSubseq = data.slice(i, i + len)
          if (JSON.stringify(nextSubseq) !== JSON.stringify(subsequence)) {
            isRepeating = false
            break
          }
        }

        if (isRepeating) {
          patterns.push({
            type: 'repeating_sequence',
            description: `Repeating pattern: ${JSON.stringify(subsequence)}`,
            confidence: 0.8
          })
          break
        }
      }
    }

    return patterns
  }

  findFrequencyPatterns(data) {
    const patterns = []
    const frequency = {}

    data.forEach(item => {
      const key = JSON.stringify(item)
      frequency[key] = (frequency[key] || 0) + 1
    })

    const entries = Object.entries(frequency).sort((a, b) => b[1] - a[1])

    if (entries.length > 0) {
      const mostFrequent = entries[0]
      patterns.push({
        type: 'frequency_pattern',
        description: `Most frequent item: ${mostFrequent[0]} (appears ${mostFrequent[1]} times)`,
        confidence: 0.7
      })
    }

    return patterns
  }

  assessPatternSignificance(patterns, data) {
    return patterns.map(pattern => {
      let significance = pattern.confidence || 0.5

      // Boost significance for strong mathematical patterns
      if (pattern.type === 'arithmetic_progression' || pattern.type === 'geometric_progression') {
        significance += 0.2
      }

      // Boost significance for patterns in larger datasets
      if (data.length > 10) {
        significance += 0.1
      }

      return Math.min(1.0, significance)
    })
  }

  generatePatternSummary(patterns) {
    if (patterns.length === 0) {
      return 'No significant patterns detected in the data.'
    }

    const strongPatterns = patterns.filter(p => (p.confidence || 0) > 0.7)

    if (strongPatterns.length > 0) {
      return `Found ${strongPatterns.length} strong pattern(s): ${strongPatterns.map(p => p.type).join(', ')}`
    }

    return `Found ${patterns.length} potential pattern(s) with varying confidence levels.`
  }

  calculatePatternConfidence(patterns) {
    if (patterns.length === 0) return 0

    const avgConfidence = patterns.reduce((sum, p) => sum + (p.confidence || 0), 0) / patterns.length
    return Math.round(avgConfidence * 100)
  }

  async generateLogicalAnalysis(question, reasoningResult, requirements) {
    const prompt = `Perform logical analysis of this question:

Question: ${question}
Reasoning steps: ${JSON.stringify(reasoningResult.reasoningSteps)}

Provide:
1. Identify premises and assumptions
2. Evaluate logical validity
3. Draw conclusions
4. Assess confidence in reasoning
5. Identify any logical fallacies`

    try {
      const response = await geminiService.generateResponse(prompt, 'logical_reasoning')

      return {
        conclusion: this.extractConclusion(response.content),
        premises: this.extractPremises(response.content),
        validity: this.assessLogicalValidity(response.content),
        confidence: this.extractConfidence(response.content)
      }
    } catch (error) {
      return {
        conclusion: 'Unable to complete logical analysis',
        premises: [],
        validity: 'unknown',
        confidence: 0
      }
    }
  }

  breakdownProblem(problem, requirements) {
    return {
      main_problem: problem,
      sub_problems: this.identifySubProblems(problem),
      constraints: requirements.constraints || [],
      objectives: requirements.objectives || ['solve the problem'],
      complexity: this.assessProblemComplexity(problem)
    }
  }

  identifySubProblems(problem) {
    // Simple heuristic to identify sub-problems
    const subProblems = []

    // Look for "and" conjunctions
    const andParts = problem.split(/\s+and\s+/i)
    if (andParts.length > 1) {
      subProblems.push(...andParts.map(part => part.trim()))
    }

    // Look for step indicators
    const stepMatches = problem.match(/step\s+\d+[:\.]?\s*([^.]+)/gi)
    if (stepMatches) {
      subProblems.push(...stepMatches.map(match => match.replace(/step\s+\d+[:\.]?\s*/i, '')))
    }

    // If no sub-problems found, treat as single problem
    if (subProblems.length === 0) {
      subProblems.push(problem)
    }

    return subProblems
  }

  assessProblemComplexity(problem) {
    let complexity = 'simple'

    if (problem.length > 100) complexity = 'medium'
    if (problem.length > 200) complexity = 'complex'

    // Check for complexity indicators
    const complexityIndicators = ['multiple', 'various', 'complex', 'comprehensive', 'detailed']
    if (complexityIndicators.some(indicator => problem.toLowerCase().includes(indicator))) {
      complexity = 'complex'
    }

    return complexity
  }

  async generateSolutionApproach(problemBreakdown, context) {
    const prompt = `Generate a solution approach for this problem:

Problem: ${problemBreakdown.main_problem}
Sub-problems: ${JSON.stringify(problemBreakdown.sub_problems)}
Constraints: ${JSON.stringify(problemBreakdown.constraints)}
Context: ${JSON.stringify(context)}

Provide:
1. Step-by-step solution approach
2. Required resources or information
3. Potential challenges and mitigation strategies
4. Success criteria`

    try {
      const response = await geminiService.generateResponse(prompt, 'problem_solving')

      return {
        steps: this.extractSolutionSteps(response.content),
        resources: this.extractRequiredResources(response.content),
        challenges: this.extractChallenges(response.content),
        success_criteria: this.extractSuccessCriteria(response.content)
      }
    } catch (error) {
      return {
        steps: ['Analyze problem', 'Generate solution', 'Verify result'],
        resources: [],
        challenges: [],
        success_criteria: ['Problem is solved']
      }
    }
  }

  async executeSolutionSteps(approach, context) {
    const results = []

    for (const step of approach.steps) {
      const stepResult = await this.executeStep(step, context)
      results.push(stepResult)
    }

    return {
      steps_executed: results,
      final_result: this.synthesizeResults(results),
      confidence: this.calculateSolutionConfidence(results)
    }
  }

  async executeStep(step, context) {
    // Simplified step execution
    return {
      step,
      status: 'completed',
      result: `Executed: ${step}`,
      timestamp: new Date().toISOString()
    }
  }

  synthesizeResults(results) {
    return `Completed ${results.length} solution steps: ${results.map(r => r.step).join(', ')}`
  }

  calculateSolutionConfidence(results) {
    const completedSteps = results.filter(r => r.status === 'completed').length
    return Math.round((completedSteps / results.length) * 100)
  }

  verifySolution(solution, originalProblem) {
    return {
      verified: true,
      explanation: 'Solution addresses the original problem',
      confidence: solution.confidence || 75
    }
  }

  identifyTrends(data) {
    const trends = []

    if (Array.isArray(data) && data.length >= 3) {
      // Simple trend analysis for numerical data
      if (data.every(item => typeof item === 'number')) {
        const isIncreasing = data.every((val, i) => i === 0 || val >= data[i-1])
        const isDecreasing = data.every((val, i) => i === 0 || val <= data[i-1])

        if (isIncreasing && !isDecreasing) {
          trends.push({ type: 'increasing', strength: 'strong' })
        } else if (isDecreasing && !isIncreasing) {
          trends.push({ type: 'decreasing', strength: 'strong' })
        } else {
          trends.push({ type: 'fluctuating', strength: 'moderate' })
        }
      }
    }

    return trends
  }

  generateProjections(trends, requirements) {
    return trends.map(trend => ({
      trend: trend.type,
      projection: `Based on ${trend.type} trend, expect continuation of pattern`,
      confidence: trend.strength === 'strong' ? 0.8 : 0.6,
      timeframe: requirements.timeframe || 'short-term'
    }))
  }

  generateTrendInsights(trends, projections) {
    return trends.map((trend, index) => ({
      insight: `${trend.type} trend detected with ${trend.strength} strength`,
      implication: projections[index]?.projection || 'No clear projection available',
      recommendation: this.generateTrendRecommendation(trend)
    }))
  }

  generateTrendRecommendation(trend) {
    switch (trend.type) {
      case 'increasing':
        return 'Monitor for potential plateau or reversal points'
      case 'decreasing':
        return 'Investigate causes and consider intervention strategies'
      case 'fluctuating':
        return 'Look for underlying patterns or cyclical behavior'
      default:
        return 'Continue monitoring for trend development'
    }
  }

  calculateTrendConfidence(trends) {
    if (trends.length === 0) return 0

    const strengthScores = trends.map(t => t.strength === 'strong' ? 0.9 : t.strength === 'moderate' ? 0.6 : 0.3)
    const avgStrength = strengthScores.reduce((sum, score) => sum + score, 0) / strengthScores.length

    return Math.round(avgStrength * 100)
  }

  // Utility methods for extracting information from AI responses
  extractInsights(content) {
    const insights = []
    const insightPatterns = [
      /insight[s]?[:\s]*(.+?)(?:\n|$)/gi,
      /finding[s]?[:\s]*(.+?)(?:\n|$)/gi,
      /key[:\s]*(.+?)(?:\n|$)/gi
    ]

    insightPatterns.forEach(pattern => {
      const matches = [...content.matchAll(pattern)]
      matches.forEach(match => insights.push(match[1].trim()))
    })

    return insights.slice(0, 5)
  }

  extractPatterns(content) {
    const patterns = []
    const patternMatches = content.match(/pattern[s]?[:\s]*(.+?)(?:\n|$)/gi)
    if (patternMatches) {
      patterns.push(...patternMatches.map(match => match.replace(/pattern[s]?[:\s]*/i, '').trim()))
    }
    return patterns.slice(0, 3)
  }

  extractRecommendations(content) {
    const recommendations = []
    const recMatches = content.match(/recommend[ation]*[s]?[:\s]*(.+?)(?:\n|$)/gi)
    if (recMatches) {
      recommendations.push(...recMatches.map(match => match.replace(/recommend[ation]*[s]?[:\s]*/i, '').trim()))
    }
    return recommendations.slice(0, 3)
  }

  extractConfidence(content) {
    const confidenceMatch = content.match(/confidence[:\s]*(\d+)/i)
    return confidenceMatch ? parseInt(confidenceMatch[1]) : 75
  }

  extractConclusion(content) {
    const conclusionMatch = content.match(/conclusion[:\s]*(.+?)(?:\n\n|\n[A-Z]|$)/is)
    return conclusionMatch ? conclusionMatch[1].trim() : 'No clear conclusion found'
  }

  extractPremises(content) {
    const premises = []
    const premiseMatches = content.match(/premise[s]?[:\s]*(.+?)(?:\n|$)/gi)
    if (premiseMatches) {
      premises.push(...premiseMatches.map(match => match.replace(/premise[s]?[:\s]*/i, '').trim()))
    }
    return premises
  }

  assessLogicalValidity(content) {
    if (/valid/i.test(content) && !/invalid/i.test(content)) return 'valid'
    if (/invalid/i.test(content)) return 'invalid'
    return 'uncertain'
  }

  extractSolutionSteps(content) {
    const steps = []
    const stepMatches = content.match(/(?:^|\n)(?:\d+\.|step\s*\d+)[:\.]?\s*(.+)/gmi)
    if (stepMatches) {
      steps.push(...stepMatches.map(match => match.replace(/(?:^|\n)(?:\d+\.|step\s*\d+)[:\.]?\s*/i, '').trim()))
    }
    return steps.length > 0 ? steps : ['Analyze problem', 'Generate solution', 'Verify result']
  }

  extractRequiredResources(content) {
    const resources = []
    const resourceMatches = content.match(/resource[s]?[:\s]*(.+?)(?:\n|$)/gi)
    if (resourceMatches) {
      resources.push(...resourceMatches.map(match => match.replace(/resource[s]?[:\s]*/i, '').trim()))
    }
    return resources
  }

  extractChallenges(content) {
    const challenges = []
    const challengeMatches = content.match(/challenge[s]?[:\s]*(.+?)(?:\n|$)/gi)
    if (challengeMatches) {
      challenges.push(...challengeMatches.map(match => match.replace(/challenge[s]?[:\s]*/i, '').trim()))
    }
    return challenges
  }

  extractSuccessCriteria(content) {
    const criteria = []
    const criteriaMatches = content.match(/(?:success|criteria)[:\s]*(.+?)(?:\n|$)/gi)
    if (criteriaMatches) {
      criteria.push(...criteriaMatches.map(match => match.replace(/(?:success|criteria)[:\s]*/i, '').trim()))
    }
    return criteria.length > 0 ? criteria : ['Problem is solved effectively']
  }

  async performGeneralAnalysis(data, requirements) {
    // Default to data analysis for general analysis tasks
    return await this.analyzeData(data, requirements)
  }
}

// Create singleton instance
const specializedAgents = new SpecializedAgents()
export default specializedAgents