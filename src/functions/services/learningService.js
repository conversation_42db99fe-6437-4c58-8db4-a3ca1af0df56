// Learning Service - Adaptive behavior and user feedback processing
import modelAdaptationService from './modelAdaptation.js'

class LearningService {
  constructor() {
    this.name = 'learning_service'
    this.version = '1.0.0'
    this.storagePrefix = 'agi_learning_'
    this.feedbackHistory = []
    this.userProfile = null
    this.adaptationRules = new Map()
    this.learningMetrics = {
      totalFeedback: 0,
      averageRating: 0,
      improvementTrend: 'stable',
      lastUpdated: null
    }
  }

  // Initialize learning service
  async initialize() {
    console.log('🧠 Initializing Learning Service...')

    try {
      // Load existing feedback history
      await this.loadFeedbackHistory()

      // Load user profile
      await this.loadUserProfile()

      // Load adaptation rules
      await this.loadAdaptationRules()

      // Initialize model adaptation service
      await modelAdaptationService.initialize()

      // Update learning metrics
      this.updateLearningMetrics()

      console.log('✅ Learning Service initialized successfully')
      console.log(`📊 Loaded ${this.feedbackHistory.length} feedback entries`)

      return true
    } catch (error) {
      console.error('❌ Failed to initialize Learning Service:', error)
      return false
    }
  }

  // Store user feedback
  async storeFeedback(feedbackData) {
    console.log('📝 Storing user feedback...', feedbackData.rating)
    
    try {
      // Add metadata
      const enrichedFeedback = {
        ...feedbackData,
        id: this.generateFeedbackId(),
        processed: false,
        learningImpact: 0, // Will be calculated during processing
        adaptationTriggers: []
      }
      
      // Store in memory
      this.feedbackHistory.push(enrichedFeedback)
      
      // Persist to localStorage
      await this.saveFeedbackHistory()
      
      // Process feedback for immediate learning
      await this.processFeedback(enrichedFeedback)
      
      // Update metrics
      this.updateLearningMetrics()
      
      console.log('✅ Feedback stored and processed successfully')
      return { success: true, feedbackId: enrichedFeedback.id }
      
    } catch (error) {
      console.error('❌ Failed to store feedback:', error)
      return { success: false, error: error.message }
    }
  }

  // Process feedback for learning
  async processFeedback(feedback) {
    console.log('🔄 Processing feedback for learning insights...')
    
    try {
      const insights = {
        qualityIssues: [],
        strengthAreas: [],
        adaptationNeeded: [],
        userPreferences: {}
      }

      // Analyze rating patterns
      if (feedback.rating <= 2) {
        insights.qualityIssues.push('Low overall satisfaction')
        insights.adaptationNeeded.push('improve_response_quality')
      } else if (feedback.rating >= 4) {
        insights.strengthAreas.push('High user satisfaction')
      }

      // Analyze specific aspects
      Object.entries(feedback.aspects || {}).forEach(([aspect, rating]) => {
        if (rating <= 2) {
          insights.qualityIssues.push(`Poor ${aspect}`)
          insights.adaptationNeeded.push(`improve_${aspect}`)
        } else if (rating >= 4) {
          insights.strengthAreas.push(`Strong ${aspect}`)
        }
      })

      // Analyze feedback text for patterns
      if (feedback.feedbackText) {
        const textInsights = this.analyzeTextFeedback(feedback.feedbackText)
        insights.qualityIssues.push(...textInsights.issues)
        insights.userPreferences = { ...insights.userPreferences, ...textInsights.preferences }
      }

      // Analyze reasoning effectiveness
      if (feedback.reasoningUsed) {
        if (feedback.aspects?.reasoning <= 2) {
          insights.adaptationNeeded.push('improve_reasoning_clarity')
        } else if (feedback.aspects?.reasoning >= 4) {
          insights.strengthAreas.push('Effective reasoning process')
        }
      }

      // Update adaptation rules based on insights
      await this.updateAdaptationRules(insights)
      
      // Update user profile
      await this.updateUserProfile(feedback, insights)

      // Process feedback for model adaptation
      const adaptationResult = await modelAdaptationService.processFeedbackForAdaptation(
        feedback,
        feedback.reasoningData
      )

      // Mark feedback as processed
      feedback.processed = true
      feedback.learningImpact = this.calculateLearningImpact(insights)
      feedback.adaptationTriggers = insights.adaptationNeeded
      feedback.modelAdaptation = adaptationResult

      console.log('✅ Feedback processing complete:', insights)
      return insights
      
    } catch (error) {
      console.error('❌ Failed to process feedback:', error)
      return null
    }
  }

  // Analyze text feedback for patterns
  analyzeTextFeedback(text) {
    const insights = {
      issues: [],
      preferences: {}
    }
    
    const textLower = text.toLowerCase()
    
    // Common issue patterns
    const issuePatterns = {
      'too long': 'verbose_responses',
      'too short': 'brief_responses',
      'unclear': 'clarity_issues',
      'wrong': 'accuracy_issues',
      'confusing': 'complexity_issues',
      'incomplete': 'completeness_issues',
      'irrelevant': 'relevance_issues'
    }
    
    Object.entries(issuePatterns).forEach(([pattern, issue]) => {
      if (textLower.includes(pattern)) {
        insights.issues.push(issue)
      }
    })
    
    // Preference patterns
    const preferencePatterns = {
      'more examples': { examples: 'preferred' },
      'step by step': { format: 'step_by_step' },
      'simpler': { complexity: 'simple' },
      'detailed': { detail_level: 'high' },
      'concise': { detail_level: 'low' },
      'technical': { style: 'technical' },
      'casual': { style: 'casual' }
    }
    
    Object.entries(preferencePatterns).forEach(([pattern, preference]) => {
      if (textLower.includes(pattern)) {
        Object.assign(insights.preferences, preference)
      }
    })
    
    return insights
  }

  // Update adaptation rules based on learning
  async updateAdaptationRules(insights) {
    console.log('🔧 Updating adaptation rules...')
    
    try {
      // Process adaptation needs
      insights.adaptationNeeded.forEach(adaptation => {
        const currentRule = this.adaptationRules.get(adaptation) || {
          weight: 0,
          frequency: 0,
          lastTriggered: null,
          effectiveness: 0.5
        }
        
        currentRule.frequency += 1
        currentRule.weight = Math.min(1.0, currentRule.weight + 0.1)
        currentRule.lastTriggered = new Date().toISOString()
        
        this.adaptationRules.set(adaptation, currentRule)
      })
      
      // Decay unused rules
      this.adaptationRules.forEach((rule, key) => {
        const daysSinceTriggered = rule.lastTriggered 
          ? (Date.now() - new Date(rule.lastTriggered).getTime()) / (1000 * 60 * 60 * 24)
          : 30
        
        if (daysSinceTriggered > 7) {
          rule.weight = Math.max(0, rule.weight - 0.05)
        }
      })
      
      // Save updated rules
      await this.saveAdaptationRules()
      
      console.log('✅ Adaptation rules updated')
      
    } catch (error) {
      console.error('❌ Failed to update adaptation rules:', error)
    }
  }

  // Update user profile based on feedback
  async updateUserProfile(feedback, insights) {
    console.log('👤 Updating user profile...')
    
    try {
      if (!this.userProfile) {
        this.userProfile = {
          preferences: {},
          patterns: {},
          satisfactionHistory: [],
          learningProgress: {
            totalInteractions: 0,
            averageRating: 0,
            improvementAreas: [],
            strengths: []
          },
          lastUpdated: new Date().toISOString()
        }
      }
      
      // Update satisfaction history
      this.userProfile.satisfactionHistory.push({
        timestamp: feedback.timestamp,
        rating: feedback.rating,
        aspects: feedback.aspects,
        type: feedback.type
      })
      
      // Keep only last 50 entries
      if (this.userProfile.satisfactionHistory.length > 50) {
        this.userProfile.satisfactionHistory = this.userProfile.satisfactionHistory.slice(-50)
      }
      
      // Update preferences from insights
      Object.assign(this.userProfile.preferences, insights.userPreferences)
      
      // Update learning progress
      this.userProfile.learningProgress.totalInteractions += 1
      this.userProfile.learningProgress.averageRating = this.calculateAverageRating()
      this.userProfile.learningProgress.improvementAreas = this.getTopImprovementAreas()
      this.userProfile.learningProgress.strengths = insights.strengthAreas.slice(0, 5)
      this.userProfile.lastUpdated = new Date().toISOString()
      
      // Save updated profile
      await this.saveUserProfile()
      
      console.log('✅ User profile updated')
      
    } catch (error) {
      console.error('❌ Failed to update user profile:', error)
    }
  }

  // Get adaptive behavior suggestions
  getAdaptiveBehaviorSuggestions(context = {}) {
    const suggestions = {
      responseStyle: 'balanced',
      detailLevel: 'medium',
      includeExamples: false,
      useReasoning: 'auto',
      formatPreference: 'structured',
      adaptations: []
    }

    // Get model adaptation parameters
    const adaptationParams = modelAdaptationService.getAdaptationParameters()
    if (adaptationParams.hasAdaptations) {
      suggestions.modelAdaptation = adaptationParams
      suggestions.adaptations.push('Model fine-tuning active')
    }
    
    // Apply adaptation rules
    this.adaptationRules.forEach((rule, adaptation) => {
      if (rule.weight > 0.3) { // Only apply significant rules
        switch (adaptation) {
          case 'improve_clarity':
            suggestions.responseStyle = 'clear'
            suggestions.includeExamples = true
            suggestions.adaptations.push('Enhanced clarity focus')
            break
            
          case 'improve_completeness':
            suggestions.detailLevel = 'high'
            suggestions.formatPreference = 'comprehensive'
            suggestions.adaptations.push('More comprehensive responses')
            break
            
          case 'improve_reasoning_clarity':
            suggestions.useReasoning = 'enhanced'
            suggestions.adaptations.push('Clearer reasoning explanations')
            break
            
          case 'verbose_responses':
            suggestions.detailLevel = 'low'
            suggestions.responseStyle = 'concise'
            suggestions.adaptations.push('More concise responses')
            break
            
          case 'brief_responses':
            suggestions.detailLevel = 'high'
            suggestions.includeExamples = true
            suggestions.adaptations.push('More detailed responses')
            break
        }
      }
    })
    
    // Apply user preferences
    if (this.userProfile?.preferences) {
      const prefs = this.userProfile.preferences
      
      if (prefs.detail_level === 'high') suggestions.detailLevel = 'high'
      if (prefs.detail_level === 'low') suggestions.detailLevel = 'low'
      if (prefs.style === 'technical') suggestions.responseStyle = 'technical'
      if (prefs.style === 'casual') suggestions.responseStyle = 'casual'
      if (prefs.format === 'step_by_step') suggestions.formatPreference = 'step_by_step'
      if (prefs.examples === 'preferred') suggestions.includeExamples = true
    }
    
    return suggestions
  }

  // Calculate learning impact score
  calculateLearningImpact(insights) {
    let impact = 0
    
    // Quality issues have high learning impact
    impact += insights.qualityIssues.length * 0.3
    
    // Adaptation needs have medium impact
    impact += insights.adaptationNeeded.length * 0.2
    
    // User preferences have low but consistent impact
    impact += Object.keys(insights.userPreferences).length * 0.1
    
    return Math.min(1.0, impact)
  }

  // Calculate average rating
  calculateAverageRating() {
    if (this.feedbackHistory.length === 0) return 0
    
    const totalRating = this.feedbackHistory.reduce((sum, feedback) => sum + feedback.rating, 0)
    return Math.round((totalRating / this.feedbackHistory.length) * 10) / 10
  }

  // Get top improvement areas
  getTopImprovementAreas() {
    const areas = new Map()
    
    this.adaptationRules.forEach((rule, adaptation) => {
      if (rule.weight > 0.2) {
        areas.set(adaptation, rule.weight)
      }
    })
    
    return Array.from(areas.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([area]) => area)
  }

  // Update learning metrics
  updateLearningMetrics() {
    this.learningMetrics = {
      totalFeedback: this.feedbackHistory.length,
      averageRating: this.calculateAverageRating(),
      improvementTrend: this.calculateImprovementTrend(),
      lastUpdated: new Date().toISOString(),
      adaptationRulesCount: this.adaptationRules.size,
      activeAdaptations: Array.from(this.adaptationRules.entries())
        .filter(([_, rule]) => rule.weight > 0.3)
        .length
    }
  }

  // Calculate improvement trend
  calculateImprovementTrend() {
    if (this.feedbackHistory.length < 10) return 'insufficient_data'
    
    const recent = this.feedbackHistory.slice(-10)
    const older = this.feedbackHistory.slice(-20, -10)
    
    if (older.length === 0) return 'stable'
    
    const recentAvg = recent.reduce((sum, f) => sum + f.rating, 0) / recent.length
    const olderAvg = older.reduce((sum, f) => sum + f.rating, 0) / older.length
    
    const difference = recentAvg - olderAvg
    
    if (difference > 0.3) return 'improving'
    if (difference < -0.3) return 'declining'
    return 'stable'
  }

  // Storage methods
  async loadFeedbackHistory() {
    try {
      const stored = localStorage.getItem(this.storagePrefix + 'feedback_history')
      this.feedbackHistory = stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('Failed to load feedback history:', error)
      this.feedbackHistory = []
    }
  }

  async saveFeedbackHistory() {
    try {
      localStorage.setItem(
        this.storagePrefix + 'feedback_history', 
        JSON.stringify(this.feedbackHistory)
      )
    } catch (error) {
      console.error('Failed to save feedback history:', error)
    }
  }

  async loadUserProfile() {
    try {
      const stored = localStorage.getItem(this.storagePrefix + 'user_profile')
      this.userProfile = stored ? JSON.parse(stored) : null
    } catch (error) {
      console.error('Failed to load user profile:', error)
      this.userProfile = null
    }
  }

  async saveUserProfile() {
    try {
      localStorage.setItem(
        this.storagePrefix + 'user_profile', 
        JSON.stringify(this.userProfile)
      )
    } catch (error) {
      console.error('Failed to save user profile:', error)
    }
  }

  async loadAdaptationRules() {
    try {
      const stored = localStorage.getItem(this.storagePrefix + 'adaptation_rules')
      const rulesArray = stored ? JSON.parse(stored) : []
      this.adaptationRules = new Map(rulesArray)
    } catch (error) {
      console.error('Failed to load adaptation rules:', error)
      this.adaptationRules = new Map()
    }
  }

  async saveAdaptationRules() {
    try {
      const rulesArray = Array.from(this.adaptationRules.entries())
      localStorage.setItem(
        this.storagePrefix + 'adaptation_rules', 
        JSON.stringify(rulesArray)
      )
    } catch (error) {
      console.error('Failed to save adaptation rules:', error)
    }
  }

  // Utility methods
  generateFeedbackId() {
    return `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Get learning statistics
  getLearningStats() {
    return {
      ...this.learningMetrics,
      userProfile: this.userProfile,
      topAdaptations: Array.from(this.adaptationRules.entries())
        .sort((a, b) => b[1].weight - a[1].weight)
        .slice(0, 5)
        .map(([adaptation, rule]) => ({
          adaptation,
          weight: rule.weight,
          frequency: rule.frequency
        })),
      recentFeedback: this.feedbackHistory.slice(-10)
    }
  }

  // Get detailed performance analytics
  getPerformanceAnalytics() {
    const analytics = {
      overview: this.getOverviewMetrics(),
      trends: this.getTrendAnalysis(),
      aspectAnalysis: this.getAspectAnalysis(),
      adaptationEffectiveness: this.getAdaptationEffectiveness(),
      userSatisfactionTrends: this.getUserSatisfactionTrends(),
      improvementRecommendations: this.getImprovementRecommendations()
    }

    return analytics
  }

  // Get overview metrics
  getOverviewMetrics() {
    const totalFeedback = this.feedbackHistory.length
    const averageRating = this.calculateAverageRating()
    const recentFeedback = this.feedbackHistory.slice(-10)
    const recentAverage = recentFeedback.length > 0
      ? recentFeedback.reduce((sum, f) => sum + f.rating, 0) / recentFeedback.length
      : 0

    return {
      totalFeedback,
      averageRating,
      recentAverage: Math.round(recentAverage * 10) / 10,
      improvementTrend: this.calculateImprovementTrend(),
      activeAdaptations: Array.from(this.adaptationRules.entries())
        .filter(([_, rule]) => rule.weight > 0.3).length,
      totalAdaptations: this.adaptationRules.size
    }
  }

  // Get trend analysis
  getTrendAnalysis() {
    if (this.feedbackHistory.length < 5) {
      return { insufficient_data: true }
    }

    const periods = this.groupFeedbackByPeriod()
    const trends = {
      daily: this.calculatePeriodTrends(periods.daily),
      weekly: this.calculatePeriodTrends(periods.weekly),
      overall: this.calculateOverallTrend()
    }

    return trends
  }

  // Group feedback by time periods
  groupFeedbackByPeriod() {
    const now = new Date()
    const daily = {}
    const weekly = {}

    this.feedbackHistory.forEach(feedback => {
      const date = new Date(feedback.timestamp)
      const dayKey = date.toISOString().split('T')[0]
      const weekKey = this.getWeekKey(date)

      if (!daily[dayKey]) daily[dayKey] = []
      if (!weekly[weekKey]) weekly[weekKey] = []

      daily[dayKey].push(feedback)
      weekly[weekKey].push(feedback)
    })

    return { daily, weekly }
  }

  // Calculate trends for a period
  calculatePeriodTrends(periodData) {
    const periods = Object.keys(periodData).sort()
    if (periods.length < 2) return { trend: 'insufficient_data' }

    const averages = periods.map(period => {
      const feedback = periodData[period]
      return feedback.reduce((sum, f) => sum + f.rating, 0) / feedback.length
    })

    const recent = averages.slice(-3)
    const older = averages.slice(0, -3)

    if (older.length === 0) return { trend: 'stable' }

    const recentAvg = recent.reduce((sum, avg) => sum + avg, 0) / recent.length
    const olderAvg = older.reduce((sum, avg) => sum + avg, 0) / older.length

    const change = recentAvg - olderAvg

    return {
      trend: change > 0.2 ? 'improving' : change < -0.2 ? 'declining' : 'stable',
      change: Math.round(change * 100) / 100,
      recentAverage: Math.round(recentAvg * 10) / 10,
      previousAverage: Math.round(olderAvg * 10) / 10
    }
  }

  // Get aspect analysis
  getAspectAnalysis() {
    const aspects = ['accuracy', 'clarity', 'completeness', 'reasoning', 'usefulness']
    const analysis = {}

    aspects.forEach(aspect => {
      const ratings = this.feedbackHistory
        .filter(f => f.aspects && f.aspects[aspect])
        .map(f => f.aspects[aspect])

      if (ratings.length > 0) {
        const average = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length
        const recent = ratings.slice(-10)
        const recentAverage = recent.length > 0
          ? recent.reduce((sum, rating) => sum + rating, 0) / recent.length
          : average

        analysis[aspect] = {
          average: Math.round(average * 10) / 10,
          recentAverage: Math.round(recentAverage * 10) / 10,
          trend: recentAverage > average ? 'improving' : recentAverage < average ? 'declining' : 'stable',
          totalRatings: ratings.length
        }
      }
    })

    return analysis
  }

  // Get adaptation effectiveness
  getAdaptationEffectiveness() {
    const effectiveness = {}

    this.adaptationRules.forEach((rule, adaptation) => {
      // Find feedback after this adaptation was triggered
      const adaptationFeedback = this.feedbackHistory.filter(feedback =>
        feedback.adaptationTriggers && feedback.adaptationTriggers.includes(adaptation)
      )

      if (adaptationFeedback.length > 0) {
        const avgRating = adaptationFeedback.reduce((sum, f) => sum + f.rating, 0) / adaptationFeedback.length
        const improvementRate = adaptationFeedback.filter(f => f.rating >= 4).length / adaptationFeedback.length

        effectiveness[adaptation] = {
          averageRating: Math.round(avgRating * 10) / 10,
          improvementRate: Math.round(improvementRate * 100),
          totalApplications: adaptationFeedback.length,
          weight: rule.weight,
          frequency: rule.frequency
        }
      }
    })

    return effectiveness
  }

  // Get user satisfaction trends
  getUserSatisfactionTrends() {
    if (this.feedbackHistory.length < 5) {
      return { insufficient_data: true }
    }

    const last30Days = this.feedbackHistory.filter(feedback => {
      const feedbackDate = new Date(feedback.timestamp)
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      return feedbackDate >= thirtyDaysAgo
    })

    const satisfactionLevels = {
      very_satisfied: last30Days.filter(f => f.rating >= 5).length,
      satisfied: last30Days.filter(f => f.rating === 4).length,
      neutral: last30Days.filter(f => f.rating === 3).length,
      dissatisfied: last30Days.filter(f => f.rating === 2).length,
      very_dissatisfied: last30Days.filter(f => f.rating === 1).length
    }

    const total = last30Days.length
    const percentages = {}
    Object.keys(satisfactionLevels).forEach(level => {
      percentages[level] = total > 0 ? Math.round((satisfactionLevels[level] / total) * 100) : 0
    })

    return {
      counts: satisfactionLevels,
      percentages,
      totalResponses: total,
      period: 'last_30_days'
    }
  }

  // Get improvement recommendations
  getImprovementRecommendations() {
    const recommendations = []
    const aspectAnalysis = this.getAspectAnalysis()

    // Check for low-performing aspects
    Object.entries(aspectAnalysis).forEach(([aspect, data]) => {
      if (data.average < 3.5) {
        recommendations.push({
          priority: 'high',
          category: 'aspect_improvement',
          aspect,
          issue: `${aspect} ratings are below average (${data.average}/5)`,
          suggestion: this.getAspectImprovementSuggestion(aspect),
          impact: 'high'
        })
      } else if (data.trend === 'declining') {
        recommendations.push({
          priority: 'medium',
          category: 'trend_concern',
          aspect,
          issue: `${aspect} ratings are declining`,
          suggestion: `Monitor and address ${aspect} quality in responses`,
          impact: 'medium'
        })
      }
    })

    // Check adaptation effectiveness
    const adaptationEffectiveness = this.getAdaptationEffectiveness()
    Object.entries(adaptationEffectiveness).forEach(([adaptation, data]) => {
      if (data.averageRating < 3.5 && data.totalApplications > 3) {
        recommendations.push({
          priority: 'medium',
          category: 'adaptation_ineffective',
          adaptation,
          issue: `Adaptation "${adaptation}" is not improving satisfaction`,
          suggestion: 'Review and adjust adaptation strategy',
          impact: 'medium'
        })
      }
    })

    // Check overall satisfaction
    const overview = this.getOverviewMetrics()
    if (overview.averageRating < 3.5) {
      recommendations.push({
        priority: 'high',
        category: 'overall_satisfaction',
        issue: 'Overall user satisfaction is below acceptable levels',
        suggestion: 'Focus on improving response quality and user experience',
        impact: 'high'
      })
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
  }

  // Get aspect improvement suggestion
  getAspectImprovementSuggestion(aspect) {
    const suggestions = {
      accuracy: 'Improve fact-checking and validation of information provided',
      clarity: 'Use simpler language and better structure in responses',
      completeness: 'Ensure all parts of questions are addressed thoroughly',
      reasoning: 'Provide clearer explanations of thought processes',
      usefulness: 'Focus on practical, actionable information'
    }

    return suggestions[aspect] || 'Review and improve this aspect of responses'
  }

  // Utility methods for analytics
  getWeekKey(date) {
    const year = date.getFullYear()
    const week = this.getWeekNumber(date)
    return `${year}-W${week}`
  }

  getWeekNumber(date) {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()))
    const dayNum = d.getUTCDay() || 7
    d.setUTCDate(d.getUTCDate() + 4 - dayNum)
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1))
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7)
  }

  calculateOverallTrend() {
    if (this.feedbackHistory.length < 10) {
      return { trend: 'insufficient_data' }
    }

    const recent = this.feedbackHistory.slice(-10)
    const older = this.feedbackHistory.slice(-20, -10)

    if (older.length === 0) {
      return { trend: 'stable' }
    }

    const recentAvg = recent.reduce((sum, f) => sum + f.rating, 0) / recent.length
    const olderAvg = older.reduce((sum, f) => sum + f.rating, 0) / older.length

    const change = recentAvg - olderAvg

    return {
      trend: change > 0.2 ? 'improving' : change < -0.2 ? 'declining' : 'stable',
      change: Math.round(change * 100) / 100,
      recentAverage: Math.round(recentAvg * 10) / 10,
      previousAverage: Math.round(olderAvg * 10) / 10,
      confidence: Math.min(recent.length, older.length) / 10
    }
  }

  // Clear learning data
  clearLearningData() {
    this.feedbackHistory = []
    this.userProfile = null
    this.adaptationRules = new Map()
    this.updateLearningMetrics()

    // Clear from storage
    localStorage.removeItem(this.storagePrefix + 'feedback_history')
    localStorage.removeItem(this.storagePrefix + 'user_profile')
    localStorage.removeItem(this.storagePrefix + 'adaptation_rules')

    console.log('🧹 Learning data cleared')
  }
}

// Create singleton instance
const learningService = new LearningService()
export default learningService
