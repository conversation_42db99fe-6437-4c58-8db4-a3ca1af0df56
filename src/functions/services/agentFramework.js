// Agent Framework - Core system for multi-agent collaboration
class AgentFramework {
  constructor() {
    this.name = 'agent_framework'
    this.version = '1.0.0'
    this.agents = new Map()
    this.activeCollaborations = new Map()
    this.communicationLog = []
    this.performanceMetrics = new Map()
  }

  // Initialize the agent framework
  async initialize() {
    console.log('🤖 Initializing Agent Framework...')
    
    try {
      // Register built-in agent types
      this.registerAgentTypes()
      
      // Initialize communication system
      this.initializeCommunication()
      
      console.log('✅ Agent Framework initialized successfully')
      return true
    } catch (error) {
      console.error('❌ Failed to initialize Agent Framework:', error)
      return false
    }
  }

  // Register available agent types
  registerAgentTypes() {
    const agentTypes = {
      'research': {
        name: 'Research Agent',
        description: 'Specializes in information gathering and research tasks',
        capabilities: ['web_search', 'data_analysis', 'fact_checking', 'source_verification'],
        expertise: ['research', 'information_gathering', 'analysis'],
        tools: ['web_search', 'knowledge_base'],
        maxConcurrentTasks: 3
      },
      'coding': {
        name: 'Coding Agent',
        description: 'Specializes in software development and programming tasks',
        capabilities: ['code_generation', 'code_review', 'debugging', 'testing', 'documentation'],
        expertise: ['programming', 'software_development', 'algorithms', 'debugging'],
        tools: ['code_execution', 'file_operations', 'syntax_validation'],
        maxConcurrentTasks: 2
      },
      'analysis': {
        name: 'Analysis Agent',
        description: 'Specializes in data analysis and logical reasoning',
        capabilities: ['data_analysis', 'pattern_recognition', 'logical_reasoning', 'problem_solving'],
        expertise: ['analysis', 'reasoning', 'problem_solving', 'pattern_recognition'],
        tools: ['reasoning_engine', 'data_processing'],
        maxConcurrentTasks: 2
      },
      'creative': {
        name: 'Creative Agent',
        description: 'Specializes in creative tasks and content generation',
        capabilities: ['content_creation', 'creative_writing', 'brainstorming', 'design_thinking'],
        expertise: ['creativity', 'writing', 'ideation', 'storytelling'],
        tools: ['content_generation', 'image_description'],
        maxConcurrentTasks: 2
      },
      'coordinator': {
        name: 'Coordinator Agent',
        description: 'Manages and coordinates multi-agent collaborations',
        capabilities: ['task_coordination', 'agent_management', 'result_synthesis', 'workflow_optimization'],
        expertise: ['coordination', 'management', 'synthesis', 'optimization'],
        tools: ['agent_communication', 'task_management'],
        maxConcurrentTasks: 5
      }
    }

    Object.entries(agentTypes).forEach(([type, config]) => {
      this.registerAgentType(type, config)
    })
  }

  // Register a new agent type
  registerAgentType(type, config) {
    if (this.agents.has(type)) {
      console.warn(`⚠️ Agent type '${type}' already registered, updating...`)
    }
    
    this.agents.set(type, {
      ...config,
      type,
      instances: new Map(),
      totalCreated: 0,
      performanceHistory: []
    })
    
    console.log(`✅ Registered agent type: ${config.name}`)
  }

  // Create a new agent instance
  createAgent(type, instanceId = null, customConfig = {}) {
    const agentType = this.agents.get(type)
    if (!agentType) {
      throw new Error(`Unknown agent type: ${type}`)
    }

    const id = instanceId || `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`
    
    const agent = new Agent({
      id,
      type,
      ...agentType,
      ...customConfig,
      framework: this,
      createdAt: new Date().toISOString(),
      status: 'idle',
      currentTasks: [],
      completedTasks: [],
      collaborations: new Set(),
      performance: {
        tasksCompleted: 0,
        averageCompletionTime: 0,
        successRate: 1.0,
        collaborationRating: 0,
        lastActive: new Date().toISOString()
      }
    })

    agentType.instances.set(id, agent)
    agentType.totalCreated++

    console.log(`🤖 Created ${agentType.name} instance: ${id}`)
    return agent
  }

  // Get agent by ID
  getAgent(agentId) {
    for (const agentType of this.agents.values()) {
      if (agentType.instances.has(agentId)) {
        return agentType.instances.get(agentId)
      }
    }
    return null
  }

  // Get all agents of a specific type
  getAgentsByType(type) {
    const agentType = this.agents.get(type)
    return agentType ? Array.from(agentType.instances.values()) : []
  }

  // Get all active agents
  getActiveAgents() {
    const activeAgents = []
    for (const agentType of this.agents.values()) {
      for (const agent of agentType.instances.values()) {
        if (agent.status !== 'idle') {
          activeAgents.push(agent)
        }
      }
    }
    return activeAgents
  }

  // Find best agent for a task
  findBestAgentForTask(taskType, requirements = {}) {
    let bestAgent = null
    let bestScore = 0

    for (const agentType of this.agents.values()) {
      // Check if agent type can handle this task
      if (!agentType.capabilities.includes(taskType) && !agentType.expertise.includes(taskType)) {
        continue
      }

      for (const agent of agentType.instances.values()) {
        // Skip if agent is at capacity
        if (agent.currentTasks.length >= agent.maxConcurrentTasks) {
          continue
        }

        // Calculate suitability score
        const score = this.calculateAgentSuitability(agent, taskType, requirements)
        
        if (score > bestScore) {
          bestScore = score
          bestAgent = agent
        }
      }
    }

    // Create new agent if none available and type supports it
    if (!bestAgent) {
      const suitableTypes = Array.from(this.agents.entries())
        .filter(([_, type]) => 
          type.capabilities.includes(taskType) || type.expertise.includes(taskType)
        )

      if (suitableTypes.length > 0) {
        const [bestType] = suitableTypes[0]
        bestAgent = this.createAgent(bestType)
      }
    }

    return bestAgent
  }

  // Calculate agent suitability for a task
  calculateAgentSuitability(agent, taskType, requirements) {
    let score = 0

    // Base capability score
    if (agent.capabilities.includes(taskType)) score += 50
    if (agent.expertise.includes(taskType)) score += 30

    // Performance score
    score += agent.performance.successRate * 20

    // Availability score (prefer less busy agents)
    const availabilityRatio = 1 - (agent.currentTasks.length / agent.maxConcurrentTasks)
    score += availabilityRatio * 20

    // Collaboration rating
    score += agent.performance.collaborationRating * 10

    // Requirement matching
    if (requirements.tools) {
      const toolMatch = requirements.tools.filter(tool => agent.tools.includes(tool)).length
      score += (toolMatch / requirements.tools.length) * 15
    }

    return score
  }

  // Initialize communication system
  initializeCommunication() {
    this.communicationChannels = {
      broadcast: new Set(), // All agents
      coordination: new Set(), // Coordinator agents
      taskSpecific: new Map() // Task-specific channels
    }
  }

  // Send message between agents
  sendMessage(fromAgentId, toAgentId, message, channel = 'direct') {
    const timestamp = new Date().toISOString()
    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`

    const communicationMessage = {
      id: messageId,
      from: fromAgentId,
      to: toAgentId,
      channel,
      message,
      timestamp,
      type: message.type || 'info',
      priority: message.priority || 'normal'
    }

    this.communicationLog.push(communicationMessage)

    // Deliver message to recipient
    const recipient = this.getAgent(toAgentId)
    if (recipient) {
      recipient.receiveMessage(communicationMessage)
    }

    console.log(`📨 Message sent from ${fromAgentId} to ${toAgentId}: ${message.content?.substring(0, 50)}...`)
    return messageId
  }

  // Broadcast message to all agents
  broadcastMessage(fromAgentId, message, channel = 'broadcast') {
    const activeAgents = this.getActiveAgents()
    const messageIds = []

    activeAgents.forEach(agent => {
      if (agent.id !== fromAgentId) {
        const messageId = this.sendMessage(fromAgentId, agent.id, message, channel)
        messageIds.push(messageId)
      }
    })

    return messageIds
  }

  // Create collaboration session
  createCollaboration(taskId, participantAgentIds, coordinatorId = null) {
    const collaborationId = `collab_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`
    
    const collaboration = {
      id: collaborationId,
      taskId,
      participants: new Set(participantAgentIds),
      coordinator: coordinatorId,
      status: 'active',
      createdAt: new Date().toISOString(),
      messages: [],
      sharedContext: {},
      results: new Map()
    }

    this.activeCollaborations.set(collaborationId, collaboration)

    // Notify participants
    participantAgentIds.forEach(agentId => {
      const agent = this.getAgent(agentId)
      if (agent) {
        agent.joinCollaboration(collaborationId)
      }
    })

    console.log(`🤝 Created collaboration ${collaborationId} for task ${taskId}`)
    return collaboration
  }

  // Get collaboration by ID
  getCollaboration(collaborationId) {
    return this.activeCollaborations.get(collaborationId)
  }

  // Update agent performance metrics
  updateAgentPerformance(agentId, taskResult) {
    const agent = this.getAgent(agentId)
    if (!agent) return

    const performance = agent.performance
    
    // Update task completion stats
    performance.tasksCompleted++
    performance.lastActive = new Date().toISOString()

    // Update completion time
    if (taskResult.completionTime) {
      const totalTime = performance.averageCompletionTime * (performance.tasksCompleted - 1) + taskResult.completionTime
      performance.averageCompletionTime = totalTime / performance.tasksCompleted
    }

    // Update success rate
    if (taskResult.success !== undefined) {
      const totalSuccess = performance.successRate * (performance.tasksCompleted - 1) + (taskResult.success ? 1 : 0)
      performance.successRate = totalSuccess / performance.tasksCompleted
    }

    // Update collaboration rating
    if (taskResult.collaborationRating !== undefined) {
      performance.collaborationRating = (performance.collaborationRating + taskResult.collaborationRating) / 2
    }

    // Store in performance history
    const agentType = this.agents.get(agent.type)
    if (agentType) {
      agentType.performanceHistory.push({
        agentId,
        timestamp: new Date().toISOString(),
        taskResult,
        performance: { ...performance }
      })
    }
  }

  // Get framework statistics
  getFrameworkStats() {
    const stats = {
      totalAgentTypes: this.agents.size,
      totalAgentInstances: 0,
      activeCollaborations: this.activeCollaborations.size,
      totalMessages: this.communicationLog.length,
      agentTypeStats: {},
      performanceOverview: {
        averageSuccessRate: 0,
        averageCompletionTime: 0,
        totalTasksCompleted: 0
      }
    }

    let totalSuccessRate = 0
    let totalCompletionTime = 0
    let totalTasksCompleted = 0
    let agentCount = 0

    for (const [type, agentType] of this.agents.entries()) {
      const instances = Array.from(agentType.instances.values())
      stats.totalAgentInstances += instances.length

      const typeStats = {
        instances: instances.length,
        totalCreated: agentType.totalCreated,
        activeInstances: instances.filter(a => a.status !== 'idle').length,
        averagePerformance: {
          successRate: 0,
          completionTime: 0,
          tasksCompleted: 0
        }
      }

      if (instances.length > 0) {
        const avgSuccessRate = instances.reduce((sum, a) => sum + a.performance.successRate, 0) / instances.length
        const avgCompletionTime = instances.reduce((sum, a) => sum + a.performance.averageCompletionTime, 0) / instances.length
        const totalTasks = instances.reduce((sum, a) => sum + a.performance.tasksCompleted, 0)

        typeStats.averagePerformance = {
          successRate: avgSuccessRate,
          completionTime: avgCompletionTime,
          tasksCompleted: totalTasks
        }

        totalSuccessRate += avgSuccessRate
        totalCompletionTime += avgCompletionTime
        totalTasksCompleted += totalTasks
        agentCount++
      }

      stats.agentTypeStats[type] = typeStats
    }

    if (agentCount > 0) {
      stats.performanceOverview = {
        averageSuccessRate: totalSuccessRate / agentCount,
        averageCompletionTime: totalCompletionTime / agentCount,
        totalTasksCompleted
      }
    }

    return stats
  }

  // Cleanup inactive collaborations
  cleanupCollaborations() {
    const now = Date.now()
    const maxAge = 24 * 60 * 60 * 1000 // 24 hours

    for (const [id, collaboration] of this.activeCollaborations.entries()) {
      const age = now - new Date(collaboration.createdAt).getTime()
      if (age > maxAge && collaboration.status === 'completed') {
        this.activeCollaborations.delete(id)
        console.log(`🧹 Cleaned up old collaboration: ${id}`)
      }
    }
  }
}

// Individual Agent class
class Agent {
  constructor(config) {
    Object.assign(this, config)
    this.messageQueue = []
    this.context = {}
  }

  // Receive a message from another agent
  receiveMessage(message) {
    this.messageQueue.push(message)
    this.processMessage(message)
  }

  // Process incoming message
  processMessage(message) {
    console.log(`🤖 Agent ${this.id} received message: ${message.message.content?.substring(0, 50)}...`)
    
    // Handle different message types
    switch (message.type) {
      case 'task_assignment':
        this.handleTaskAssignment(message)
        break
      case 'collaboration_request':
        this.handleCollaborationRequest(message)
        break
      case 'result_sharing':
        this.handleResultSharing(message)
        break
      case 'status_update':
        this.handleStatusUpdate(message)
        break
      default:
        this.handleGenericMessage(message)
    }
  }

  // Handle task assignment
  handleTaskAssignment(message) {
    const task = message.message.task
    this.currentTasks.push(task)
    this.status = 'working'
    
    console.log(`🎯 Agent ${this.id} assigned task: ${task.description}`)
  }

  // Handle collaboration request
  handleCollaborationRequest(message) {
    const collaborationId = message.message.collaborationId
    this.joinCollaboration(collaborationId)
  }

  // Join a collaboration
  joinCollaboration(collaborationId) {
    this.collaborations.add(collaborationId)
    console.log(`🤝 Agent ${this.id} joined collaboration: ${collaborationId}`)
  }

  // Handle result sharing
  handleResultSharing(message) {
    const result = message.message.result
    this.context[message.from] = result
    console.log(`📊 Agent ${this.id} received result from ${message.from}`)
  }

  // Handle status update
  handleStatusUpdate(message) {
    console.log(`📢 Agent ${this.id} received status update: ${message.message.status}`)
  }

  // Handle generic message
  handleGenericMessage(message) {
    console.log(`💬 Agent ${this.id} received generic message from ${message.from}`)
  }

  // Complete a task
  completeTask(taskId, result) {
    const taskIndex = this.currentTasks.findIndex(t => t.id === taskId)
    if (taskIndex !== -1) {
      const task = this.currentTasks.splice(taskIndex, 1)[0]
      this.completedTasks.push({ ...task, result, completedAt: new Date().toISOString() })
      
      if (this.currentTasks.length === 0) {
        this.status = 'idle'
      }

      // Update performance
      this.framework.updateAgentPerformance(this.id, {
        success: result.success,
        completionTime: result.completionTime,
        collaborationRating: result.collaborationRating
      })

      console.log(`✅ Agent ${this.id} completed task: ${task.description}`)
    }
  }

  // Get agent status
  getStatus() {
    return {
      id: this.id,
      type: this.type,
      name: this.name,
      status: this.status,
      currentTasks: this.currentTasks.length,
      completedTasks: this.completedTasks.length,
      collaborations: Array.from(this.collaborations),
      performance: this.performance,
      capabilities: this.capabilities,
      expertise: this.expertise
    }
  }
}

// Create singleton instance
const agentFramework = new AgentFramework()
export default agentFramework
