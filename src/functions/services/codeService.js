// Code Generation and Validation Service
import hljs from 'highlight.js'

class CodeService {
  constructor() {
    this.supportedLanguages = [
      'javascript', 'python', 'java', 'cpp', 'c', 'csharp', 'php', 'ruby', 
      'go', 'rust', 'typescript', 'html', 'css', 'sql', 'bash', 'json', 'xml'
    ]
  }

  // Detect programming language from code
  detectLanguage(code) {
    try {
      const result = hljs.highlightAuto(code, this.supportedLanguages)
      return result.language || 'plaintext'
    } catch (error) {
      console.warn('Language detection failed:', error)
      return 'plaintext'
    }
  }

  // Highlight code with syntax highlighting
  highlightCode(code, language = null) {
    try {
      if (language && hljs.getLanguage(language)) {
        return hljs.highlight(code, { language }).value
      } else {
        const result = hljs.highlightAuto(code, this.supportedLanguages)
        return result.value
      }
    } catch (error) {
      console.warn('Code highlighting failed:', error)
      return code // Return plain code if highlighting fails
    }
  }

  // Basic code validation
  validateCode(code, language) {
    const validation = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    }

    // Basic syntax checks
    if (!code || code.trim().length === 0) {
      validation.isValid = false
      validation.errors.push('Code cannot be empty')
      return validation
    }

    // Language-specific validation
    switch (language) {
      case 'javascript':
      case 'typescript':
        this.validateJavaScript(code, validation)
        break
      case 'python':
        this.validatePython(code, validation)
        break
      case 'json':
        this.validateJSON(code, validation)
        break
      default:
        this.validateGeneral(code, validation)
    }

    return validation
  }

  // JavaScript/TypeScript validation
  validateJavaScript(code, validation) {
    // Check for basic syntax issues
    const openBraces = (code.match(/\{/g) || []).length
    const closeBraces = (code.match(/\}/g) || []).length
    const openParens = (code.match(/\(/g) || []).length
    const closeParens = (code.match(/\)/g) || []).length

    if (openBraces !== closeBraces) {
      validation.errors.push('Mismatched curly braces')
      validation.isValid = false
    }

    if (openParens !== closeParens) {
      validation.errors.push('Mismatched parentheses')
      validation.isValid = false
    }

    // Check for common issues
    if (code.includes('console.log')) {
      validation.suggestions.push('Consider using proper logging in production')
    }

    if (!code.includes('function') && !code.includes('=>') && !code.includes('class')) {
      validation.warnings.push('Code might benefit from being wrapped in a function')
    }
  }

  // Python validation
  validatePython(code, validation) {
    const lines = code.split('\n')
    let indentLevel = 0

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      if (line.trim().length === 0) continue

      // Check indentation consistency
      const leadingSpaces = line.match(/^ */)[0].length
      if (line.trim().endsWith(':')) {
        indentLevel = leadingSpaces + 4
      }
    }

    // Check for common Python patterns
    if (code.includes('print(') && !code.includes('def ') && !code.includes('class ')) {
      validation.suggestions.push('Consider organizing code into functions')
    }
  }

  // JSON validation
  validateJSON(code, validation) {
    try {
      JSON.parse(code)
    } catch (error) {
      validation.isValid = false
      validation.errors.push(`Invalid JSON: ${error.message}`)
    }
  }

  // General validation for all languages
  validateGeneral(code, validation) {
    // Check for extremely long lines
    const lines = code.split('\n')
    const longLines = lines.filter(line => line.length > 120)
    if (longLines.length > 0) {
      validation.warnings.push(`${longLines.length} lines exceed 120 characters`)
    }

    // Check for potential security issues
    if (code.includes('eval(') || code.includes('exec(')) {
      validation.warnings.push('Code contains potentially dangerous eval/exec statements')
    }
  }

  // Format code response with metadata
  formatCodeResponse(code, language, explanation = '') {
    const detectedLanguage = language || this.detectLanguage(code)
    const validation = this.validateCode(code, detectedLanguage)
    const highlightedCode = this.highlightCode(code, detectedLanguage)

    return {
      code,
      language: detectedLanguage,
      highlightedCode,
      validation,
      explanation,
      metadata: {
        lineCount: code.split('\n').length,
        characterCount: code.length,
        estimatedComplexity: this.estimateComplexity(code)
      }
    }
  }

  // Estimate code complexity
  estimateComplexity(code) {
    const complexityIndicators = [
      /if\s*\(/g,
      /for\s*\(/g,
      /while\s*\(/g,
      /function\s+/g,
      /class\s+/g,
      /try\s*\{/g,
      /catch\s*\(/g
    ]

    let complexity = 1 // Base complexity
    complexityIndicators.forEach(pattern => {
      const matches = code.match(pattern)
      if (matches) {
        complexity += matches.length
      }
    })

    if (complexity <= 5) return 'Low'
    if (complexity <= 15) return 'Medium'
    return 'High'
  }

  // Get language-specific templates
  getCodeTemplate(language, type = 'basic') {
    const templates = {
      javascript: {
        basic: `// JavaScript function template
function exampleFunction(param) {
  // Your code here
  return param;
}

// Usage example
const result = exampleFunction('Hello World');
console.log(result);`,
        class: `// JavaScript class template
class ExampleClass {
  constructor(name) {
    this.name = name;
  }
  
  greet() {
    return \`Hello, \${this.name}!\`;
  }
}

// Usage
const instance = new ExampleClass('World');
console.log(instance.greet());`
      },
      python: {
        basic: `# Python function template
def example_function(param):
    """
    Example function description
    """
    # Your code here
    return param

# Usage example
result = example_function("Hello World")
print(result)`,
        class: `# Python class template
class ExampleClass:
    def __init__(self, name):
        self.name = name
    
    def greet(self):
        return f"Hello, {self.name}!"

# Usage
instance = ExampleClass("World")
print(instance.greet())`
      }
    }

    return templates[language]?.[type] || templates.javascript.basic
  }
}

// Export singleton instance
const codeService = new CodeService()
export default codeService
