// Reasoning Service - Advanced multi-step problem solving and chain-of-thought
class ReasoningService {
  constructor() {
    this.name = 'reasoning_service'
    this.version = '1.0.0'
    this.maxReasoningSteps = 10
    this.minComplexityThreshold = 50 // Characters that indicate complex problem
    this.reasoningHistory = []
  }

  // Main reasoning entry point
  async analyzeAndReason(query, context = {}) {
    console.log('🧠 Starting reasoning analysis for:', query.substring(0, 100) + '...')

    try {
      // Step 1: Analyze problem complexity
      const complexity = this.analyzeComplexity(query)
      
      // Step 2: Determine if reasoning is needed
      const needsReasoning = this.shouldUseReasoning(query, complexity)
      
      if (!needsReasoning) {
        return {
          useReasoning: false,
          complexity: complexity,
          reason: 'Query is straightforward and doesn\'t require multi-step reasoning'
        }
      }

      // Step 3: Decompose the problem
      const decomposition = await this.decomposeProblem(query, context)
      
      // Step 4: Generate reasoning steps
      const reasoningSteps = await this.generateReasoningSteps(decomposition)
      
      // Step 5: Create reasoning chain
      const reasoningChain = this.buildReasoningChain(reasoningSteps)
      
      // Step 6: Generate enhanced prompt
      const enhancedPrompt = this.createReasoningPrompt(query, reasoningChain)

      const result = {
        useReasoning: true,
        complexity: complexity,
        decomposition: decomposition,
        reasoningSteps: reasoningSteps,
        reasoningChain: reasoningChain,
        enhancedPrompt: enhancedPrompt,
        metadata: {
          timestamp: new Date().toISOString(),
          stepCount: reasoningSteps.length,
          estimatedDifficulty: complexity.level
        }
      }

      // Store in reasoning history
      this.reasoningHistory.push({
        query: query.substring(0, 200),
        result: result,
        timestamp: new Date().toISOString()
      })

      console.log('✅ Reasoning analysis complete:', reasoningSteps.length, 'steps generated')
      return result

    } catch (error) {
      console.error('❌ Reasoning analysis failed:', error)
      return {
        useReasoning: false,
        error: error.message,
        fallback: true
      }
    }
  }

  // Analyze problem complexity
  analyzeComplexity(query) {
    const indicators = {
      // Length indicators
      length: query.length,
      
      // Complexity keywords
      multiStep: /\b(step|steps|process|procedure|how to|explain|analyze|compare|evaluate)\b/i.test(query),
      mathematical: /\b(calculate|solve|equation|formula|math|statistics|probability)\b/i.test(query),
      logical: /\b(if|then|because|therefore|logic|reason|prove|deduce)\b/i.test(query),
      creative: /\b(create|design|invent|imagine|brainstorm|generate)\b/i.test(query),
      analytical: /\b(analyze|examine|investigate|research|study|review)\b/i.test(query),
      
      // Question complexity
      multipleQuestions: (query.match(/\?/g) || []).length > 1,
      compoundSentences: (query.match(/\b(and|but|however|moreover|furthermore)\b/gi) || []).length > 2,
      
      // Technical indicators
      technical: /\b(algorithm|implementation|architecture|system|framework|methodology)\b/i.test(query),
      
      // Problem-solving indicators
      problemSolving: /\b(problem|issue|challenge|solution|fix|resolve|troubleshoot)\b/i.test(query)
    }

    // Calculate complexity score
    let score = 0
    score += Math.min(indicators.length / 10, 20) // Length contribution (max 20)
    score += indicators.multiStep ? 15 : 0
    score += indicators.mathematical ? 20 : 0
    score += indicators.logical ? 15 : 0
    score += indicators.creative ? 10 : 0
    score += indicators.analytical ? 15 : 0
    score += indicators.multipleQuestions ? 10 : 0
    score += indicators.compoundSentences ? 5 : 0
    score += indicators.technical ? 15 : 0
    score += indicators.problemSolving ? 10 : 0

    // Determine complexity level
    let level = 'simple'
    if (score >= 70) level = 'very_complex'
    else if (score >= 50) level = 'complex'
    else if (score >= 30) level = 'moderate'

    return {
      score: Math.round(score),
      level: level,
      indicators: indicators,
      reasoning: this.getComplexityReasoning(indicators, score)
    }
  }

  // Determine if reasoning should be used
  shouldUseReasoning(query, complexity) {
    // Always use reasoning for complex problems
    if (complexity.score >= 40) return true
    
    // Use reasoning for specific patterns
    const reasoningTriggers = [
      /\bhow to\b.*\band\b/i, // "How to X and Y"
      /\bwhy\b.*\bbecause\b/i, // "Why X because Y"
      /\bcompare\b.*\bwith\b/i, // "Compare X with Y"
      /\bstep.*by.*step\b/i, // "Step by step"
      /\bexplain.*process\b/i, // "Explain the process"
      /\banalyze.*and.*provide\b/i, // "Analyze X and provide Y"
    ]

    return reasoningTriggers.some(pattern => pattern.test(query))
  }

  // Decompose problem into sub-problems
  async decomposeProblem(query, context) {
    console.log('🔍 Decomposing problem...')

    // Identify main components
    const components = {
      mainGoal: this.extractMainGoal(query),
      subGoals: this.extractSubGoals(query),
      constraints: this.extractConstraints(query),
      context: context,
      dependencies: this.identifyDependencies(query)
    }

    // Create sub-problems
    const subProblems = this.createSubProblems(components)

    return {
      originalQuery: query,
      mainGoal: components.mainGoal,
      subProblems: subProblems,
      estimatedSteps: subProblems.length,
      complexity: this.estimateDecompositionComplexity(subProblems)
    }
  }

  // Extract main goal from query
  extractMainGoal(query) {
    // Look for main action verbs and objectives
    const goalPatterns = [
      /(?:how to|how can I|how do I)\s+(.+?)(?:\?|$)/i,
      /(?:explain|describe|analyze)\s+(.+?)(?:\?|$)/i,
      /(?:create|build|make|design)\s+(.+?)(?:\?|$)/i,
      /(?:solve|fix|resolve)\s+(.+?)(?:\?|$)/i,
      /(?:find|discover|determine)\s+(.+?)(?:\?|$)/i
    ]

    for (const pattern of goalPatterns) {
      const match = query.match(pattern)
      if (match) {
        return {
          action: match[0].split(' ')[0].toLowerCase(),
          target: match[1].trim(),
          confidence: 0.8
        }
      }
    }

    // Fallback: use first sentence as goal
    const firstSentence = query.split(/[.!?]/)[0]
    return {
      action: 'understand',
      target: firstSentence.trim(),
      confidence: 0.5
    }
  }

  // Extract sub-goals
  extractSubGoals(query) {
    const subGoals = []
    
    // Look for enumerated items
    const enumeratedItems = query.match(/\d+\.\s*([^.!?]+)/g)
    if (enumeratedItems) {
      enumeratedItems.forEach((item, index) => {
        subGoals.push({
          id: index + 1,
          description: item.replace(/^\d+\.\s*/, '').trim(),
          type: 'enumerated'
        })
      })
    }

    // Look for "and" connections
    const andConnections = query.split(/\s+and\s+/i)
    if (andConnections.length > 1) {
      andConnections.forEach((connection, index) => {
        if (connection.trim().length > 10) {
          subGoals.push({
            id: subGoals.length + 1,
            description: connection.trim(),
            type: 'connected'
          })
        }
      })
    }

    return subGoals
  }

  // Extract constraints
  extractConstraints(query) {
    const constraints = []
    
    const constraintPatterns = [
      { pattern: /\bwithin\s+(\d+\s*\w+)/i, type: 'time' },
      { pattern: /\bunder\s*\$?(\d+)/i, type: 'budget' },
      { pattern: /\busing\s+([^.!?]+)/i, type: 'tools' },
      { pattern: /\bwithout\s+([^.!?]+)/i, type: 'exclusion' },
      { pattern: /\bmust\s+([^.!?]+)/i, type: 'requirement' }
    ]

    constraintPatterns.forEach(({ pattern, type }) => {
      const match = query.match(pattern)
      if (match) {
        constraints.push({
          type: type,
          value: match[1].trim(),
          original: match[0]
        })
      }
    })

    return constraints
  }

  // Identify dependencies between parts
  identifyDependencies(query) {
    const dependencies = []
    
    // Look for sequential indicators
    const sequentialPatterns = [
      /\bfirst\b.*\bthen\b/i,
      /\bbefore\b.*\bafter\b/i,
      /\bonce\b.*\bcan\b/i
    ]

    sequentialPatterns.forEach(pattern => {
      if (pattern.test(query)) {
        dependencies.push({
          type: 'sequential',
          description: 'Steps must be completed in order'
        })
      }
    })

    return dependencies
  }

  // Create sub-problems from components
  createSubProblems(components) {
    const subProblems = []

    // Add main goal as primary problem
    subProblems.push({
      id: 1,
      type: 'primary',
      description: `${components.mainGoal.action} ${components.mainGoal.target}`,
      priority: 'high',
      dependencies: []
    })

    // Add sub-goals as secondary problems
    components.subGoals.forEach((subGoal, index) => {
      subProblems.push({
        id: subProblems.length + 1,
        type: 'secondary',
        description: subGoal.description,
        priority: 'medium',
        dependencies: [1] // Depends on primary goal
      })
    })

    // Add constraint validation as tertiary problems
    components.constraints.forEach((constraint, index) => {
      subProblems.push({
        id: subProblems.length + 1,
        type: 'validation',
        description: `Ensure solution meets ${constraint.type} constraint: ${constraint.value}`,
        priority: 'low',
        dependencies: [1]
      })
    })

    return subProblems
  }

  // Generate reasoning steps
  async generateReasoningSteps(decomposition) {
    console.log('🔗 Generating reasoning steps...')

    const steps = []

    // Step 1: Problem understanding
    steps.push({
      id: 1,
      type: 'understanding',
      title: 'Problem Analysis',
      description: `Understanding the main goal: ${decomposition.mainGoal.target}`,
      reasoning: 'First, I need to clearly understand what is being asked.',
      inputs: [decomposition.originalQuery],
      outputs: ['Clear problem definition'],
      confidence: 0.9
    })

    // Step 2: Information gathering
    steps.push({
      id: 2,
      type: 'information',
      title: 'Information Gathering',
      description: 'Identifying what information and resources are needed',
      reasoning: 'To solve this effectively, I need to gather relevant information.',
      inputs: ['Problem definition'],
      outputs: ['Required information list'],
      confidence: 0.8
    })

    // Step 3: Sub-problem solving
    decomposition.subProblems.forEach((subProblem, index) => {
      if (subProblem.type === 'primary' || subProblem.type === 'secondary') {
        steps.push({
          id: steps.length + 1,
          type: 'solving',
          title: `Solve: ${subProblem.description}`,
          description: `Addressing sub-problem: ${subProblem.description}`,
          reasoning: `This step is essential because it contributes to the overall solution.`,
          inputs: ['Previous step results'],
          outputs: [`Solution for: ${subProblem.description}`],
          confidence: 0.7,
          priority: subProblem.priority
        })
      }
    })

    // Step 4: Integration
    if (decomposition.subProblems.length > 1) {
      steps.push({
        id: steps.length + 1,
        type: 'integration',
        title: 'Solution Integration',
        description: 'Combining all sub-solutions into a coherent final answer',
        reasoning: 'The individual solutions need to be integrated for a complete response.',
        inputs: ['All sub-solutions'],
        outputs: ['Integrated solution'],
        confidence: 0.8
      })
    }

    // Step 5: Validation
    steps.push({
      id: steps.length + 1,
      type: 'validation',
      title: 'Solution Validation',
      description: 'Verifying the solution addresses the original problem',
      reasoning: 'I need to ensure the solution actually solves the original problem.',
      inputs: ['Final solution', 'Original problem'],
      outputs: ['Validated solution'],
      confidence: 0.9
    })

    return steps
  }

  // Build reasoning chain for prompt
  buildReasoningChain(reasoningSteps) {
    const chain = {
      introduction: "I'll approach this step-by-step to ensure a thorough and accurate response:",
      steps: reasoningSteps.map(step => ({
        number: step.id,
        title: step.title,
        approach: step.reasoning,
        focus: step.description
      })),
      conclusion: "This systematic approach will ensure I provide a comprehensive and well-reasoned response."
    }

    return chain
  }

  // Create enhanced prompt with reasoning
  createReasoningPrompt(originalQuery, reasoningChain) {
    const prompt = `
I need to approach this query systematically using chain-of-thought reasoning.

Original Query: "${originalQuery}"

My Reasoning Approach:
${reasoningChain.introduction}

${reasoningChain.steps.map(step => 
  `${step.number}. ${step.title}: ${step.approach} - ${step.focus}`
).join('\n')}

${reasoningChain.conclusion}

Now, let me work through each step carefully and provide a detailed response that shows my reasoning process.

Please provide a response that:
1. Shows clear step-by-step thinking
2. Explains the reasoning behind each step
3. Validates conclusions against the original question
4. Provides a comprehensive final answer

Response:`

    return prompt.trim()
  }

  // Get complexity reasoning explanation
  getComplexityReasoning(indicators, score) {
    const reasons = []
    
    if (indicators.multiStep) reasons.push('requires multiple steps')
    if (indicators.mathematical) reasons.push('involves mathematical concepts')
    if (indicators.logical) reasons.push('requires logical reasoning')
    if (indicators.analytical) reasons.push('needs analytical thinking')
    if (indicators.technical) reasons.push('involves technical concepts')
    if (indicators.problemSolving) reasons.push('is a problem-solving task')
    if (indicators.length > 100) reasons.push('is a detailed query')

    return reasons.length > 0 ? reasons.join(', ') : 'appears straightforward'
  }

  // Estimate decomposition complexity
  estimateDecompositionComplexity(subProblems) {
    const primaryCount = subProblems.filter(p => p.type === 'primary').length
    const secondaryCount = subProblems.filter(p => p.type === 'secondary').length
    const validationCount = subProblems.filter(p => p.type === 'validation').length

    let complexity = 'simple'
    if (primaryCount + secondaryCount > 5) complexity = 'complex'
    else if (primaryCount + secondaryCount > 2) complexity = 'moderate'

    return {
      level: complexity,
      primaryProblems: primaryCount,
      secondaryProblems: secondaryCount,
      validationSteps: validationCount,
      totalSteps: subProblems.length
    }
  }

  // Get reasoning statistics
  getReasoningStats() {
    return {
      totalReasoningSessions: this.reasoningHistory.length,
      averageSteps: this.reasoningHistory.length > 0 
        ? this.reasoningHistory.reduce((sum, session) => sum + (session.result.reasoningSteps?.length || 0), 0) / this.reasoningHistory.length
        : 0,
      complexityDistribution: this.getComplexityDistribution(),
      recentSessions: this.reasoningHistory.slice(-5)
    }
  }

  // Get complexity distribution
  getComplexityDistribution() {
    const distribution = { simple: 0, moderate: 0, complex: 0, very_complex: 0 }
    
    this.reasoningHistory.forEach(session => {
      const level = session.result.complexity?.level || 'simple'
      distribution[level]++
    })

    return distribution
  }

  // Clear reasoning history
  clearHistory() {
    this.reasoningHistory = []
    console.log('🧹 Reasoning history cleared')
  }
}

// Create singleton instance
const reasoningService = new ReasoningService()
export default reasoningService
