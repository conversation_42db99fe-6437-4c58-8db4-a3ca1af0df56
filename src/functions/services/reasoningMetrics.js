// Reasoning Quality Metrics - Measure and improve reasoning effectiveness
class ReasoningMetrics {
  constructor() {
    this.name = 'reasoning_metrics'
    this.version = '1.0.0'
    this.metricsHistory = []
    this.performanceThresholds = {
      excellent: 90,
      good: 75,
      acceptable: 60,
      poor: 40
    }
  }

  // Main metrics collection entry point
  collectMetrics(reasoningData, validationResult, userFeedback = null) {
    console.log('📊 Collecting reasoning quality metrics...')

    try {
      const metrics = {
        // Basic metrics
        timestamp: new Date().toISOString(),
        sessionId: this.generateSessionId(),
        
        // Reasoning effectiveness metrics
        reasoningQuality: this.assessReasoningQuality(reasoningData),
        solutionQuality: this.assessSolutionQuality(validationResult),
        userSatisfaction: this.assessUserSatisfaction(userFeedback),
        
        // Performance metrics
        efficiency: this.assessEfficiency(reasoningData),
        complexity: this.assessComplexityHandling(reasoningData),
        
        // Overall score
        overallScore: 0 // Will be calculated
      }

      // Calculate overall score
      metrics.overallScore = this.calculateOverallScore(metrics)
      
      // Store metrics
      this.metricsHistory.push(metrics)
      
      // Keep only last 100 entries to prevent memory issues
      if (this.metricsHistory.length > 100) {
        this.metricsHistory = this.metricsHistory.slice(-100)
      }

      console.log(`✅ Metrics collected: Overall score ${metrics.overallScore}%`)
      return metrics

    } catch (error) {
      console.error('❌ Failed to collect reasoning metrics:', error)
      return null
    }
  }

  // Assess reasoning quality
  assessReasoningQuality(reasoningData) {
    if (!reasoningData || !reasoningData.useReasoning) {
      return { score: 100, note: 'No reasoning used - simple query' }
    }

    let score = 70 // Base score for using reasoning

    const { complexity, decomposition, reasoningSteps, reasoningChain } = reasoningData

    // Complexity appropriateness (20 points)
    if (complexity.score >= 50 && reasoningSteps.length >= 3) score += 20
    else if (complexity.score >= 30 && reasoningSteps.length >= 2) score += 10

    // Decomposition quality (15 points)
    if (decomposition && decomposition.subProblems.length > 0) {
      score += Math.min(15, decomposition.subProblems.length * 3)
    }

    // Reasoning steps quality (15 points)
    if (reasoningSteps.length > 0) {
      const avgConfidence = reasoningSteps.reduce((sum, step) => sum + (step.confidence || 0.7), 0) / reasoningSteps.length
      score += Math.round(avgConfidence * 15)
    }

    return {
      score: Math.min(100, score),
      details: {
        complexityScore: complexity.score,
        stepCount: reasoningSteps.length,
        decompositionQuality: decomposition ? decomposition.subProblems.length : 0,
        avgStepConfidence: reasoningSteps.length > 0 
          ? reasoningSteps.reduce((sum, step) => sum + (step.confidence || 0.7), 0) / reasoningSteps.length 
          : 0
      }
    }
  }

  // Assess solution quality based on validation
  assessSolutionQuality(validationResult) {
    if (!validationResult) {
      return { score: 75, note: 'No validation performed' }
    }

    return {
      score: validationResult.score,
      passed: validationResult.passed,
      details: validationResult.details,
      recommendations: validationResult.recommendations
    }
  }

  // Assess user satisfaction (if feedback provided)
  assessUserSatisfaction(userFeedback) {
    if (!userFeedback) {
      return { score: null, note: 'No user feedback provided' }
    }

    // Simple feedback scoring
    if (typeof userFeedback === 'number') {
      return { score: userFeedback, type: 'numeric' }
    }

    if (typeof userFeedback === 'string') {
      const positive = ['good', 'great', 'excellent', 'helpful', 'useful', 'clear', 'perfect']
      const negative = ['bad', 'poor', 'unclear', 'wrong', 'useless', 'confusing']
      
      const feedbackLower = userFeedback.toLowerCase()
      const positiveCount = positive.filter(word => feedbackLower.includes(word)).length
      const negativeCount = negative.filter(word => feedbackLower.includes(word)).length
      
      let score = 50 // Neutral base
      score += positiveCount * 20
      score -= negativeCount * 25
      
      return { 
        score: Math.max(0, Math.min(100, score)), 
        type: 'text',
        analysis: { positiveCount, negativeCount }
      }
    }

    return { score: null, note: 'Unknown feedback format' }
  }

  // Assess reasoning efficiency
  assessEfficiency(reasoningData) {
    if (!reasoningData || !reasoningData.useReasoning) {
      return { score: 100, note: 'No reasoning overhead' }
    }

    let score = 80 // Base efficiency score

    const { reasoningSteps, complexity } = reasoningData

    // Check if reasoning steps are proportional to complexity
    const expectedSteps = Math.ceil(complexity.score / 20) // Rough heuristic
    const actualSteps = reasoningSteps.length

    if (actualSteps <= expectedSteps) {
      score += 20 // Efficient reasoning
    } else if (actualSteps <= expectedSteps * 1.5) {
      score += 10 // Acceptable reasoning
    } else {
      score -= 10 // Over-complicated reasoning
    }

    // Check for redundant steps
    const uniqueTypes = new Set(reasoningSteps.map(step => step.type))
    if (uniqueTypes.size === reasoningSteps.length) {
      score += 10 // No redundant step types
    }

    return {
      score: Math.max(0, Math.min(100, score)),
      details: {
        expectedSteps,
        actualSteps,
        efficiency: actualSteps > 0 ? expectedSteps / actualSteps : 1,
        uniqueStepTypes: uniqueTypes.size
      }
    }
  }

  // Assess complexity handling
  assessComplexityHandling(reasoningData) {
    if (!reasoningData || !reasoningData.useReasoning) {
      return { score: 100, note: 'Simple query handled appropriately' }
    }

    const { complexity, reasoningSteps } = reasoningData
    let score = 60 // Base score

    // Check if reasoning depth matches complexity
    if (complexity.level === 'very_complex' && reasoningSteps.length >= 5) score += 30
    else if (complexity.level === 'complex' && reasoningSteps.length >= 4) score += 25
    else if (complexity.level === 'moderate' && reasoningSteps.length >= 3) score += 20
    else if (complexity.level === 'simple' && reasoningSteps.length >= 2) score += 15

    // Check for appropriate step types
    const stepTypes = reasoningSteps.map(step => step.type)
    const hasUnderstanding = stepTypes.includes('understanding')
    const hasSolving = stepTypes.includes('solving')
    const hasValidation = stepTypes.includes('validation')

    if (hasUnderstanding) score += 5
    if (hasSolving) score += 5
    if (hasValidation) score += 5

    return {
      score: Math.min(100, score),
      details: {
        complexityLevel: complexity.level,
        complexityScore: complexity.score,
        reasoningDepth: reasoningSteps.length,
        hasKeySteps: { hasUnderstanding, hasSolving, hasValidation }
      }
    }
  }

  // Calculate overall reasoning performance score
  calculateOverallScore(metrics) {
    const weights = {
      reasoningQuality: 0.3,    // 30% - How well reasoning was applied
      solutionQuality: 0.4,     // 40% - Quality of final solution
      efficiency: 0.2,          // 20% - Efficiency of reasoning process
      complexity: 0.1           // 10% - Appropriate complexity handling
    }

    let weightedScore = 0
    let totalWeight = 0

    // Reasoning quality
    if (metrics.reasoningQuality && typeof metrics.reasoningQuality.score === 'number') {
      weightedScore += metrics.reasoningQuality.score * weights.reasoningQuality
      totalWeight += weights.reasoningQuality
    }

    // Solution quality
    if (metrics.solutionQuality && typeof metrics.solutionQuality.score === 'number') {
      weightedScore += metrics.solutionQuality.score * weights.solutionQuality
      totalWeight += weights.solutionQuality
    }

    // Efficiency
    if (metrics.efficiency && typeof metrics.efficiency.score === 'number') {
      weightedScore += metrics.efficiency.score * weights.efficiency
      totalWeight += weights.efficiency
    }

    // Complexity handling
    if (metrics.complexity && typeof metrics.complexity.score === 'number') {
      weightedScore += metrics.complexity.score * weights.complexity
      totalWeight += weights.complexity
    }

    return totalWeight > 0 ? Math.round(weightedScore / totalWeight) : 0
  }

  // Get performance analytics
  getPerformanceAnalytics() {
    if (this.metricsHistory.length === 0) {
      return {
        totalSessions: 0,
        averageScore: 0,
        performanceDistribution: {},
        trends: null
      }
    }

    const totalSessions = this.metricsHistory.length
    const averageScore = this.metricsHistory.reduce((sum, m) => sum + m.overallScore, 0) / totalSessions

    // Performance distribution
    const distribution = {
      excellent: 0,
      good: 0,
      acceptable: 0,
      poor: 0
    }

    this.metricsHistory.forEach(metrics => {
      const score = metrics.overallScore
      if (score >= this.performanceThresholds.excellent) distribution.excellent++
      else if (score >= this.performanceThresholds.good) distribution.good++
      else if (score >= this.performanceThresholds.acceptable) distribution.acceptable++
      else distribution.poor++
    })

    // Recent trends (last 10 sessions vs previous 10)
    let trends = null
    if (totalSessions >= 10) {
      const recent = this.metricsHistory.slice(-10)
      const previous = this.metricsHistory.slice(-20, -10)
      
      if (previous.length > 0) {
        const recentAvg = recent.reduce((sum, m) => sum + m.overallScore, 0) / recent.length
        const previousAvg = previous.reduce((sum, m) => sum + m.overallScore, 0) / previous.length
        
        trends = {
          direction: recentAvg > previousAvg ? 'improving' : recentAvg < previousAvg ? 'declining' : 'stable',
          change: Math.round(recentAvg - previousAvg),
          recentAverage: Math.round(recentAvg),
          previousAverage: Math.round(previousAvg)
        }
      }
    }

    return {
      totalSessions,
      averageScore: Math.round(averageScore),
      performanceDistribution: distribution,
      trends,
      recentSessions: this.metricsHistory.slice(-5)
    }
  }

  // Get improvement recommendations
  getImprovementRecommendations() {
    const analytics = this.getPerformanceAnalytics()
    const recommendations = []

    if (analytics.averageScore < this.performanceThresholds.acceptable) {
      recommendations.push({
        priority: 'high',
        area: 'overall_performance',
        suggestion: 'Overall reasoning performance is below acceptable levels. Focus on improving solution quality and reasoning depth.'
      })
    }

    if (analytics.performanceDistribution.poor > analytics.totalSessions * 0.3) {
      recommendations.push({
        priority: 'high',
        area: 'consistency',
        suggestion: 'High variability in performance. Review reasoning triggers and validation criteria.'
      })
    }

    if (analytics.trends && analytics.trends.direction === 'declining') {
      recommendations.push({
        priority: 'medium',
        area: 'trends',
        suggestion: `Performance declining by ${Math.abs(analytics.trends.change)} points. Investigate recent changes.`
      })
    }

    // Analyze specific areas from recent sessions
    if (this.metricsHistory.length >= 5) {
      const recentMetrics = this.metricsHistory.slice(-5)
      
      const avgReasoningQuality = recentMetrics.reduce((sum, m) => sum + (m.reasoningQuality?.score || 0), 0) / recentMetrics.length
      const avgSolutionQuality = recentMetrics.reduce((sum, m) => sum + (m.solutionQuality?.score || 0), 0) / recentMetrics.length
      const avgEfficiency = recentMetrics.reduce((sum, m) => sum + (m.efficiency?.score || 0), 0) / recentMetrics.length

      if (avgReasoningQuality < 70) {
        recommendations.push({
          priority: 'medium',
          area: 'reasoning_quality',
          suggestion: 'Reasoning quality is below optimal. Consider improving problem decomposition and step generation.'
        })
      }

      if (avgSolutionQuality < 70) {
        recommendations.push({
          priority: 'high',
          area: 'solution_quality',
          suggestion: 'Solution quality needs improvement. Focus on relevance, completeness, and accuracy.'
        })
      }

      if (avgEfficiency < 70) {
        recommendations.push({
          priority: 'low',
          area: 'efficiency',
          suggestion: 'Reasoning efficiency could be improved. Optimize step generation and reduce redundancy.'
        })
      }
    }

    if (recommendations.length === 0) {
      recommendations.push({
        priority: 'info',
        area: 'performance',
        suggestion: 'Reasoning performance is meeting expectations. Continue monitoring for consistency.'
      })
    }

    return recommendations
  }

  // Generate session ID
  generateSessionId() {
    return `reasoning_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Clear metrics history
  clearHistory() {
    this.metricsHistory = []
    console.log('🧹 Reasoning metrics history cleared')
  }
}

// Create singleton instance
const reasoningMetrics = new ReasoningMetrics()
export default reasoningMetrics
