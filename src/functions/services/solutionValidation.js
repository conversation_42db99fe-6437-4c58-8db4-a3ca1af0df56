// Solution Validation System - Verify solutions against original problems
class SolutionValidator {
  constructor() {
    this.name = 'solution_validator'
    this.version = '1.0.0'
    this.validationHistory = []
  }

  // Main validation entry point
  async validateSolution(originalQuery, solution, reasoningData = null) {
    console.log('🔍 Validating solution against original query...')

    try {
      // Step 1: Basic validation checks
      const basicChecks = this.performBasicValidation(originalQuery, solution)

      // Step 2: Content relevance validation
      const relevanceCheck = this.validateRelevance(originalQuery, solution)

      // Step 3: Completeness validation
      const completenessCheck = this.validateCompleteness(originalQuery, solution)

      // Step 4: Quality assessment
      const qualityCheck = this.assessQuality(solution)

      // Step 5: Reasoning consistency (if reasoning data available)
      const reasoningCheck = reasoningData
        ? this.validateReasoningConsistency(solution, reasoningData)
        : { score: 100, passed: true, note: 'No reasoning data to validate' }

      // Calculate overall validation score
      const overallScore = this.calculateOverallScore({
        basic: basicChecks,
        relevance: relevanceCheck,
        completeness: completenessCheck,
        quality: qualityCheck,
        reasoning: reasoningCheck
      })

      const result = {
        passed: overallScore.score >= 70, // 70% threshold for passing
        score: overallScore.score,
        details: {
          basic: basicChecks,
          relevance: relevanceCheck,
          completeness: completenessCheck,
          quality: qualityCheck,
          reasoning: reasoningCheck
        },
        recommendations: this.generateRecommendations(overallScore),
        timestamp: new Date().toISOString()
      }

      // Store validation result
      this.validationHistory.push({
        query: originalQuery.substring(0, 100),
        result: result,
        timestamp: new Date().toISOString()
      })

      console.log(`✅ Solution validation complete: ${result.passed ? 'PASSED' : 'FAILED'} (${result.score}%)`)
      return result

    } catch (error) {
      console.error('❌ Solution validation failed:', error)
      return {
        passed: false,
        score: 0,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  // Basic validation checks
  performBasicValidation(query, solution) {
    const checks = {
      hasContent: solution && solution.trim().length > 0,
      minimumLength: solution.length >= 20, // At least 20 characters
      notEmpty: solution.trim() !== '',
      notGeneric: !this.isGenericResponse(solution),
      properFormat: this.hasProperFormat(solution)
    }

    const passedChecks = Object.values(checks).filter(Boolean).length
    const totalChecks = Object.keys(checks).length
    const score = Math.round((passedChecks / totalChecks) * 100)

    return {
      score: score,
      passed: score >= 80,
      checks: checks,
      details: `${passedChecks}/${totalChecks} basic checks passed`
    }
  }

  // Validate content relevance
  validateRelevance(query, solution) {
    const queryWords = this.extractKeywords(query.toLowerCase())
    const solutionWords = this.extractKeywords(solution.toLowerCase())

    // Calculate keyword overlap
    const overlap = queryWords.filter(word => solutionWords.includes(word))
    const relevanceScore = queryWords.length > 0
      ? Math.round((overlap.length / queryWords.length) * 100)
      : 0

    // Check for direct question answering
    const answersQuestion = this.checksIfAnswersQuestion(query, solution)

    // Check for topic consistency
    const topicConsistent = this.checkTopicConsistency(query, solution)

    const finalScore = Math.min(100, relevanceScore + (answersQuestion ? 20 : 0) + (topicConsistent ? 15 : 0))

    return {
      score: finalScore,
      passed: finalScore >= 60,
      keywordOverlap: overlap.length,
      totalKeywords: queryWords.length,
      answersQuestion: answersQuestion,
      topicConsistent: topicConsistent,
      details: `${Math.round((overlap.length / queryWords.length) * 100)}% keyword relevance`
    }
  }

  // Validate completeness
  validateCompleteness(query, solution) {
    const completenessChecks = {
      addressesMainQuestion: this.addressesMainQuestion(query, solution),
      providesExamples: this.containsExamples(solution),
      hasConclusion: this.hasConclusion(solution),
      coversKeyAspects: this.coversKeyAspects(query, solution),
      appropriateLength: this.hasAppropriateLength(query, solution)
    }

    const passedChecks = Object.values(completenessChecks).filter(Boolean).length
    const totalChecks = Object.keys(completenessChecks).length
    const score = Math.round((passedChecks / totalChecks) * 100)

    return {
      score: score,
      passed: score >= 60,
      checks: completenessChecks,
      details: `${passedChecks}/${totalChecks} completeness criteria met`
    }
  }

  // Assess solution quality
  assessQuality(solution) {
    const qualityMetrics = {
      clarity: this.assessClarity(solution),
      structure: this.assessStructure(solution),
      depth: this.assessDepth(solution),
      accuracy: this.assessAccuracy(solution),
      usefulness: this.assessUsefulness(solution)
    }

    const averageScore = Object.values(qualityMetrics).reduce((sum, score) => sum + score, 0) / Object.keys(qualityMetrics).length

    return {
      score: Math.round(averageScore),
      passed: averageScore >= 70,
      metrics: qualityMetrics,
      details: `Average quality score across ${Object.keys(qualityMetrics).length} metrics`
    }
  }

  // Helper methods for validation checks
  isGenericResponse(solution) {
    const genericPhrases = [
      'i cannot help',
      'i don\'t know',
      'sorry, i cannot',
      'i\'m not able to',
      'this is a complex topic',
      'it depends'
    ]

    const lowerSolution = solution.toLowerCase()
    return genericPhrases.some(phrase => lowerSolution.includes(phrase)) && solution.length < 100
  }

  hasProperFormat(solution) {
    // Check for basic formatting elements
    const hasCapitalization = /[A-Z]/.test(solution)
    const hasPunctuation = /[.!?]/.test(solution)
    const notAllCaps = solution !== solution.toUpperCase()

    return hasCapitalization && hasPunctuation && notAllCaps
  }

  extractKeywords(text) {
    // Remove common stop words and extract meaningful keywords
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'])

    return text
      .split(/\s+/)
      .map(word => word.replace(/[^\w]/g, ''))
      .filter(word => word.length > 2 && !stopWords.has(word))
      .slice(0, 20) // Limit to top 20 keywords
  }

  checksIfAnswersQuestion(query, solution) {
    // Check if solution addresses question words
    const questionWords = ['what', 'how', 'why', 'when', 'where', 'who', 'which']
    const queryLower = query.toLowerCase()
    const solutionLower = solution.toLowerCase()

    const hasQuestionWord = questionWords.some(word => queryLower.includes(word))
    if (!hasQuestionWord) return true // Not a question, so this check passes

    // For questions, check if solution provides explanatory content
    const explanatoryWords = ['because', 'since', 'due to', 'therefore', 'thus', 'so', 'as a result', 'consequently']
    return explanatoryWords.some(word => solutionLower.includes(word)) || solution.length > 100
  }

  checkTopicConsistency(query, solution) {
    // Simple topic consistency check based on domain keywords
    const domains = {
      programming: ['code', 'function', 'variable', 'algorithm', 'programming', 'software', 'development'],
      math: ['calculate', 'equation', 'formula', 'number', 'mathematics', 'solve', 'problem'],
      science: ['research', 'study', 'experiment', 'theory', 'hypothesis', 'analysis', 'data'],
      business: ['strategy', 'market', 'customer', 'revenue', 'profit', 'business', 'company'],
      creative: ['design', 'create', 'art', 'creative', 'imagination', 'innovative', 'original']
    }

    const queryLower = query.toLowerCase()
    const solutionLower = solution.toLowerCase()

    for (const [domain, keywords] of Object.entries(domains)) {
      const queryHasDomain = keywords.some(keyword => queryLower.includes(keyword))
      const solutionHasDomain = keywords.some(keyword => solutionLower.includes(keyword))

      if (queryHasDomain && !solutionHasDomain) {
        return false // Query is about this domain but solution doesn't address it
      }
    }

    return true
  }

  // Additional validation helper methods
  addressesMainQuestion(query, solution) {
    // Extract main question/request from query
    const questionPatterns = [
      /how to ([^?]+)/i,
      /what is ([^?]+)/i,
      /why ([^?]+)/i,
      /explain ([^?]+)/i,
      /describe ([^?]+)/i
    ]

    for (const pattern of questionPatterns) {
      const match = query.match(pattern)
      if (match) {
        const topic = match[1].toLowerCase()
        return solution.toLowerCase().includes(topic)
      }
    }

    return true // If no clear question pattern, assume it's addressed
  }

  containsExamples(solution) {
    const exampleIndicators = [
      'for example', 'for instance', 'such as', 'like', 'including',
      'e.g.', 'i.e.', 'example:', 'examples:', '1.', '2.', '•', '-'
    ]

    const solutionLower = solution.toLowerCase()
    return exampleIndicators.some(indicator => solutionLower.includes(indicator))
  }

  hasConclusion(solution) {
    const conclusionWords = [
      'in conclusion', 'to summarize', 'in summary', 'therefore', 'thus',
      'finally', 'overall', 'to conclude', 'in the end'
    ]

    const solutionLower = solution.toLowerCase()
    return conclusionWords.some(word => solutionLower.includes(word)) ||
           solution.split('.').length > 3 // Multiple sentences suggest structure
  }

  coversKeyAspects(query, solution) {
    // Check if solution covers multiple aspects mentioned in query
    const queryAspects = query.split(/\band\b|\bor\b|,/).length
    const solutionAspects = solution.split(/\band\b|\bor\b|,/).length

    return queryAspects <= 1 || solutionAspects >= Math.min(queryAspects, 3)
  }

  hasAppropriateLength(query, solution) {
    const queryLength = query.length
    const solutionLength = solution.length

    // Simple heuristic: solution should be at least as long as query for complex questions
    if (queryLength > 100) return solutionLength >= queryLength * 0.8
    if (queryLength > 50) return solutionLength >= queryLength * 1.2
    return solutionLength >= 50 // Minimum reasonable response length
  }

  // Quality assessment methods
  assessClarity(solution) {
    let score = 70 // Base score

    // Check for clear structure
    if (/\d+\.|\•|-/.test(solution)) score += 10 // Has lists/bullets
    if (solution.split('\n').length > 2) score += 5 // Has paragraphs
    if (!/\b(um|uh|well|like)\b/i.test(solution)) score += 10 // No filler words
    if (solution.split('.').length > 2) score += 5 // Multiple sentences

    return Math.min(100, score)
  }

  assessStructure(solution) {
    let score = 60 // Base score

    // Check structural elements
    if (solution.includes('\n\n')) score += 15 // Paragraph breaks
    if (/^[A-Z]/.test(solution)) score += 10 // Starts with capital
    if (/[.!?]$/.test(solution.trim())) score += 10 // Ends with punctuation
    if (solution.length > 200) score += 5 // Substantial content

    return Math.min(100, score)
  }

  assessDepth(solution) {
    let score = 50 // Base score

    // Check for depth indicators
    if (solution.length > 300) score += 20 // Detailed response
    if (solution.includes('because') || solution.includes('since')) score += 15 // Explanations
    if (/\b(research|study|analysis|data)\b/i.test(solution)) score += 10 // Evidence-based
    if (solution.split('.').length > 5) score += 5 // Multiple points

    return Math.min(100, score)
  }

  assessAccuracy(solution) {
    // Basic accuracy checks (more sophisticated checks would require domain knowledge)
    let score = 80 // Assume accurate unless proven otherwise

    // Check for uncertainty indicators (which can be good)
    if (/\b(might|could|possibly|potentially|likely)\b/i.test(solution)) score += 5

    // Check for absolute statements (which might be problematic)
    if (/\b(always|never|all|none|every|completely)\b/i.test(solution)) score -= 10

    return Math.max(0, Math.min(100, score))
  }

  assessUsefulness(solution) {
    let score = 60 // Base score

    // Check for actionable content
    if (/\b(step|steps|how to|method|approach|way)\b/i.test(solution)) score += 15
    if (solution.includes('example') || solution.includes('instance')) score += 10
    if (/\d+\./.test(solution)) score += 10 // Numbered steps
    if (solution.length > 150) score += 5 // Substantial content

    return Math.min(100, score)
  }

  // Calculate overall validation score
  calculateOverallScore(validationResults) {
    const weights = {
      basic: 0.2,      // 20% - Must have basic quality
      relevance: 0.3,  // 30% - Must be relevant
      completeness: 0.2, // 20% - Should be complete
      quality: 0.2,    // 20% - Should be high quality
      reasoning: 0.1   // 10% - Reasoning consistency bonus
    }

    let weightedScore = 0
    let totalWeight = 0

    for (const [category, result] of Object.entries(validationResults)) {
      if (result && typeof result.score === 'number') {
        weightedScore += result.score * weights[category]
        totalWeight += weights[category]
      }
    }

    const finalScore = totalWeight > 0 ? Math.round(weightedScore / totalWeight) : 0

    return {
      score: finalScore,
      breakdown: validationResults,
      weights: weights
    }
  }

  // Generate improvement recommendations
  generateRecommendations(overallScore) {
    const recommendations = []
    const { breakdown } = overallScore

    if (breakdown.basic?.score < 80) {
      recommendations.push("Improve basic response quality - ensure proper formatting and sufficient content length")
    }

    if (breakdown.relevance?.score < 60) {
      recommendations.push("Increase relevance - address the specific question asked and use related terminology")
    }

    if (breakdown.completeness?.score < 60) {
      recommendations.push("Provide more complete answers - include examples, explanations, and conclusions")
    }

    if (breakdown.quality?.score < 70) {
      recommendations.push("Enhance response quality - improve clarity, structure, and depth of explanation")
    }

    if (breakdown.reasoning?.score < 70) {
      recommendations.push("Ensure reasoning consistency - align the response with the stated reasoning process")
    }

    if (recommendations.length === 0) {
      recommendations.push("Excellent response quality - all validation criteria met successfully")
    }

    return recommendations
  }

  // Validate reasoning consistency
  validateReasoningConsistency(solution, reasoningData) {
    if (!reasoningData || !reasoningData.reasoningSteps) {
      return { score: 100, passed: true, note: 'No reasoning data to validate' }
    }

    let score = 80 // Base score

    // Check if solution follows the reasoning steps
    const solutionLower = solution.toLowerCase()
    const reasoningSteps = reasoningData.reasoningSteps

    // Look for evidence of each reasoning step in the solution
    let stepsFollowed = 0
    reasoningSteps.forEach(step => {
      const stepKeywords = step.description.toLowerCase().split(' ').slice(0, 3)
      const hasEvidence = stepKeywords.some(keyword =>
        keyword.length > 3 && solutionLower.includes(keyword)
      )
      if (hasEvidence) stepsFollowed++
    })

    // Calculate consistency score
    if (reasoningSteps.length > 0) {
      const consistencyRatio = stepsFollowed / reasoningSteps.length
      score += Math.round(consistencyRatio * 20) // Up to 20 bonus points
    }

    // Check if solution addresses the complexity level appropriately
    const complexity = reasoningData.complexity
    if (complexity.level === 'very_complex' && solution.length < 200) {
      score -= 15 // Complex problems should have detailed solutions
    } else if (complexity.level === 'simple' && solution.length > 500) {
      score -= 5 // Simple problems don't need overly long solutions
    }

    return {
      score: Math.max(0, Math.min(100, score)),
      passed: score >= 70,
      stepsFollowed: stepsFollowed,
      totalSteps: reasoningSteps.length,
      consistencyRatio: reasoningSteps.length > 0 ? stepsFollowed / reasoningSteps.length : 1,
      details: `${stepsFollowed}/${reasoningSteps.length} reasoning steps reflected in solution`
    }
  }

  // Get validation statistics
  getValidationStats() {
    if (this.validationHistory.length === 0) {
      return { totalValidations: 0, averageScore: 0, passRate: 0 }
    }

    const totalValidations = this.validationHistory.length
    const averageScore = this.validationHistory.reduce((sum, v) => sum + v.result.score, 0) / totalValidations
    const passedValidations = this.validationHistory.filter(v => v.result.passed).length
    const passRate = (passedValidations / totalValidations) * 100

    return {
      totalValidations,
      averageScore: Math.round(averageScore),
      passRate: Math.round(passRate),
      recentValidations: this.validationHistory.slice(-5)
    }
  }
}

// Create singleton instance
const solutionValidator = new SolutionValidator()
export default solutionValidator