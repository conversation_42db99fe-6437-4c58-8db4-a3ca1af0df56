// Context Service for Semantic Search and Context-Aware Response Generation
import memoryService from './memoryService.js'

class ContextService {
  constructor() {
    this.contextWindow = 5 // Number of recent messages to consider
    this.semanticThreshold = 0.3 // Minimum similarity score for relevance
  }

  // Simple semantic similarity using keyword matching and context
  calculateSimilarity(text1, text2) {
    if (!text1 || !text2) return 0

    const words1 = this.extractKeywords(text1.toLowerCase())
    const words2 = this.extractKeywords(text2.toLowerCase())
    
    if (words1.length === 0 || words2.length === 0) return 0

    // Calculate Jaccard similarity
    const intersection = words1.filter(word => words2.includes(word))
    const union = [...new Set([...words1, ...words2])]
    
    return intersection.length / union.length
  }

  // Extract meaningful keywords from text
  extractKeywords(text) {
    // Remove common stop words
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those',
      'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your',
      'his', 'her', 'its', 'our', 'their', 'what', 'when', 'where', 'why', 'how', 'who', 'which'
    ])

    return text
      .replace(/[^\w\s]/g, ' ') // Remove punctuation
      .split(/\s+/) // Split by whitespace
      .filter(word => word.length > 2 && !stopWords.has(word)) // Filter meaningful words
      .slice(0, 20) // Limit to top 20 keywords
  }

  // Find relevant conversations based on current input
  findRelevantContext(currentInput, mode = 'chat', limit = 3) {
    try {
      const conversations = memoryService.getConversations()
      const relevantConversations = []

      for (const conversation of conversations) {
        // Skip if different mode (unless it's a general query)
        if (conversation.mode !== mode && mode !== 'chat') continue

        // Calculate relevance score
        let maxSimilarity = 0
        let relevantMessage = null

        for (const message of conversation.messages) {
          const similarity = this.calculateSimilarity(currentInput, message.content)
          if (similarity > maxSimilarity) {
            maxSimilarity = similarity
            relevantMessage = message
          }
        }

        // Include if above threshold
        if (maxSimilarity > this.semanticThreshold) {
          relevantConversations.push({
            conversation,
            similarity: maxSimilarity,
            relevantMessage,
            timestamp: conversation.timestamp
          })
        }
      }

      // Sort by similarity and recency
      return relevantConversations
        .sort((a, b) => {
          // Weight similarity higher than recency
          const scoreA = a.similarity * 0.7 + (new Date(a.timestamp).getTime() / Date.now()) * 0.3
          const scoreB = b.similarity * 0.7 + (new Date(b.timestamp).getTime() / Date.now()) * 0.3
          return scoreB - scoreA
        })
        .slice(0, limit)
    } catch (error) {
      console.error('❌ Failed to find relevant context:', error)
      return []
    }
  }

  // Build enhanced context for AI response generation
  buildEnhancedContext(currentInput, conversationHistory = [], mode = 'chat') {
    try {
      const preferences = memoryService.getPreferences()
      const relevantContext = this.findRelevantContext(currentInput, mode)
      const recentConversations = memoryService.getRecentConversations(2)

      // Build context summary
      const contextSummary = {
        userPreferences: this.formatPreferences(preferences),
        relevantHistory: this.formatRelevantHistory(relevantContext),
        recentPatterns: this.analyzeRecentPatterns(recentConversations),
        conversationFlow: this.analyzeConversationFlow(conversationHistory)
      }

      console.log('🧠 Enhanced context built:', {
        relevantContextCount: relevantContext.length,
        preferencesLoaded: Object.keys(preferences).length,
        recentConversations: recentConversations.length
      })

      return contextSummary
    } catch (error) {
      console.error('❌ Failed to build enhanced context:', error)
      return null
    }
  }

  // Format user preferences for AI context
  formatPreferences(preferences) {
    return {
      responseStyle: preferences.responseStyle,
      conversationTone: preferences.conversationTone,
      preferredDepth: preferences.analysisDepth,
      codeLanguage: preferences.codeLanguagePreference,
      imageStyle: preferences.imageDescriptionStyle
    }
  }

  // Format relevant history for AI context
  formatRelevantHistory(relevantContext) {
    if (relevantContext.length === 0) return null

    return relevantContext.map(ctx => ({
      topic: this.extractMainTopic(ctx.relevantMessage.content),
      userMessage: ctx.relevantMessage.role === 'user' ? ctx.relevantMessage.content.substring(0, 100) : null,
      aiResponse: ctx.relevantMessage.role === 'assistant' ? ctx.relevantMessage.content.substring(0, 100) : null,
      similarity: Math.round(ctx.similarity * 100),
      timeAgo: this.getTimeAgo(ctx.timestamp)
    }))
  }

  // Extract main topic from message
  extractMainTopic(content) {
    const keywords = this.extractKeywords(content)
    return keywords.slice(0, 3).join(', ') || 'general discussion'
  }

  // Analyze recent conversation patterns
  analyzeRecentPatterns(recentConversations) {
    if (recentConversations.length === 0) return null

    const patterns = {
      mostUsedMode: this.getMostUsedMode(recentConversations),
      averageMessageLength: this.getAverageMessageLength(recentConversations),
      commonTopics: this.getCommonTopics(recentConversations),
      sessionFrequency: recentConversations.length
    }

    return patterns
  }

  // Analyze current conversation flow
  analyzeConversationFlow(conversationHistory) {
    if (conversationHistory.length === 0) return null

    const recentMessages = conversationHistory.slice(-this.contextWindow)
    
    return {
      messageCount: conversationHistory.length,
      recentTopic: this.extractMainTopic(recentMessages[recentMessages.length - 1]?.content || ''),
      conversationProgression: this.getConversationProgression(recentMessages),
      userEngagement: this.assessUserEngagement(recentMessages)
    }
  }

  // Get most used mode from recent conversations
  getMostUsedMode(conversations) {
    const modes = conversations.map(conv => conv.mode)
    const modeCount = modes.reduce((acc, mode) => {
      acc[mode] = (acc[mode] || 0) + 1
      return acc
    }, {})
    
    return Object.keys(modeCount).reduce((a, b) => modeCount[a] > modeCount[b] ? a : b, 'chat')
  }

  // Get average message length
  getAverageMessageLength(conversations) {
    const totalMessages = conversations.reduce((sum, conv) => sum + conv.messageCount, 0)
    return totalMessages / conversations.length || 0
  }

  // Get common topics from conversations
  getCommonTopics(conversations) {
    const allKeywords = []
    conversations.forEach(conv => {
      if (conv.lastMessage) {
        allKeywords.push(...this.extractKeywords(conv.lastMessage))
      }
    })

    // Count keyword frequency
    const keywordCount = allKeywords.reduce((acc, keyword) => {
      acc[keyword] = (acc[keyword] || 0) + 1
      return acc
    }, {})

    // Return top 3 most common topics
    return Object.entries(keywordCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([keyword]) => keyword)
  }

  // Get conversation progression
  getConversationProgression(messages) {
    if (messages.length < 2) return 'starting'
    
    const userMessages = messages.filter(msg => msg.role === 'user')
    const avgLength = userMessages.reduce((sum, msg) => sum + msg.content.length, 0) / userMessages.length
    
    if (avgLength > 100) return 'detailed'
    if (avgLength > 50) return 'engaged'
    return 'brief'
  }

  // Assess user engagement level
  assessUserEngagement(messages) {
    const userMessages = messages.filter(msg => msg.role === 'user')
    if (userMessages.length === 0) return 'unknown'
    
    const hasQuestions = userMessages.some(msg => msg.content.includes('?'))
    const hasFollowUp = userMessages.length > 1
    const hasSpecificRequests = userMessages.some(msg => 
      msg.content.toLowerCase().includes('can you') || 
      msg.content.toLowerCase().includes('please') ||
      msg.content.toLowerCase().includes('how to')
    )
    
    if (hasQuestions && hasFollowUp && hasSpecificRequests) return 'high'
    if (hasQuestions || hasFollowUp) return 'medium'
    return 'low'
  }

  // Get human-readable time ago
  getTimeAgo(timestamp) {
    const now = new Date()
    const past = new Date(timestamp)
    const diffMs = now - past
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMs / 3600000)
    const diffDays = Math.floor(diffMs / 86400000)
    
    if (diffMins < 60) return `${diffMins} minutes ago`
    if (diffHours < 24) return `${diffHours} hours ago`
    return `${diffDays} days ago`
  }

  // Search conversations with semantic similarity
  semanticSearch(query, limit = 5) {
    try {
      const conversations = memoryService.getConversations()
      const results = []

      for (const conversation of conversations) {
        for (const message of conversation.messages) {
          const similarity = this.calculateSimilarity(query, message.content)
          if (similarity > this.semanticThreshold) {
            results.push({
              conversation,
              message,
              similarity,
              timestamp: conversation.timestamp
            })
          }
        }
      }

      return results
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit)
    } catch (error) {
      console.error('❌ Semantic search failed:', error)
      return []
    }
  }
}

// Create singleton instance
const contextService = new ContextService()
export default contextService
