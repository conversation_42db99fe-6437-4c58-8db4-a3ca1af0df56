// Google Gemini API Service with Tool Integration
import { GoogleGenerativeAI } from '@google/generative-ai'
import toolService from './toolService.js'

class GeminiService {
  constructor() {
    // Handle both browser (Vite) and Node.js environments
    this.apiKey = (typeof import.meta !== 'undefined' && import.meta.env)
      ? import.meta.env.VITE_GEMINI_API_KEY
      : process.env.VITE_GEMINI_API_KEY || 'demo-key-for-testing'
    this.genAI = null
    this.model = null
    this.currentModel = null
    this.isInitialized = false
    this.requestCount = 0
    this.lastRequestTime = 0
    this.rateLimitDelay = 2000 // 2 seconds between requests (increased for free tier)
  }

  // Get optimal model for task type
  getOptimalModel(mode, inputLength = 0) {
    // Model selection based on task complexity and type
    // NOTE: Using Flash model for all modes due to free tier quota limits
    // TODO: Switch to Pro model for complex tasks when on paid plan
    const modelConfig = {
      chat: {
        model: 'gemini-1.5-flash', // Fast for conversational responses
        reason: 'Optimized for quick, conversational responses'
      },
      code: {
        model: 'gemini-1.5-flash', // Using Flash due to quota limits
        reason: inputLength > 500 ? 'Flash model (Pro quota exceeded)' : 'Flash model for code generation'
      },
      image: {
        model: 'gemini-1.5-flash', // Using Flash due to quota limits
        reason: 'Flash model for image descriptions (Pro quota exceeded)'
      },
      analysis: {
        model: 'gemini-1.5-flash', // Using Flash due to quota limits
        reason: 'Flash model for analysis (Pro quota exceeded)'
      }
    }

    return modelConfig[mode] || modelConfig.chat
  }

  // Initialize the Gemini API with dynamic model selection
  async initialize(mode = 'chat', inputLength = 0) {
    const modelConfig = this.getOptimalModel(mode, inputLength)
    const modelName = modelConfig.model

    // Only reinitialize if model changed or not initialized
    if (this.isInitialized && this.currentModel === modelName) return

    console.log('🔧 Initializing Gemini API...')
    console.log('🔑 API Key present:', !!this.apiKey)
    console.log('🔑 API Key length:', this.apiKey ? this.apiKey.length : 0)
    console.log('🤖 Selected model:', modelName, 'for mode:', mode)

    if (!this.apiKey || this.apiKey === 'demo-key-for-testing') {
      console.log('⚠️ Running in demo mode - using simulated responses')
      this.demoMode = true
      this.isInitialized = true
      this.currentModel = modelName
      return
    }

    try {
      this.genAI = new GoogleGenerativeAI(this.apiKey)
      this.model = this.genAI.getGenerativeModel({ model: modelName })
      this.currentModel = modelName
      this.isInitialized = true
      console.log(`✅ Gemini API initialized with model: ${modelName}`)
      console.log(`📋 Model selection reason: ${modelConfig.reason}`)
    } catch (error) {
      console.error('❌ Failed to initialize Gemini API:', error)
      throw new Error(`Failed to initialize Gemini API: ${error.message}`)
    }
  }

  // Rate limiting to prevent API abuse
  async enforceRateLimit() {
    const now = Date.now()
    const timeSinceLastRequest = now - this.lastRequestTime
    
    if (timeSinceLastRequest < this.rateLimitDelay) {
      const waitTime = this.rateLimitDelay - timeSinceLastRequest
      await new Promise(resolve => setTimeout(resolve, waitTime))
    }
    
    this.lastRequestTime = Date.now()
    this.requestCount++
  }

  // Generate demo response for testing
  generateDemoResponse(input, mode) {
    console.log(`🎭 Generating demo response for ${mode} mode`)

    const demoResponses = {
      'chat': 'This is a simulated response for testing purposes. The multi-agent system is working correctly in demo mode.',
      'research': 'Research findings: The topic shows significant growth trends with 85% positive indicators. Key insights include market expansion and technological advancement.',
      'analysis': 'Analysis results: Data shows strong correlation patterns with 92% confidence. Trends indicate positive trajectory with measurable improvements.',
      'creative': 'Creative output: Innovative solution combining multiple approaches. The concept demonstrates originality and practical application potential.',
      'coding': 'Code implementation: Successfully generated functional code with proper error handling and optimization. All requirements met.',
      'synthesis': 'Synthesis complete: Combined multiple perspectives into coherent framework. Quality assessment shows high coherence and completeness.',
      'coordination': 'Coordination plan: Multi-agent workflow established with clear responsibilities and timelines. Execution strategy optimized for efficiency.',
      'logical_reasoning': 'Logical analysis: Applied systematic reasoning to evaluate options. Conclusion supported by evidence and sound methodology.',
      'enhancement': 'Enhanced version: Improved original content with better structure, clarity, and comprehensive coverage of key points.'
    }

    const content = demoResponses[mode] || demoResponses['chat']

    return {
      content,
      mode,
      timestamp: new Date(),
      requestId: this.requestCount,
      demo: true
    }
  }

  // Generate response using Gemini
  async generateResponse(input, mode = 'chat', conversationHistory = []) {
    await this.initialize(mode, input.length)
    await this.enforceRateLimit()

    // Handle demo mode
    if (this.demoMode) {
      return this.generateDemoResponse(input, mode)
    }

    try {
      const systemPrompt = this.getSystemPrompt(mode)
      const contextualInput = this.buildContextualInput(input, conversationHistory, systemPrompt)

      console.log(`🤖 Sending request to Gemini (${mode} mode)...`)
      console.log('📝 Input length:', contextualInput.length)

      const result = await this.model.generateContent(contextualInput)
      const response = result.response

      // Check if response was blocked
      if (response.promptFeedback && response.promptFeedback.blockReason) {
        throw new Error(`Content blocked: ${response.promptFeedback.blockReason}`)
      }

      const text = response.text()
      console.log('✅ Received response from Gemini')
      console.log('📄 Response length:', text.length)

      return {
        content: text,
        mode: mode,
        timestamp: new Date(),
        requestId: this.requestCount
      }
    } catch (error) {
      console.error('❌ Gemini API error details:', {
        message: error.message,
        name: error.name,
        status: error.status,
        statusText: error.statusText,
        stack: error.stack,
        fullError: error
      })
      return this.handleError(error, mode)
    }
  }

  // Generate context-aware response using enhanced context
  async generateContextAwareResponse(input, mode = 'chat', conversationHistory = [], enhancedContext = null) {
    await this.initialize(mode, input.length)
    await this.enforceRateLimit()

    try {
      const systemPrompt = this.getSystemPrompt(mode)
      const contextualInput = this.buildContextualInput(input, conversationHistory, systemPrompt, enhancedContext)

      console.log(`🧠 Sending context-aware request to Gemini (${mode} mode)...`)
      console.log('📝 Input length:', contextualInput.length)
      console.log('🔍 Enhanced context:', enhancedContext ? 'Yes' : 'No')

      const result = await this.model.generateContent(contextualInput)
      const response = result.response

      // Check if response was blocked
      if (response.promptFeedback && response.promptFeedback.blockReason) {
        throw new Error(`Content blocked: ${response.promptFeedback.blockReason}`)
      }

      const text = response.text()
      console.log('✅ Received context-aware response from Gemini')
      console.log('📄 Response length:', text.length)

      return {
        content: text,
        mode: mode,
        timestamp: new Date(),
        requestId: this.requestCount,
        contextEnhanced: !!enhancedContext
      }
    } catch (error) {
      console.error('❌ Context-aware Gemini API error:', error)
      return this.handleError(error, mode)
    }
  }

  // Generate tool-enhanced response with external capabilities
  async generateToolEnhancedResponse(input, mode = 'chat', conversationHistory = [], enhancedContext = null) {
    await this.initialize(mode, input.length)

    try {
      console.log(`🔧 Analyzing input for tool usage...`)

      // Check if this is a reasoning-enhanced prompt (skip tools for reasoning)
      const isReasoningPrompt = input.includes('chain-of-thought reasoning') ||
                               input.includes('My Reasoning Approach:') ||
                               input.includes('systematic approach will ensure')

      if (isReasoningPrompt) {
        console.log(`🧠 Detected reasoning-enhanced prompt - skipping tool analysis`)
        // Skip tool analysis for reasoning prompts and go directly to generation
        return await this.generateResponse(input, mode, conversationHistory, enhancedContext)
      }

      // Analyze if tools should be used
      const toolAnalysis = toolService.analyzeInput(input)
      let toolResults = []

      console.log(`🔧 Tool analysis result:`, toolAnalysis)

      // Execute tools if suggested
      if (toolAnalysis.suggestedTools.length > 0) {
        console.log(`🔧 Suggested tools:`, toolAnalysis.suggestedTools)
        console.log(`🔧 Tool confidence scores:`, toolAnalysis.confidence)

        for (const toolName of toolAnalysis.suggestedTools.slice(0, 2)) { // Limit to 2 tools
          const confidence = toolAnalysis.confidence[toolName] || 0.5

          console.log(`🔧 Checking tool: ${toolName} with confidence: ${confidence}`)

          if (confidence > 0.3) { // Lowered threshold for testing
            console.log(`🔧 Executing tool: ${toolName} (confidence: ${confidence})`)

            let toolResult
            if (toolName === 'web_search') {
              console.log(`🔍 Executing web search for: ${input}`)
              toolResult = await toolService.executeTool('web_search', input)
              console.log(`🔍 Web search result:`, toolResult)
            } else if (toolName === 'code_execution') {
              // Extract code from input if present
              let codeMatch = input.match(/```[\w]*\n([\s\S]*?)```/)
              let code, language = 'javascript'

              if (codeMatch) {
                // Code in markdown format
                code = codeMatch[1]
                language = input.match(/```(\w+)/)?.[1] || 'javascript'
              } else {
                // For raw code input, try to extract the complete executable code
                // First, check if the entire input looks like code
                const codeIndicators = [
                  /function\s+\w+/,
                  /console\.log/,
                  /def\s+\w+/,
                  /print\s*\(/,
                  /class\s+\w+/,
                  /import\s+/,
                  /const\s+\w+\s*=/,
                  /let\s+\w+\s*=/,
                  /var\s+\w+\s*=/
                ]

                const looksLikeCode = codeIndicators.some(pattern => pattern.test(input))

                if (looksLikeCode) {
                  // If the entire input looks like code, use it all
                  code = input.trim()
                } else {
                  // Look for specific code patterns
                  const patterns = [
                    /function\s+\w+\([^)]*\)\s*{[^}]*}.*?console\.log\([^)]+\)/s, // Function + console.log
                    /console\.log\([^)]+\)/,
                    /function\s+\w+\([^)]*\)\s*{[^}]*}/,
                    /\w+\s*=\s*[^;]+;?/,
                    /for\s*\([^)]+\)\s*{[^}]*}/,
                    /if\s*\([^)]+\)\s*{[^}]*}/
                  ]

                  for (const pattern of patterns) {
                    const match = input.match(pattern)
                    if (match) {
                      code = match[0]
                      break
                    }
                  }

                  // If no specific pattern, extract everything after "code:" or similar
                  if (!code) {
                    const codeKeywords = ['code:', 'run:', 'execute:', 'try:']
                    for (const keyword of codeKeywords) {
                      const index = input.toLowerCase().indexOf(keyword)
                      if (index !== -1) {
                        code = input.substring(index + keyword.length).trim()
                        break
                      }
                    }
                  }
                }
              }

              if (code) {
                console.log(`💻 Executing code: ${code}`)
                toolResult = await toolService.executeTool('code_execution', code, language)
              } else {
                console.log(`💻 No executable code found in input`)
              }
            } else if (toolName === 'file_operations') {
              // File operations would be handled differently based on context
              console.log('📁 File operations require user interaction')
            }

            if (toolResult) {
              console.log(`✅ Tool ${toolName} executed successfully`)
              toolResults.push({
                toolName,
                result: toolResult,
                formatted: toolService.formatToolResults(toolName, toolResult)
              })
            } else {
              console.log(`❌ Tool ${toolName} returned no result`)
            }
          } else {
            console.log(`⏭️ Skipping tool ${toolName} due to low confidence: ${confidence}`)
          }
        }
      }

      // Build enhanced prompt with tool results
      let enhancedInput = input
      if (toolResults.length > 0) {
        enhancedInput += '\n\nTool Results:\n'
        toolResults.forEach(tr => {
          enhancedInput += `\n${tr.formatted}\n`
        })
        enhancedInput += '\nPlease incorporate these tool results into your response.'
      }

      await this.enforceRateLimit()

      const systemPrompt = this.getSystemPrompt(mode)
      const contextualInput = this.buildContextualInput(enhancedInput, conversationHistory, systemPrompt, enhancedContext)

      console.log(`🤖 Sending tool-enhanced request to Gemini (${mode} mode)...`)
      console.log('🔧 Tools used:', toolResults.map(tr => tr.toolName))

      const result = await this.model.generateContent(contextualInput)
      const response = result.response

      // Check if response was blocked
      if (response.promptFeedback && response.promptFeedback.blockReason) {
        throw new Error(`Content blocked: ${response.promptFeedback.blockReason}`)
      }

      const text = response.text()
      console.log('✅ Received tool-enhanced response from Gemini')
      console.log('📄 Response length:', text.length)

      return {
        content: text,
        mode: mode,
        timestamp: new Date(),
        requestId: this.requestCount,
        contextEnhanced: !!enhancedContext,
        toolsUsed: toolResults.map(tr => tr.toolName),
        toolResults: toolResults
      }
    } catch (error) {
      console.error('❌ Tool-enhanced Gemini API error:', error)
      return this.handleError(error, mode)
    }
  }

  // Generate streaming response (for real-time feel)
  async generateStreamingResponse(input, mode = 'chat', conversationHistory = [], onChunk) {
    await this.initialize(mode, input.length)
    await this.enforceRateLimit()

    try {
      const systemPrompt = this.getSystemPrompt(mode)
      const contextualInput = this.buildContextualInput(input, conversationHistory, systemPrompt)

      console.log(`🤖 Starting streaming response from Gemini (${mode} mode)...`)
      
      const result = await this.model.generateContentStream(contextualInput)
      let fullText = ''

      for await (const chunk of result.stream) {
        const chunkText = chunk.text()
        fullText += chunkText
        
        if (onChunk) {
          onChunk(chunkText, fullText)
        }
      }

      console.log('✅ Streaming response completed')
      
      return {
        content: fullText,
        mode: mode,
        timestamp: new Date(),
        requestId: this.requestCount
      }
    } catch (error) {
      console.error('❌ Gemini streaming error:', error)
      return this.handleError(error, mode)
    }
  }

  // Get system prompt based on mode
  getSystemPrompt(mode) {
    const prompts = {
      chat: `You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user questions. Be conversational and informative.`,

      code: `You are an expert programming assistant. Generate clean, working, production-ready code with the following requirements:

1. **Code Quality**: Write clean, readable, well-structured code
2. **Comments**: Include meaningful comments explaining complex logic
3. **Best Practices**: Follow language-specific conventions and best practices
4. **Error Handling**: Include appropriate error handling where needed
5. **Documentation**: Provide clear explanations of what the code does
6. **Examples**: Include usage examples when helpful

Format your response as:
1. Brief explanation of the approach
2. The complete, working code
3. Usage example (if applicable)
4. Any important notes or considerations

Always specify the programming language and ensure the code is complete and runnable.`,

      image: `You are a professional image prompt engineer specializing in creating detailed descriptions for AI image generation tools. Create comprehensive, vivid prompts that include:

**VISUAL ELEMENTS:**
- Subject/main focus with specific details
- Composition and framing (close-up, wide shot, etc.)
- Pose, expression, and positioning
- Clothing, accessories, and props

**TECHNICAL SPECIFICATIONS:**
- Art style (photorealistic, digital art, oil painting, etc.)
- Camera settings (depth of field, lighting type)
- Resolution and quality descriptors
- Color palette and mood

**ENVIRONMENT & ATMOSPHERE:**
- Background and setting details
- Lighting conditions (golden hour, studio lighting, etc.)
- Weather and atmospheric effects
- Time of day and season

**ARTISTIC DIRECTION:**
- Artistic influences or styles
- Mood and emotion
- Perspective and viewpoint
- Special effects or techniques

Format as a single, detailed prompt optimized for DALL-E 3, Midjourney v6, or Stable Diffusion XL. Include technical keywords that improve generation quality.`,

      analysis: `You are a professional strategic analyst and consultant. Provide comprehensive, structured analysis using this format:

## 🎯 EXECUTIVE SUMMARY
Brief overview of key findings and recommendations

## 📊 SITUATION ANALYSIS
### Current State
- Key facts and data points
- Current challenges and opportunities
- Stakeholder perspectives

### Context & Background
- Historical context
- Market/industry factors
- External influences

## 🔍 DETAILED ANALYSIS
### Strengths
- Positive factors and advantages
- Core competencies
- Success factors

### Weaknesses
- Areas for improvement
- Limitations and constraints
- Risk factors

### Opportunities
- Growth potential
- Emerging trends
- Strategic possibilities

### Threats
- Potential risks
- Competitive challenges
- External threats

## 💡 KEY INSIGHTS
- Critical findings
- Patterns and trends
- Cause-and-effect relationships

## 🚀 RECOMMENDATIONS
### Immediate Actions (0-3 months)
- Priority initiatives
- Quick wins
- Resource requirements

### Medium-term Strategy (3-12 months)
- Strategic initiatives
- Implementation roadmap
- Success metrics

### Long-term Vision (1+ years)
- Future opportunities
- Strategic positioning
- Transformation goals

## 📈 SUCCESS METRICS
- Key performance indicators
- Measurement criteria
- Timeline for evaluation

Use clear headings, bullet points, and actionable language. Provide specific, practical recommendations.`
    }

    return prompts[mode] || prompts.chat
  }

  // Build contextual input with conversation history and enhanced context
  buildContextualInput(input, conversationHistory, systemPrompt, enhancedContext = null) {
    let contextualInput = systemPrompt + '\n\n'

    // Add enhanced context if available
    if (enhancedContext) {
      contextualInput += this.formatEnhancedContext(enhancedContext) + '\n\n'
    }

    // Add adaptive behavior instructions if available
    if (enhancedContext?.adaptiveBehavior) {
      contextualInput += this.formatAdaptiveBehavior(enhancedContext.adaptiveBehavior) + '\n\n'
    }

    // Add recent conversation history for context (last 5 messages)
    if (conversationHistory.length > 0) {
      const recentHistory = conversationHistory.slice(-5)
      contextualInput += 'Recent conversation context:\n'

      recentHistory.forEach(message => {
        const role = message.role === 'user' ? 'Human' : 'AGI'
        contextualInput += `${role}: ${message.content}\n`
      })

      contextualInput += '\n'
    }

    contextualInput += `Current request: ${input}`

    return contextualInput
  }

  // Format adaptive behavior instructions for AI prompt
  formatAdaptiveBehavior(adaptiveBehavior) {
    let instructions = 'ADAPTIVE RESPONSE GUIDELINES (based on user feedback learning):\n'

    // Response style instructions
    if (adaptiveBehavior.responseStyle !== 'balanced') {
      const styleInstructions = {
        'clear': 'Use clear, simple language and avoid jargon. Break down complex concepts.',
        'concise': 'Be brief and to the point. Avoid unnecessary elaboration.',
        'technical': 'Use technical terminology and detailed explanations appropriate for experts.',
        'casual': 'Use a conversational, friendly tone. Be approachable and relatable.'
      }

      if (styleInstructions[adaptiveBehavior.responseStyle]) {
        instructions += `- Style: ${styleInstructions[adaptiveBehavior.responseStyle]}\n`
      }
    }

    // Detail level instructions
    if (adaptiveBehavior.detailLevel !== 'medium') {
      const detailInstructions = {
        'high': 'Provide comprehensive, detailed explanations with thorough coverage of the topic.',
        'low': 'Keep explanations brief and focus only on essential information.'
      }

      if (detailInstructions[adaptiveBehavior.detailLevel]) {
        instructions += `- Detail Level: ${detailInstructions[adaptiveBehavior.detailLevel]}\n`
      }
    }

    // Examples preference
    if (adaptiveBehavior.includeExamples) {
      instructions += '- Include practical examples and demonstrations to illustrate concepts.\n'
    }

    // Format preference
    if (adaptiveBehavior.formatPreference !== 'structured') {
      const formatInstructions = {
        'step_by_step': 'Present information in clear, numbered steps when appropriate.',
        'comprehensive': 'Use detailed sections with headings and thorough coverage.',
        'bullet_points': 'Use bullet points and lists for better readability.'
      }

      if (formatInstructions[adaptiveBehavior.formatPreference]) {
        instructions += `- Format: ${formatInstructions[adaptiveBehavior.formatPreference]}\n`
      }
    }

    // Reasoning preference
    if (adaptiveBehavior.useReasoning === 'enhanced') {
      instructions += '- Reasoning: Provide extra clear explanations of your reasoning process.\n'
    }

    // Add applied adaptations
    if (adaptiveBehavior.adaptations.length > 0) {
      instructions += `- Applied Adaptations: ${adaptiveBehavior.adaptations.join(', ')}\n`
    }

    // Add model adaptation instructions if available
    if (adaptiveBehavior.modelAdaptation && adaptiveBehavior.modelAdaptation.hasAdaptations) {
      instructions += '\nMODEL ADAPTATION PARAMETERS (based on user feedback learning):\n'

      const adaptation = adaptiveBehavior.modelAdaptation

      if (adaptation.qualityFocus === 'high') {
        instructions += '- PRIORITY: Focus on high-quality, accurate responses\n'
      }

      // Add learned preferences
      Object.entries(adaptation.preferences).forEach(([key, value]) => {
        const prefInstructions = {
          'responseLength': value === 'shorter' ? 'Keep responses concise and to the point' : 'Provide detailed, comprehensive responses',
          'complexity': value === 'simple' ? 'Use simple language and avoid technical jargon' : 'Use technical terminology and detailed explanations',
          'format': value === 'step_by_step' ? 'Present information in clear, numbered steps' : 'Use structured formatting',
          'tone': value === 'casual' ? 'Use a friendly, conversational tone' : 'Maintain a professional, formal tone',
          'includeExamples': value ? 'Include practical examples and demonstrations' : 'Focus on explanations without examples'
        }

        if (prefInstructions[key]) {
          instructions += `- ${prefInstructions[key]}\n`
        }
      })

      // Add behavior modifications
      adaptation.behaviorModifications.forEach(mod => {
        if (mod.weight > 0.5) {
          const modInstructions = {
            'quality_enhancement': 'Pay extra attention to accuracy and completeness',
            'clarity_improvement': 'Use clear, simple language and logical structure',
            'completeness_improvement': 'Ensure all aspects of the question are thoroughly addressed',
            'usefulness_improvement': 'Focus on practical, actionable information',
            'engagement_improvement': 'Make responses more engaging and interesting'
          }

          if (modInstructions[mod.target]) {
            instructions += `- ${modInstructions[mod.target]}\n`
          }
        }
      })
    }

    return instructions
  }

  // Format enhanced context for AI prompt
  formatEnhancedContext(context) {
    let formattedContext = 'CONTEXT & PERSONALIZATION:\n'

    // User preferences
    if (context.userPreferences) {
      formattedContext += `User Preferences: ${JSON.stringify(context.userPreferences)}\n`
    }

    // Relevant history
    if (context.relevantHistory && context.relevantHistory.length > 0) {
      formattedContext += 'Relevant Past Conversations:\n'
      context.relevantHistory.forEach((item, index) => {
        formattedContext += `${index + 1}. Topic: ${item.topic} (${item.similarity}% relevant, ${item.timeAgo})\n`
      })
    }

    // Recent patterns
    if (context.recentPatterns) {
      formattedContext += `Recent Usage Patterns: ${JSON.stringify(context.recentPatterns)}\n`
    }

    // Conversation flow
    if (context.conversationFlow) {
      formattedContext += `Current Conversation: ${JSON.stringify(context.conversationFlow)}\n`
    }

    return formattedContext
  }

  // Handle API errors gracefully
  handleError(error, mode) {
    let fallbackMessage = ''

    console.log('🔍 Analyzing error:', error)

    if (error.message?.includes('API key') || error.message?.includes('API_KEY')) {
      fallbackMessage = 'API key error. Please check your Gemini API configuration.'
    } else if (error.message?.includes('quota') || error.message?.includes('QUOTA')) {
      fallbackMessage = 'API quota exceeded. Please try again later.'
    } else if (error.message?.includes('rate limit') || error.message?.includes('RATE_LIMIT')) {
      fallbackMessage = 'Rate limit reached. Please wait a moment before trying again.'
    } else if (error.message?.includes('SAFETY') || error.message?.includes('safety')) {
      fallbackMessage = 'Content was blocked by safety filters. Please try rephrasing your request.'
    } else if (error.message?.includes('RECITATION') || error.message?.includes('recitation')) {
      fallbackMessage = 'Content may contain copyrighted material. Please try a different request.'
    } else if (error.status === 400) {
      fallbackMessage = `Bad request error: ${error.message}. Please check your input.`
    } else if (error.status === 403) {
      fallbackMessage = `Access denied: ${error.message}. Please check your API key permissions.`
    } else if (error.status === 429) {
      fallbackMessage = `Rate limit exceeded: ${error.message}. Please wait before trying again.`
    } else if (error.status === 500) {
      fallbackMessage = `Server error: ${error.message}. Please try again later.`
    } else {
      // Show the actual error message for debugging
      fallbackMessage = `API Error: ${error.message || 'Unknown error occurred'}. Please check the console for details.`
    }

    return {
      content: fallbackMessage,
      mode: mode,
      timestamp: new Date(),
      error: true,
      originalError: error.message,
      errorStatus: error.status,
      errorDetails: error
    }
  }

  // Get API usage statistics
  getUsageStats() {
    return {
      requestCount: this.requestCount,
      isInitialized: this.isInitialized,
      lastRequestTime: this.lastRequestTime,
      rateLimitDelay: this.rateLimitDelay
    }
  }

  // Reset usage statistics
  resetStats() {
    this.requestCount = 0
    this.lastRequestTime = 0
  }
}

// Create singleton instance
const geminiService = new GeminiService()

export default geminiService
