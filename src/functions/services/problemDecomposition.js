// Problem Decomposition Algorithm - Advanced problem breaking and analysis
class ProblemDecomposition {
  constructor() {
    this.name = 'problem_decomposition'
    this.version = '1.0.0'
    this.maxDepth = 5 // Maximum decomposition depth
    this.minProblemSize = 10 // Minimum characters for a sub-problem
  }

  // Main decomposition entry point
  async decompose(problem, options = {}) {
    console.log('🔍 Starting problem decomposition...')

    const config = {
      maxDepth: options.maxDepth || this.maxDepth,
      strategy: options.strategy || 'auto',
      context: options.context || {},
      preserveOrder: options.preserveOrder !== false
    }

    try {
      // Step 1: Analyze problem structure
      const structure = this.analyzeProblemStructure(problem)
      
      // Step 2: Choose decomposition strategy
      const strategy = this.selectDecompositionStrategy(problem, structure, config.strategy)
      
      // Step 3: Apply decomposition
      const decomposition = await this.applyDecomposition(problem, strategy, config)
      
      // Step 4: Validate and optimize
      const optimized = this.optimizeDecomposition(decomposition)
      
      // Step 5: Create execution plan
      const executionPlan = this.createExecutionPlan(optimized)

      const result = {
        original: problem,
        strategy: strategy,
        structure: structure,
        decomposition: optimized,
        executionPlan: executionPlan,
        metadata: {
          timestamp: new Date().toISOString(),
          totalSubProblems: optimized.subProblems.length,
          estimatedComplexity: optimized.complexity,
          decompositionDepth: this.calculateDepth(optimized)
        }
      }

      console.log('✅ Problem decomposition complete:', optimized.subProblems.length, 'sub-problems')
      return result

    } catch (error) {
      console.error('❌ Problem decomposition failed:', error)
      return {
        original: problem,
        error: error.message,
        fallback: this.createFallbackDecomposition(problem)
      }
    }
  }

  // Analyze problem structure
  analyzeProblemStructure(problem) {
    const analysis = {
      // Basic metrics
      length: problem.length,
      sentences: problem.split(/[.!?]+/).filter(s => s.trim().length > 0).length,
      words: problem.split(/\s+/).length,
      
      // Structural patterns
      hasEnumeration: /\d+\.\s|\b(first|second|third|next|then|finally)\b/i.test(problem),
      hasConditionals: /\b(if|when|unless|provided|assuming)\b/i.test(problem),
      hasComparisons: /\b(compare|versus|vs|better|worse|different|similar)\b/i.test(problem),
      hasSequence: /\b(step|process|procedure|method|approach)\b/i.test(problem),
      
      // Question types
      isHowTo: /\bhow\s+(to|do|can|should)\b/i.test(problem),
      isWhy: /\bwhy\b/i.test(problem),
      isWhat: /\bwhat\b/i.test(problem),
      isAnalytical: /\b(analyze|examine|evaluate|assess|review)\b/i.test(problem),
      
      // Complexity indicators
      hasMultipleQuestions: (problem.match(/\?/g) || []).length > 1,
      hasConjunctions: /\b(and|but|however|moreover|furthermore|additionally)\b/gi.test(problem),
      hasTechnicalTerms: /\b(algorithm|implementation|architecture|framework|methodology|optimization)\b/i.test(problem),
      
      // Domain indicators
      isMathematical: /\b(calculate|solve|equation|formula|proof|theorem)\b/i.test(problem),
      isCreative: /\b(create|design|invent|brainstorm|generate|imagine)\b/i.test(problem),
      isProgramming: /\b(code|program|function|class|variable|loop|array)\b/i.test(problem),
      isResearch: /\b(research|study|investigate|explore|discover)\b/i.test(problem)
    }

    // Calculate structural complexity
    analysis.structuralComplexity = this.calculateStructuralComplexity(analysis)
    
    // Determine primary type
    analysis.primaryType = this.determinePrimaryType(analysis)
    
    return analysis
  }

  // Select appropriate decomposition strategy
  selectDecompositionStrategy(problem, structure, requestedStrategy) {
    if (requestedStrategy !== 'auto') {
      return requestedStrategy
    }

    // Strategy selection based on problem characteristics
    if (structure.hasEnumeration) return 'enumeration'
    if (structure.hasSequence) return 'sequential'
    if (structure.hasComparisons) return 'comparative'
    if (structure.isAnalytical) return 'analytical'
    if (structure.isMathematical) return 'mathematical'
    if (structure.isCreative) return 'creative'
    if (structure.isProgramming) return 'programming'
    if (structure.hasConditionals) return 'conditional'
    if (structure.hasMultipleQuestions) return 'multi_question'
    
    // Default strategies based on complexity
    if (structure.structuralComplexity > 70) return 'hierarchical'
    if (structure.structuralComplexity > 40) return 'modular'
    
    return 'linear'
  }

  // Apply decomposition strategy
  async applyDecomposition(problem, strategy, config) {
    console.log(`🔧 Applying ${strategy} decomposition strategy...`)

    const decomposers = {
      enumeration: () => this.enumerationDecomposition(problem),
      sequential: () => this.sequentialDecomposition(problem),
      comparative: () => this.comparativeDecomposition(problem),
      analytical: () => this.analyticalDecomposition(problem),
      mathematical: () => this.mathematicalDecomposition(problem),
      creative: () => this.creativeDecomposition(problem),
      programming: () => this.programmingDecomposition(problem),
      conditional: () => this.conditionalDecomposition(problem),
      multi_question: () => this.multiQuestionDecomposition(problem),
      hierarchical: () => this.hierarchicalDecomposition(problem),
      modular: () => this.modularDecomposition(problem),
      linear: () => this.linearDecomposition(problem)
    }

    const decomposer = decomposers[strategy] || decomposers.linear
    return await decomposer()
  }

  // Enumeration-based decomposition
  enumerationDecomposition(problem) {
    const subProblems = []
    
    // Extract numbered items
    const numberedItems = problem.match(/\d+\.\s*([^.!?]+)/g)
    if (numberedItems) {
      numberedItems.forEach((item, index) => {
        subProblems.push({
          id: index + 1,
          type: 'enumerated_item',
          description: item.replace(/^\d+\.\s*/, '').trim(),
          priority: 'medium',
          dependencies: index > 0 ? [index] : []
        })
      })
    }

    // Extract bullet points
    const bulletPoints = problem.match(/[-*•]\s*([^.!?\n]+)/g)
    if (bulletPoints) {
      bulletPoints.forEach((item, index) => {
        subProblems.push({
          id: subProblems.length + 1,
          type: 'bullet_item',
          description: item.replace(/^[-*•]\s*/, '').trim(),
          priority: 'medium',
          dependencies: []
        })
      })
    }

    return {
      strategy: 'enumeration',
      subProblems: subProblems,
      complexity: this.estimateComplexity(subProblems)
    }
  }

  // Sequential decomposition
  sequentialDecomposition(problem) {
    const subProblems = []
    
    // Look for sequence indicators
    const sequenceWords = ['first', 'second', 'third', 'next', 'then', 'finally', 'lastly']
    const sentences = problem.split(/[.!?]+/).filter(s => s.trim().length > 0)
    
    sentences.forEach((sentence, index) => {
      const trimmed = sentence.trim()
      if (trimmed.length > this.minProblemSize) {
        const hasSequenceWord = sequenceWords.some(word => 
          new RegExp(`\\b${word}\\b`, 'i').test(trimmed)
        )
        
        subProblems.push({
          id: index + 1,
          type: 'sequential_step',
          description: trimmed,
          priority: hasSequenceWord ? 'high' : 'medium',
          dependencies: index > 0 ? [index] : [],
          sequenceOrder: index + 1
        })
      }
    })

    return {
      strategy: 'sequential',
      subProblems: subProblems,
      complexity: this.estimateComplexity(subProblems),
      requiresOrder: true
    }
  }

  // Comparative decomposition
  comparativeDecomposition(problem) {
    const subProblems = []
    
    // Extract comparison subjects
    const comparisonPatterns = [
      /compare\s+([^.!?]+?)\s+(?:with|to|and)\s+([^.!?]+)/i,
      /([^.!?]+?)\s+(?:vs|versus)\s+([^.!?]+)/i,
      /difference\s+between\s+([^.!?]+?)\s+and\s+([^.!?]+)/i
    ]

    let subjects = []
    for (const pattern of comparisonPatterns) {
      const match = problem.match(pattern)
      if (match) {
        subjects = [match[1].trim(), match[2].trim()]
        break
      }
    }

    if (subjects.length === 2) {
      subProblems.push({
        id: 1,
        type: 'analysis_subject_a',
        description: `Analyze characteristics of ${subjects[0]}`,
        priority: 'high',
        dependencies: []
      })

      subProblems.push({
        id: 2,
        type: 'analysis_subject_b',
        description: `Analyze characteristics of ${subjects[1]}`,
        priority: 'high',
        dependencies: []
      })

      subProblems.push({
        id: 3,
        type: 'comparison',
        description: `Compare ${subjects[0]} and ${subjects[1]}`,
        priority: 'high',
        dependencies: [1, 2]
      })

      subProblems.push({
        id: 4,
        type: 'conclusion',
        description: 'Draw conclusions from the comparison',
        priority: 'medium',
        dependencies: [3]
      })
    }

    return {
      strategy: 'comparative',
      subProblems: subProblems,
      complexity: this.estimateComplexity(subProblems),
      subjects: subjects
    }
  }

  // Analytical decomposition
  analyticalDecomposition(problem) {
    const subProblems = []
    
    // Standard analytical framework
    subProblems.push({
      id: 1,
      type: 'context_analysis',
      description: 'Understand the context and background',
      priority: 'high',
      dependencies: []
    })

    subProblems.push({
      id: 2,
      type: 'component_identification',
      description: 'Identify key components and factors',
      priority: 'high',
      dependencies: [1]
    })

    subProblems.push({
      id: 3,
      type: 'relationship_analysis',
      description: 'Analyze relationships between components',
      priority: 'medium',
      dependencies: [2]
    })

    subProblems.push({
      id: 4,
      type: 'impact_assessment',
      description: 'Assess impacts and implications',
      priority: 'medium',
      dependencies: [3]
    })

    subProblems.push({
      id: 5,
      type: 'synthesis',
      description: 'Synthesize findings into conclusions',
      priority: 'high',
      dependencies: [4]
    })

    return {
      strategy: 'analytical',
      subProblems: subProblems,
      complexity: this.estimateComplexity(subProblems),
      framework: 'standard_analysis'
    }
  }

  // Mathematical decomposition
  mathematicalDecomposition(problem) {
    const subProblems = []
    
    // Mathematical problem-solving framework
    subProblems.push({
      id: 1,
      type: 'problem_understanding',
      description: 'Understand what is being asked mathematically',
      priority: 'high',
      dependencies: []
    })

    subProblems.push({
      id: 2,
      type: 'given_information',
      description: 'Identify given information and constraints',
      priority: 'high',
      dependencies: [1]
    })

    subProblems.push({
      id: 3,
      type: 'method_selection',
      description: 'Choose appropriate mathematical methods',
      priority: 'medium',
      dependencies: [2]
    })

    subProblems.push({
      id: 4,
      type: 'calculation',
      description: 'Perform calculations step by step',
      priority: 'high',
      dependencies: [3]
    })

    subProblems.push({
      id: 5,
      type: 'verification',
      description: 'Verify the solution and check for reasonableness',
      priority: 'medium',
      dependencies: [4]
    })

    return {
      strategy: 'mathematical',
      subProblems: subProblems,
      complexity: this.estimateComplexity(subProblems),
      framework: 'mathematical_problem_solving'
    }
  }

  // Creative decomposition
  creativeDecomposition(problem) {
    const subProblems = []
    
    // Creative process framework
    subProblems.push({
      id: 1,
      type: 'inspiration_gathering',
      description: 'Gather inspiration and understand requirements',
      priority: 'high',
      dependencies: []
    })

    subProblems.push({
      id: 2,
      type: 'ideation',
      description: 'Generate multiple creative ideas',
      priority: 'high',
      dependencies: [1]
    })

    subProblems.push({
      id: 3,
      type: 'concept_development',
      description: 'Develop the most promising concepts',
      priority: 'medium',
      dependencies: [2]
    })

    subProblems.push({
      id: 4,
      type: 'refinement',
      description: 'Refine and polish the creative solution',
      priority: 'medium',
      dependencies: [3]
    })

    subProblems.push({
      id: 5,
      type: 'presentation',
      description: 'Present the creative solution effectively',
      priority: 'low',
      dependencies: [4]
    })

    return {
      strategy: 'creative',
      subProblems: subProblems,
      complexity: this.estimateComplexity(subProblems),
      framework: 'creative_process'
    }
  }

  // Programming decomposition
  programmingDecomposition(problem) {
    const subProblems = []
    
    // Software development framework
    subProblems.push({
      id: 1,
      type: 'requirements_analysis',
      description: 'Understand functional and technical requirements',
      priority: 'high',
      dependencies: []
    })

    subProblems.push({
      id: 2,
      type: 'design_planning',
      description: 'Plan the architecture and design approach',
      priority: 'high',
      dependencies: [1]
    })

    subProblems.push({
      id: 3,
      type: 'implementation',
      description: 'Write the code implementation',
      priority: 'high',
      dependencies: [2]
    })

    subProblems.push({
      id: 4,
      type: 'testing',
      description: 'Test the implementation for correctness',
      priority: 'medium',
      dependencies: [3]
    })

    subProblems.push({
      id: 5,
      type: 'optimization',
      description: 'Optimize for performance and maintainability',
      priority: 'low',
      dependencies: [4]
    })

    return {
      strategy: 'programming',
      subProblems: subProblems,
      complexity: this.estimateComplexity(subProblems),
      framework: 'software_development'
    }
  }

  // Linear decomposition (fallback)
  linearDecomposition(problem) {
    const sentences = problem.split(/[.!?]+/).filter(s => s.trim().length > this.minProblemSize)
    const subProblems = sentences.map((sentence, index) => ({
      id: index + 1,
      type: 'linear_component',
      description: sentence.trim(),
      priority: 'medium',
      dependencies: index > 0 ? [index] : []
    }))

    return {
      strategy: 'linear',
      subProblems: subProblems,
      complexity: this.estimateComplexity(subProblems)
    }
  }

  // Calculate structural complexity
  calculateStructuralComplexity(analysis) {
    let score = 0
    
    // Length factors
    score += Math.min(analysis.length / 20, 20)
    score += Math.min(analysis.sentences * 5, 25)
    
    // Structural factors
    if (analysis.hasEnumeration) score += 10
    if (analysis.hasConditionals) score += 15
    if (analysis.hasComparisons) score += 15
    if (analysis.hasSequence) score += 10
    if (analysis.hasMultipleQuestions) score += 10
    if (analysis.hasConjunctions) score += 5
    if (analysis.hasTechnicalTerms) score += 15
    
    return Math.min(score, 100)
  }

  // Determine primary problem type
  determinePrimaryType(analysis) {
    if (analysis.isMathematical) return 'mathematical'
    if (analysis.isProgramming) return 'programming'
    if (analysis.isCreative) return 'creative'
    if (analysis.isResearch) return 'research'
    if (analysis.isAnalytical) return 'analytical'
    if (analysis.isHowTo) return 'instructional'
    if (analysis.isWhy) return 'explanatory'
    if (analysis.isWhat) return 'definitional'
    return 'general'
  }

  // Estimate complexity of decomposition
  estimateComplexity(subProblems) {
    const count = subProblems.length
    const dependencies = subProblems.reduce((sum, p) => sum + p.dependencies.length, 0)
    
    let level = 'simple'
    if (count > 6 || dependencies > 8) level = 'complex'
    else if (count > 3 || dependencies > 4) level = 'moderate'
    
    return {
      level: level,
      subProblemCount: count,
      totalDependencies: dependencies,
      averageDependencies: count > 0 ? dependencies / count : 0
    }
  }

  // Optimize decomposition
  optimizeDecomposition(decomposition) {
    // Remove duplicate sub-problems
    const unique = this.removeDuplicates(decomposition.subProblems)
    
    // Merge similar sub-problems
    const merged = this.mergeSimilar(unique)
    
    // Optimize dependencies
    const optimizedDeps = this.optimizeDependencies(merged)
    
    return {
      ...decomposition,
      subProblems: optimizedDeps,
      optimizations: {
        duplicatesRemoved: decomposition.subProblems.length - unique.length,
        similarMerged: unique.length - merged.length,
        dependenciesOptimized: true
      }
    }
  }

  // Remove duplicate sub-problems
  removeDuplicates(subProblems) {
    const seen = new Set()
    return subProblems.filter(problem => {
      const key = problem.description.toLowerCase().trim()
      if (seen.has(key)) return false
      seen.add(key)
      return true
    })
  }

  // Merge similar sub-problems
  mergeSimilar(subProblems) {
    // Simple similarity check based on word overlap
    const merged = []
    const used = new Set()
    
    subProblems.forEach((problem, index) => {
      if (used.has(index)) return
      
      const similar = subProblems.slice(index + 1).find((other, otherIndex) => {
        const actualIndex = index + 1 + otherIndex
        if (used.has(actualIndex)) return false
        
        const similarity = this.calculateSimilarity(problem.description, other.description)
        if (similarity > 0.7) {
          used.add(actualIndex)
          return true
        }
        return false
      })
      
      if (similar) {
        merged.push({
          ...problem,
          description: `${problem.description} (including ${similar.description.toLowerCase()})`,
          dependencies: [...new Set([...problem.dependencies, ...similar.dependencies])]
        })
      } else {
        merged.push(problem)
      }
    })
    
    return merged
  }

  // Calculate similarity between two descriptions
  calculateSimilarity(desc1, desc2) {
    const words1 = new Set(desc1.toLowerCase().split(/\s+/))
    const words2 = new Set(desc2.toLowerCase().split(/\s+/))
    
    const intersection = new Set([...words1].filter(x => words2.has(x)))
    const union = new Set([...words1, ...words2])
    
    return intersection.size / union.size
  }

  // Optimize dependencies
  optimizeDependencies(subProblems) {
    return subProblems.map(problem => ({
      ...problem,
      dependencies: problem.dependencies.filter(dep => 
        dep <= subProblems.length && dep !== problem.id
      )
    }))
  }

  // Create execution plan
  createExecutionPlan(decomposition) {
    const plan = {
      phases: this.groupIntoPhases(decomposition.subProblems),
      totalSteps: decomposition.subProblems.length,
      estimatedDuration: this.estimateDuration(decomposition.subProblems),
      criticalPath: this.findCriticalPath(decomposition.subProblems)
    }
    
    return plan
  }

  // Group sub-problems into execution phases
  groupIntoPhases(subProblems) {
    const phases = []
    const completed = new Set()
    
    while (completed.size < subProblems.length) {
      const currentPhase = subProblems.filter(problem => 
        !completed.has(problem.id) && 
        problem.dependencies.every(dep => completed.has(dep))
      )
      
      if (currentPhase.length === 0) break // Prevent infinite loop
      
      phases.push({
        phase: phases.length + 1,
        problems: currentPhase,
        canRunInParallel: currentPhase.length > 1
      })
      
      currentPhase.forEach(problem => completed.add(problem.id))
    }
    
    return phases
  }

  // Estimate duration
  estimateDuration(subProblems) {
    const baseDuration = subProblems.length * 2 // 2 minutes per sub-problem
    const complexityMultiplier = subProblems.filter(p => p.priority === 'high').length * 0.5
    
    return Math.round(baseDuration + complexityMultiplier)
  }

  // Find critical path
  findCriticalPath(subProblems) {
    // Simple critical path: longest dependency chain
    let longestPath = []
    
    subProblems.forEach(problem => {
      const path = this.findPathToRoot(problem, subProblems)
      if (path.length > longestPath.length) {
        longestPath = path
      }
    })
    
    return longestPath
  }

  // Find path to root for a problem
  findPathToRoot(problem, allProblems, visited = new Set()) {
    if (visited.has(problem.id)) return [] // Prevent cycles
    visited.add(problem.id)
    
    if (problem.dependencies.length === 0) {
      return [problem.id]
    }
    
    let longestPath = [problem.id]
    problem.dependencies.forEach(depId => {
      const depProblem = allProblems.find(p => p.id === depId)
      if (depProblem) {
        const path = [problem.id, ...this.findPathToRoot(depProblem, allProblems, new Set(visited))]
        if (path.length > longestPath.length) {
          longestPath = path
        }
      }
    })
    
    return longestPath
  }

  // Calculate decomposition depth
  calculateDepth(decomposition) {
    return Math.max(...decomposition.subProblems.map(p => p.dependencies.length)) + 1
  }

  // Create fallback decomposition
  createFallbackDecomposition(problem) {
    return {
      strategy: 'fallback',
      subProblems: [{
        id: 1,
        type: 'complete_problem',
        description: problem,
        priority: 'high',
        dependencies: []
      }],
      complexity: { level: 'simple', subProblemCount: 1, totalDependencies: 0 }
    }
  }
}

// Create singleton instance
const problemDecomposition = new ProblemDecomposition()
export default problemDecomposition
