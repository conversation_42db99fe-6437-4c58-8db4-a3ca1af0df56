// Multi-Agent Task Decomposition - Enhanced task breakdown for agent collaboration
import geminiService from './geminiService.js'
import reasoningService from './reasoningService.js'
import agentFramework from './agentFramework.js'

class MultiAgentTaskDecomposition {
  constructor() {
    this.name = 'multi_agent_task_decomposition'
    this.version = '1.0.0'
    this.decompositionHistory = []
    this.agentCapabilityMap = new Map()
    this.taskComplexityThresholds = {
      simple: 1,
      moderate: 3,
      complex: 5,
      very_complex: 8
    }
  }

  // Initialize the decomposition service
  async initialize() {
    console.log('🔧 Initializing Multi-Agent Task Decomposition...')

    try {
      // Load agent capabilities
      this.loadAgentCapabilities()

      console.log('✅ Multi-Agent Task Decomposition initialized successfully')
      return true
    } catch (error) {
      console.error('❌ Failed to initialize Multi-Agent Task Decomposition:', error)
      return false
    }
  }

  // Main decomposition method for multi-agent scenarios
  async decomposeForMultiAgent(task, requirements = {}) {
    console.log(`🔍 Decomposing task for multi-agent execution: ${task.substring(0, 50)}...`)

    try {
      // Step 1: Analyze task complexity and requirements
      const taskAnalysis = await this.analyzeTaskForAgents(task, requirements)

      // Step 2: Determine if multi-agent approach is beneficial
      const multiAgentAssessment = this.assessMultiAgentBenefit(taskAnalysis)

      // Step 3: Decompose into agent-specific subtasks
      const decomposition = await this.createAgentDecomposition(taskAnalysis, multiAgentAssessment)

      // Step 4: Optimize task distribution
      const optimizedDistribution = this.optimizeTaskDistribution(decomposition)

      // Step 5: Create execution plan
      const executionPlan = this.createMultiAgentExecutionPlan(optimizedDistribution)

      const result = {
        originalTask: task,
        analysis: taskAnalysis,
        multiAgentBenefit: multiAgentAssessment,
        decomposition: decomposition,
        distribution: optimizedDistribution,
        executionPlan: executionPlan,
        estimatedEfficiency: this.calculateEfficiencyGain(executionPlan),
        timestamp: new Date().toISOString()
      }

      // Store in history
      this.decompositionHistory.push(result)

      return result

    } catch (error) {
      console.error('❌ Task decomposition failed:', error)
      return {
        success: false,
        error: error.message,
        fallbackPlan: this.createSingleAgentFallback(task, requirements)
      }
    }
  }

  // Analyze task specifically for agent requirements
  async analyzeTaskForAgents(task, requirements) {
    console.log('📊 Analyzing task for agent requirements...')

    // Use reasoning service for deep analysis
    const reasoningResult = await reasoningService.analyzeAndReason(
      `Analyze this task for multi-agent decomposition: ${task}`,
      {
        mode: 'task_analysis',
        focus: 'agent_requirements',
        requirements
      }
    )

    const analysis = {
      originalTask: task,
      complexity: this.assessTaskComplexity(task),
      domains: this.identifyTaskDomains(task),
      dependencies: this.identifyTaskDependencies(task),
      parallelizability: this.assessParallelizability(task),
      requiredCapabilities: this.extractRequiredCapabilities(task),
      estimatedEffort: this.estimateTaskEffort(task),
      timeConstraints: requirements.timeConstraints || 'flexible',
      qualityRequirements: requirements.qualityRequirements || 'standard',
      reasoningInsights: reasoningResult.useReasoning ? reasoningResult.reasoningSteps : null
    }

    return analysis
  }

  // Assess if multi-agent approach would be beneficial
  assessMultiAgentBenefit(taskAnalysis) {
    console.log('🤔 Assessing multi-agent benefit...')

    const benefits = {
      recommended: false,
      confidence: 0,
      reasons: [],
      concerns: [],
      alternativeApproach: null
    }

    let score = 0

    // Complexity benefit
    if (taskAnalysis.complexity >= this.taskComplexityThresholds.moderate) {
      score += 20
      benefits.reasons.push('Task complexity benefits from specialization')
    }

    // Domain diversity benefit
    if (taskAnalysis.domains.length > 1) {
      score += 25
      benefits.reasons.push(`Multiple domains identified: ${taskAnalysis.domains.join(', ')}`)
    }

    // Parallelizability benefit
    if (taskAnalysis.parallelizability.score > 0.6) {
      score += 30
      benefits.reasons.push('Task has high parallelization potential')
    }

    // Capability diversity benefit
    if (taskAnalysis.requiredCapabilities.length > 2) {
      score += 20
      benefits.reasons.push('Multiple specialized capabilities required')
    }

    // Effort justification
    if (taskAnalysis.estimatedEffort > 15) { // minutes
      score += 15
      benefits.reasons.push('Task effort justifies coordination overhead')
    } else {
      benefits.concerns.push('Task may be too simple for multi-agent approach')
    }

    // Time constraint considerations
    if (taskAnalysis.timeConstraints === 'urgent') {
      score -= 10
      benefits.concerns.push('Urgent timeline may not allow for coordination')
    }

    // Quality requirement considerations
    if (taskAnalysis.qualityRequirements === 'high') {
      score += 10
      benefits.reasons.push('High quality requirements benefit from specialized agents')
    }

    benefits.confidence = Math.max(0, Math.min(100, score))
    benefits.recommended = benefits.confidence > 60

    if (!benefits.recommended) {
      benefits.alternativeApproach = this.suggestAlternativeApproach(taskAnalysis)
    }

    return benefits
  }

  // Create agent-specific decomposition
  async createAgentDecomposition(taskAnalysis, multiAgentAssessment) {
    console.log('🔨 Creating agent-specific decomposition...')

    if (!multiAgentAssessment.recommended) {
      return this.createSingleAgentDecomposition(taskAnalysis)
    }

    const decomposition = {
      strategy: 'multi_agent_collaborative',
      subtasks: [],
      agentAssignments: new Map(),
      dependencies: [],
      coordinationPoints: []
    }

    // Create domain-specific subtasks
    for (const domain of taskAnalysis.domains) {
      const domainSubtasks = await this.createDomainSubtasks(taskAnalysis.originalTask, domain, taskAnalysis)
      decomposition.subtasks.push(...domainSubtasks)
    }

    // Create integration subtasks if multiple domains
    if (taskAnalysis.domains.length > 1) {
      const integrationTasks = this.createIntegrationTasks(taskAnalysis, decomposition.subtasks)
      decomposition.subtasks.push(...integrationTasks)
    }

    // Assign agents to subtasks
    decomposition.agentAssignments = this.assignAgentsToSubtasks(decomposition.subtasks)

    // Identify dependencies between subtasks
    decomposition.dependencies = this.identifySubtaskDependencies(decomposition.subtasks)

    // Create coordination points
    decomposition.coordinationPoints = this.createCoordinationPoints(decomposition)

    return decomposition
  }

  // Assess task complexity
  assessTaskComplexity(task) {
    let complexity = 1 // Base complexity

    // Length indicators
    if (task.length > 200) complexity += 1
    if (task.length > 500) complexity += 1

    // Complexity keywords
    const complexityKeywords = {
      high: ['comprehensive', 'detailed', 'thorough', 'complete', 'analyze', 'evaluate', 'compare'],
      medium: ['create', 'develop', 'implement', 'design', 'build'],
      indicators: ['multiple', 'various', 'different', 'several', 'many']
    }

    const taskLower = task.toLowerCase()

    complexityKeywords.high.forEach(keyword => {
      if (taskLower.includes(keyword)) complexity += 1.5
    })

    complexityKeywords.medium.forEach(keyword => {
      if (taskLower.includes(keyword)) complexity += 1
    })

    complexityKeywords.indicators.forEach(keyword => {
      if (taskLower.includes(keyword)) complexity += 0.5
    })

    // Conjunction complexity (and, or, but)
    const conjunctions = (task.match(/\b(and|or|but|also|additionally|furthermore)\b/gi) || []).length
    complexity += conjunctions * 0.5

    return Math.round(complexity * 10) / 10
  }

  // Identify task domains
  identifyTaskDomains(task) {
    const domains = []
    const domainKeywords = {
      'research': ['research', 'find', 'search', 'investigate', 'study', 'gather', 'information'],
      'coding': ['code', 'program', 'develop', 'implement', 'debug', 'test', 'software'],
      'analysis': ['analyze', 'examine', 'evaluate', 'assess', 'compare', 'review', 'data'],
      'creative': ['create', 'design', 'write', 'generate', 'brainstorm', 'imagine', 'content'],
      'planning': ['plan', 'organize', 'schedule', 'coordinate', 'manage', 'strategy'],
      'communication': ['explain', 'describe', 'present', 'communicate', 'report', 'document']
    }

    const taskLower = task.toLowerCase()

    Object.entries(domainKeywords).forEach(([domain, keywords]) => {
      const matchCount = keywords.filter(keyword => taskLower.includes(keyword)).length
      if (matchCount > 0) {
        domains.push({
          domain,
          confidence: Math.min(1, matchCount / 3),
          matchedKeywords: keywords.filter(keyword => taskLower.includes(keyword))
        })
      }
    })

    // Sort by confidence and return domain names
    return domains
      .sort((a, b) => b.confidence - a.confidence)
      .map(d => d.domain)
      .slice(0, 4) // Limit to top 4 domains
  }

  // Identify task dependencies
  identifyTaskDependencies(task) {
    const dependencies = []

    // Sequential indicators
    const sequentialPatterns = [
      /first.*then/i,
      /before.*after/i,
      /step.*step/i,
      /\d+\.\s.*\d+\./,
      /initially.*finally/i
    ]

    sequentialPatterns.forEach(pattern => {
      if (pattern.test(task)) {
        dependencies.push({
          type: 'sequential',
          description: 'Task contains sequential steps',
          pattern: pattern.source
        })
      }
    })

    // Data dependencies
    if (/based on|using|from|with data|requires.*result/i.test(task)) {
      dependencies.push({
        type: 'data_dependency',
        description: 'Task depends on data or results from other tasks'
      })
    }

    // Resource dependencies
    if (/needs|requires|depends on|must have/i.test(task)) {
      dependencies.push({
        type: 'resource_dependency',
        description: 'Task has resource or capability dependencies'
      })
    }

    return dependencies
  }

  // Assess parallelizability
  assessParallelizability(task) {
    let score = 0.5 // Base score
    const factors = []

    // Positive indicators for parallelization
    const parallelIndicators = ['multiple', 'various', 'different', 'several', 'each', 'all']
    const parallelCount = parallelIndicators.filter(indicator =>
      task.toLowerCase().includes(indicator)
    ).length

    if (parallelCount > 0) {
      score += parallelCount * 0.1
      factors.push(`Multiple items/aspects mentioned (${parallelCount} indicators)`)
    }

    // Domain diversity increases parallelizability
    const domains = this.identifyTaskDomains(task)
    if (domains.length > 1) {
      score += domains.length * 0.15
      factors.push(`Multiple domains: ${domains.join(', ')}`)
    }

    // Negative indicators for parallelization
    const sequentialIndicators = ['first', 'then', 'next', 'after', 'before', 'step']
    const sequentialCount = sequentialIndicators.filter(indicator =>
      task.toLowerCase().includes(indicator)
    ).length

    if (sequentialCount > 0) {
      score -= sequentialCount * 0.1
      factors.push(`Sequential indicators found (${sequentialCount})`)
    }

    // Dependency indicators
    if (/depends on|based on|requires.*result/i.test(task)) {
      score -= 0.2
      factors.push('Task has dependencies that limit parallelization')
    }

    return {
      score: Math.max(0, Math.min(1, score)),
      factors,
      recommendation: score > 0.6 ? 'highly_parallel' : score > 0.3 ? 'moderately_parallel' : 'mostly_sequential'
    }
  }

  // Extract required capabilities
  extractRequiredCapabilities(task) {
    const capabilities = []
    const capabilityMap = {
      'web_search': ['search', 'find online', 'look up', 'google', 'web'],
      'data_analysis': ['analyze', 'examine data', 'statistics', 'trends', 'patterns'],
      'code_generation': ['code', 'program', 'implement', 'develop', 'script'],
      'creative_writing': ['write', 'create content', 'story', 'article', 'blog'],
      'problem_solving': ['solve', 'figure out', 'resolve', 'fix', 'troubleshoot'],
      'research': ['research', 'investigate', 'study', 'gather information'],
      'planning': ['plan', 'organize', 'schedule', 'strategy', 'roadmap'],
      'communication': ['explain', 'describe', 'present', 'communicate'],
      'reasoning': ['analyze', 'evaluate', 'assess', 'think through', 'logic']
    }

    const taskLower = task.toLowerCase()

    Object.entries(capabilityMap).forEach(([capability, keywords]) => {
      const matches = keywords.filter(keyword => taskLower.includes(keyword))
      if (matches.length > 0) {
        capabilities.push({
          capability,
          confidence: matches.length / keywords.length,
          matchedKeywords: matches
        })
      }
    })

    return capabilities
      .sort((a, b) => b.confidence - a.confidence)
      .map(c => c.capability)
  }

  // Estimate task effort in minutes
  estimateTaskEffort(task) {
    let baseEffort = 5 // Base 5 minutes

    // Adjust based on length
    baseEffort += Math.floor(task.length / 100) * 2

    // Adjust based on complexity keywords
    const effortKeywords = {
      high: ['comprehensive', 'detailed', 'thorough', 'complete', 'extensive'],
      medium: ['analyze', 'create', 'develop', 'implement', 'design'],
      low: ['simple', 'basic', 'quick', 'brief', 'short']
    }

    const taskLower = task.toLowerCase()

    effortKeywords.high.forEach(keyword => {
      if (taskLower.includes(keyword)) baseEffort += 10
    })

    effortKeywords.medium.forEach(keyword => {
      if (taskLower.includes(keyword)) baseEffort += 5
    })

    effortKeywords.low.forEach(keyword => {
      if (taskLower.includes(keyword)) baseEffort -= 2
    })

    return Math.max(2, baseEffort) // Minimum 2 minutes
  }

  // Load agent capabilities from framework
  loadAgentCapabilities() {
    const agentTypes = agentFramework.agents

    agentTypes.forEach((agentType, typeName) => {
      this.agentCapabilityMap.set(typeName, {
        capabilities: agentType.capabilities || [],
        expertise: agentType.expertise || [],
        tools: agentType.tools || [],
        maxConcurrentTasks: agentType.maxConcurrentTasks || 1,
        averageTaskTime: 10 // Default 10 minutes
      })
    })

    console.log(`📋 Loaded capabilities for ${this.agentCapabilityMap.size} agent types`)
  }

  // Suggest alternative approach when multi-agent isn't recommended
  suggestAlternativeApproach(taskAnalysis) {
    if (taskAnalysis.complexity < this.taskComplexityThresholds.moderate) {
      return {
        approach: 'single_agent',
        reason: 'Task complexity is low, single agent sufficient',
        recommendedAgent: this.selectBestSingleAgent(taskAnalysis)
      }
    }

    if (taskAnalysis.domains.length === 1) {
      return {
        approach: 'specialized_single_agent',
        reason: 'Single domain task, use specialized agent',
        recommendedAgent: taskAnalysis.domains[0]
      }
    }

    return {
      approach: 'enhanced_single_agent',
      reason: 'Use single agent with tool integration',
      recommendedAgent: 'coordinator',
      tools: taskAnalysis.requiredCapabilities
    }
  }

  // Select best single agent for task
  selectBestSingleAgent(taskAnalysis) {
    if (taskAnalysis.domains.length > 0) {
      return taskAnalysis.domains[0]
    }

    if (taskAnalysis.requiredCapabilities.length > 0) {
      const capability = taskAnalysis.requiredCapabilities[0].capability || taskAnalysis.requiredCapabilities[0]

      // Map capabilities to agent types
      const capabilityToAgent = {
        'web_search': 'research',
        'data_analysis': 'analysis',
        'code_generation': 'coding',
        'creative_writing': 'creative',
        'problem_solving': 'analysis',
        'research': 'research',
        'planning': 'coordinator'
      }

      return capabilityToAgent[capability] || 'coordinator'
    }

    return 'coordinator' // Default fallback
  }

  // Create single agent decomposition
  createSingleAgentDecomposition(taskAnalysis) {
    return {
      strategy: 'single_agent',
      subtasks: [{
        id: 'main_task',
        description: taskAnalysis.originalTask,
        type: 'complete_task',
        estimatedTime: taskAnalysis.estimatedEffort,
        priority: 'high',
        dependencies: []
      }],
      agentAssignments: new Map([
        ['main_task', this.selectBestSingleAgent(taskAnalysis)]
      ]),
      dependencies: [],
      coordinationPoints: []
    }
  }

  // Create domain-specific subtasks
  async createDomainSubtasks(originalTask, domain, taskAnalysis) {
    console.log(`🎯 Creating ${domain} subtasks...`)

    const prompt = `Break down this task into ${domain}-specific subtasks:

Task: ${originalTask}
Domain: ${domain}
Complexity: ${taskAnalysis.complexity}

Create 2-4 specific subtasks that a ${domain} agent should handle. Each subtask should be:
1. Specific and actionable
2. Focused on ${domain} capabilities
3. Contribute to the overall task goal
4. Be completable in 5-15 minutes

Format as numbered list with brief descriptions.`

    try {
      const response = await geminiService.generateResponse(prompt, 'task_planning')
      const subtasks = this.parseSubtasksFromResponse(response.content, domain)

      return subtasks.map((subtask, index) => ({
        id: `${domain}_${index + 1}`,
        description: subtask,
        domain: domain,
        type: 'domain_specific',
        estimatedTime: Math.ceil(taskAnalysis.estimatedEffort / taskAnalysis.domains.length),
        priority: index === 0 ? 'high' : 'medium',
        dependencies: []
      }))

    } catch (error) {
      console.error(`❌ Failed to create ${domain} subtasks:`, error)

      // Fallback: create generic subtask
      return [{
        id: `${domain}_1`,
        description: `Handle ${domain} aspects of: ${originalTask}`,
        domain: domain,
        type: 'domain_specific',
        estimatedTime: Math.ceil(taskAnalysis.estimatedEffort / taskAnalysis.domains.length),
        priority: 'high',
        dependencies: []
      }]
    }
  }

  // Parse subtasks from AI response
  parseSubtasksFromResponse(content, domain) {
    const subtasks = []

    // Look for numbered lists
    const numberedMatches = content.match(/(?:^|\n)\d+\.\s*(.+)/gm)
    if (numberedMatches) {
      subtasks.push(...numberedMatches.map(match =>
        match.replace(/(?:^|\n)\d+\.\s*/, '').trim()
      ))
    }

    // Look for bullet points
    const bulletMatches = content.match(/(?:^|\n)[-*]\s*(.+)/gm)
    if (bulletMatches && subtasks.length === 0) {
      subtasks.push(...bulletMatches.map(match =>
        match.replace(/(?:^|\n)[-*]\s*/, '').trim()
      ))
    }

    // Fallback: split by sentences
    if (subtasks.length === 0) {
      const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 10)
      subtasks.push(...sentences.slice(0, 3))
    }

    return subtasks.slice(0, 4) // Limit to 4 subtasks per domain
  }

  // Create integration tasks for multi-domain scenarios
  createIntegrationTasks(taskAnalysis, existingSubtasks) {
    const integrationTasks = []

    // Create synthesis task
    integrationTasks.push({
      id: 'synthesis',
      description: `Synthesize results from ${taskAnalysis.domains.join(', ')} domains`,
      domain: 'coordination',
      type: 'integration',
      estimatedTime: Math.ceil(taskAnalysis.estimatedEffort * 0.2),
      priority: 'high',
      dependencies: existingSubtasks.map(st => st.id)
    })

    // Create quality assurance task if high quality required
    if (taskAnalysis.qualityRequirements === 'high') {
      integrationTasks.push({
        id: 'quality_assurance',
        description: 'Review and validate integrated results for quality',
        domain: 'coordination',
        type: 'quality_check',
        estimatedTime: Math.ceil(taskAnalysis.estimatedEffort * 0.1),
        priority: 'medium',
        dependencies: ['synthesis']
      })
    }

    return integrationTasks
  }

  // Assign agents to subtasks
  assignAgentsToSubtasks(subtasks) {
    const assignments = new Map()

    subtasks.forEach(subtask => {
      let agentType = 'coordinator' // Default

      // Assign based on domain
      if (subtask.domain && subtask.domain !== 'coordination') {
        agentType = subtask.domain
      }

      // Special handling for integration tasks
      if (subtask.type === 'integration' || subtask.type === 'quality_check') {
        agentType = 'coordinator'
      }

      assignments.set(subtask.id, agentType)
    })

    return assignments
  }

  // Identify dependencies between subtasks
  identifySubtaskDependencies(subtasks) {
    const dependencies = []

    subtasks.forEach(subtask => {
      // Integration tasks depend on domain tasks
      if (subtask.type === 'integration') {
        const domainTasks = subtasks.filter(st => st.type === 'domain_specific')
        domainTasks.forEach(domainTask => {
          dependencies.push({
            from: domainTask.id,
            to: subtask.id,
            type: 'completion_dependency',
            description: `${subtask.id} requires completion of ${domainTask.id}`
          })
        })
      }

      // Quality check depends on synthesis
      if (subtask.type === 'quality_check') {
        const synthesisTask = subtasks.find(st => st.id === 'synthesis')
        if (synthesisTask) {
          dependencies.push({
            from: synthesisTask.id,
            to: subtask.id,
            type: 'completion_dependency',
            description: 'Quality check requires synthesis completion'
          })
        }
      }

      // Explicit dependencies from subtask
      if (subtask.dependencies && subtask.dependencies.length > 0) {
        subtask.dependencies.forEach(depId => {
          dependencies.push({
            from: depId,
            to: subtask.id,
            type: 'explicit_dependency',
            description: `${subtask.id} explicitly depends on ${depId}`
          })
        })
      }
    })

    return dependencies
  }

  // Create coordination points
  createCoordinationPoints(decomposition) {
    const coordinationPoints = []

    // Start coordination
    coordinationPoints.push({
      id: 'start_coordination',
      type: 'initialization',
      description: 'Initialize multi-agent collaboration',
      timing: 'before_execution',
      participants: Array.from(decomposition.agentAssignments.values()),
      actions: ['Brief agents on tasks', 'Establish communication', 'Confirm readiness']
    })

    // Mid-point check if complex
    const domainTasks = decomposition.subtasks.filter(st => st.type === 'domain_specific')
    if (domainTasks.length > 2) {
      coordinationPoints.push({
        id: 'midpoint_check',
        type: 'progress_review',
        description: 'Review progress and coordinate next steps',
        timing: 'after_domain_tasks',
        participants: ['coordinator'],
        actions: ['Review progress', 'Identify issues', 'Adjust plan if needed']
      })
    }

    // Integration coordination
    const integrationTasks = decomposition.subtasks.filter(st => st.type === 'integration')
    if (integrationTasks.length > 0) {
      coordinationPoints.push({
        id: 'integration_coordination',
        type: 'result_integration',
        description: 'Coordinate result integration and synthesis',
        timing: 'before_integration',
        participants: ['coordinator'],
        actions: ['Collect results', 'Prepare for synthesis', 'Resolve conflicts']
      })
    }

    // Final coordination
    coordinationPoints.push({
      id: 'final_coordination',
      type: 'completion',
      description: 'Finalize results and complete collaboration',
      timing: 'after_execution',
      participants: Array.from(decomposition.agentAssignments.values()),
      actions: ['Validate completion', 'Gather feedback', 'Document results']
    })

    return coordinationPoints
  }

  // Optimize task distribution
  optimizeTaskDistribution(decomposition) {
    console.log('⚡ Optimizing task distribution...')

    const optimization = {
      original: decomposition,
      optimizations: [],
      estimatedImprovement: 0
    }

    // Load balancing optimization
    const loadBalance = this.optimizeLoadBalance(decomposition)
    if (loadBalance.improvement > 0) {
      optimization.optimizations.push(loadBalance)
      optimization.estimatedImprovement += loadBalance.improvement
    }

    // Dependency optimization
    const depOptimization = this.optimizeDependencies(decomposition)
    if (depOptimization.improvement > 0) {
      optimization.optimizations.push(depOptimization)
      optimization.estimatedImprovement += depOptimization.improvement
    }

    // Parallelization optimization
    const parallelOptimization = this.optimizeParallelization(decomposition)
    if (parallelOptimization.improvement > 0) {
      optimization.optimizations.push(parallelOptimization)
      optimization.estimatedImprovement += parallelOptimization.improvement
    }

    return {
      ...decomposition,
      optimizations: optimization.optimizations,
      estimatedImprovement: optimization.estimatedImprovement
    }
  }

  // Optimize load balance
  optimizeLoadBalance(decomposition) {
    const agentLoads = new Map()

    // Calculate current load per agent
    decomposition.agentAssignments.forEach((agentType, taskId) => {
      const task = decomposition.subtasks.find(st => st.id === taskId)
      const currentLoad = agentLoads.get(agentType) || 0
      agentLoads.set(agentType, currentLoad + (task?.estimatedTime || 5))
    })

    const loads = Array.from(agentLoads.values())
    const maxLoad = Math.max(...loads)
    const minLoad = Math.min(...loads)
    const imbalance = maxLoad - minLoad

    return {
      type: 'load_balancing',
      current_imbalance: imbalance,
      improvement: imbalance > 10 ? 15 : 0, // 15% improvement if significant imbalance
      description: imbalance > 10 ? 'Significant load imbalance detected' : 'Load is well balanced',
      recommendations: imbalance > 10 ? ['Redistribute tasks', 'Consider additional agents'] : []
    }
  }

  // Optimize dependencies
  optimizeDependencies(decomposition) {
    const criticalPath = this.calculateCriticalPath(decomposition)

    return {
      type: 'dependency_optimization',
      critical_path_length: criticalPath.length,
      improvement: criticalPath.length > 5 ? 10 : 0, // 10% improvement if long critical path
      description: `Critical path has ${criticalPath.length} steps`,
      recommendations: criticalPath.length > 5 ? ['Reduce dependencies', 'Increase parallelization'] : []
    }
  }

  // Optimize parallelization
  optimizeParallelization(decomposition) {
    const parallelTasks = decomposition.subtasks.filter(st =>
      !decomposition.dependencies.some(dep => dep.to === st.id)
    )

    const parallelizationRatio = parallelTasks.length / decomposition.subtasks.length

    return {
      type: 'parallelization_optimization',
      current_ratio: parallelizationRatio,
      improvement: parallelizationRatio < 0.5 ? 20 : 0, // 20% improvement if low parallelization
      description: `${Math.round(parallelizationRatio * 100)}% of tasks can run in parallel`,
      recommendations: parallelizationRatio < 0.5 ? ['Reduce sequential dependencies', 'Break down large tasks'] : []
    }
  }

  // Calculate critical path
  calculateCriticalPath(decomposition) {
    // Simplified critical path calculation
    const path = []
    const visited = new Set()

    // Find tasks with no dependencies (start points)
    const startTasks = decomposition.subtasks.filter(st =>
      !decomposition.dependencies.some(dep => dep.to === st.id)
    )

    // Follow longest path
    let currentTasks = startTasks
    while (currentTasks.length > 0) {
      const longestTask = currentTasks.reduce((longest, current) =>
        (current.estimatedTime || 0) > (longest.estimatedTime || 0) ? current : longest
      )

      if (!visited.has(longestTask.id)) {
        path.push(longestTask.id)
        visited.add(longestTask.id)

        // Find next tasks that depend on this one
        const nextTasks = decomposition.dependencies
          .filter(dep => dep.from === longestTask.id)
          .map(dep => decomposition.subtasks.find(st => st.id === dep.to))
          .filter(task => task && !visited.has(task.id))

        currentTasks = nextTasks
      } else {
        break
      }
    }

    return path
  }

  // Create multi-agent execution plan
  createMultiAgentExecutionPlan(optimizedDistribution) {
    console.log('📋 Creating multi-agent execution plan...')

    const plan = {
      strategy: optimizedDistribution.strategy,
      phases: this.createExecutionPhases(optimizedDistribution),
      timeline: this.createDetailedTimeline(optimizedDistribution),
      resourceRequirements: this.calculateResourceRequirements(optimizedDistribution),
      riskAssessment: this.assessExecutionRisks(optimizedDistribution),
      successMetrics: this.defineSuccessMetrics(optimizedDistribution),
      contingencyPlans: this.createContingencyPlans(optimizedDistribution)
    }

    return plan
  }

  // Create execution phases
  createExecutionPhases(distribution) {
    const phases = []

    // Phase 1: Initialization
    phases.push({
      name: 'Initialization',
      description: 'Setup and coordination',
      tasks: ['start_coordination'],
      estimatedDuration: 2,
      parallelizable: false
    })

    // Phase 2: Domain Execution
    const domainTasks = distribution.subtasks.filter(st => st.type === 'domain_specific')
    if (domainTasks.length > 0) {
      phases.push({
        name: 'Domain Execution',
        description: 'Parallel execution of domain-specific tasks',
        tasks: domainTasks.map(dt => dt.id),
        estimatedDuration: Math.max(...domainTasks.map(dt => dt.estimatedTime || 5)),
        parallelizable: true
      })
    }

    // Phase 3: Integration
    const integrationTasks = distribution.subtasks.filter(st => st.type === 'integration')
    if (integrationTasks.length > 0) {
      phases.push({
        name: 'Integration',
        description: 'Result synthesis and integration',
        tasks: integrationTasks.map(it => it.id),
        estimatedDuration: integrationTasks.reduce((sum, it) => sum + (it.estimatedTime || 5), 0),
        parallelizable: false
      })
    }

    // Phase 4: Finalization
    phases.push({
      name: 'Finalization',
      description: 'Quality check and completion',
      tasks: ['final_coordination'],
      estimatedDuration: 2,
      parallelizable: false
    })

    return phases
  }

  // Create detailed timeline
  createDetailedTimeline(distribution) {
    const timeline = []
    let currentTime = 0

    const phases = this.createExecutionPhases(distribution)

    phases.forEach(phase => {
      timeline.push({
        phase: phase.name,
        start_time: currentTime,
        end_time: currentTime + phase.estimatedDuration,
        duration: phase.estimatedDuration,
        tasks: phase.tasks,
        parallelizable: phase.parallelizable
      })

      currentTime += phase.estimatedDuration
    })

    return {
      total_duration: currentTime,
      phases: timeline,
      critical_path: this.calculateCriticalPath(distribution)
    }
  }

  // Calculate resource requirements
  calculateResourceRequirements(distribution) {
    const agentTypes = new Set(distribution.agentAssignments.values())
    const maxConcurrentTasks = Math.max(
      ...Array.from(agentTypes).map(type =>
        this.agentCapabilityMap.get(type)?.maxConcurrentTasks || 1
      )
    )

    return {
      required_agents: Array.from(agentTypes),
      max_concurrent_tasks: maxConcurrentTasks,
      estimated_memory_usage: 'moderate',
      estimated_cpu_usage: agentTypes.size > 3 ? 'high' : 'moderate',
      coordination_overhead: distribution.coordinationPoints.length * 0.5 // minutes
    }
  }

  // Assess execution risks
  assessExecutionRisks(distribution) {
    const risks = []

    // Complexity risk
    if (distribution.subtasks.length > 6) {
      risks.push({
        type: 'complexity',
        level: 'medium',
        description: 'High number of subtasks may increase coordination complexity',
        mitigation: 'Ensure clear communication protocols'
      })
    }

    // Dependency risk
    const criticalPath = this.calculateCriticalPath(distribution)
    if (criticalPath.length > 4) {
      risks.push({
        type: 'dependency',
        level: 'medium',
        description: 'Long critical path may cause delays',
        mitigation: 'Monitor critical path tasks closely'
      })
    }

    // Agent availability risk
    const uniqueAgents = new Set(distribution.agentAssignments.values())
    if (uniqueAgents.size > 4) {
      risks.push({
        type: 'resource',
        level: 'low',
        description: 'Multiple agent types required',
        mitigation: 'Ensure all agent types are available'
      })
    }

    return risks
  }

  // Define success metrics
  defineSuccessMetrics(distribution) {
    return {
      completion_rate: 'All subtasks completed successfully',
      quality_score: 'Average quality score > 80%',
      efficiency_gain: `Achieve ${distribution.estimatedImprovement || 0}% efficiency improvement`,
      coordination_effectiveness: 'Smooth coordination with minimal conflicts',
      time_adherence: 'Complete within estimated timeline ±20%'
    }
  }

  // Create contingency plans
  createContingencyPlans(distribution) {
    return [
      {
        scenario: 'Agent failure during execution',
        trigger: 'Agent becomes unresponsive',
        response: 'Reassign tasks to backup agent or redistribute workload',
        impact: 'medium'
      },
      {
        scenario: 'Task dependency blocking progress',
        trigger: 'Dependent task fails or delays significantly',
        response: 'Activate parallel alternative approach or skip non-critical dependencies',
        impact: 'high'
      },
      {
        scenario: 'Quality issues in intermediate results',
        trigger: 'Quality score below threshold',
        response: 'Request revision or escalate to coordinator for review',
        impact: 'medium'
      },
      {
        scenario: 'Coordination breakdown',
        trigger: 'Communication failures between agents',
        response: 'Reset coordination protocols and re-establish communication',
        impact: 'high'
      }
    ]
  }

  // Calculate efficiency gain
  calculateEfficiencyGain(executionPlan) {
    const sequentialTime = executionPlan.timeline.phases.reduce((sum, phase) => sum + phase.duration, 0)
    const parallelTime = executionPlan.timeline.total_duration

    const timeGain = Math.max(0, (sequentialTime - parallelTime) / sequentialTime)
    const coordinationOverhead = (executionPlan.resourceRequirements.coordination_overhead || 0) / parallelTime

    const netGain = Math.max(0, timeGain - coordinationOverhead)

    return {
      time_savings: Math.round(timeGain * 100),
      coordination_overhead: Math.round(coordinationOverhead * 100),
      net_efficiency_gain: Math.round(netGain * 100),
      recommendation: netGain > 0.1 ? 'Recommended' : netGain > 0 ? 'Marginal benefit' : 'Not recommended'
    }
  }

  // Get decomposition statistics
  getDecompositionStats() {
    return {
      total_decompositions: this.decompositionHistory.length,
      multi_agent_recommendations: this.decompositionHistory.filter(d => d.multiAgentBenefit?.recommended).length,
      average_efficiency_gain: this.decompositionHistory.reduce((sum, d) =>
        sum + (d.estimatedEfficiency?.net_efficiency_gain || 0), 0
      ) / Math.max(1, this.decompositionHistory.length),
      agent_utilization: this.calculateAgentUtilization(),
      recent_decompositions: this.decompositionHistory.slice(-5)
    }
  }

  // Calculate agent utilization
  calculateAgentUtilization() {
    const utilization = {}

    this.decompositionHistory.forEach(decomp => {
      if (decomp.distribution?.agentAssignments) {
        decomp.distribution.agentAssignments.forEach((agentType, taskId) => {
          utilization[agentType] = (utilization[agentType] || 0) + 1
        })
      }
    })

    return utilization
  }
}

// Create singleton instance
const multiAgentTaskDecomposition = new MultiAgentTaskDecomposition()
export default multiAgentTaskDecomposition