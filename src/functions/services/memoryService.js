// Memory Service for Persistent Conversation Storage and User Preferences
class MemoryService {
  constructor() {
    this.storagePrefix = 'agi_playground_'
    this.conversationKey = `${this.storagePrefix}conversations`
    this.preferencesKey = `${this.storagePrefix}preferences`
    this.profileKey = `${this.storagePrefix}profile`
    this.sessionKey = `${this.storagePrefix}current_session`
    
    // Initialize default preferences
    this.defaultPreferences = {
      responseStyle: 'balanced', // concise, balanced, detailed
      preferredLanguage: 'en',
      codeLanguagePreference: 'javascript',
      analysisDepth: 'medium', // shallow, medium, deep
      imageDescriptionStyle: 'detailed', // simple, detailed, artistic
      conversationTone: 'professional', // casual, professional, technical
      rememberContext: true,
      maxHistoryLength: 50,
      autoSave: true
    }

    this.currentSession = this.generateSessionId()
    this.initializeStorage()
  }

  // Generate unique session ID
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Initialize storage with default values if not exists
  initializeStorage() {
    try {
      // Initialize preferences if not exists
      if (!localStorage.getItem(this.preferencesKey)) {
        this.savePreferences(this.defaultPreferences)
      }

      // Initialize conversations array if not exists
      if (!localStorage.getItem(this.conversationKey)) {
        localStorage.setItem(this.conversationKey, JSON.stringify([]))
      }

      // Set current session
      localStorage.setItem(this.sessionKey, this.currentSession)

      console.log('✅ Memory service initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize memory service:', error)
    }
  }

  // Save conversation to persistent storage
  saveConversation(conversationHistory, mode = 'chat') {
    try {
      if (!conversationHistory || conversationHistory.length === 0) return

      const conversations = this.getConversations()
      const conversationData = {
        id: this.generateConversationId(),
        sessionId: this.currentSession,
        mode: mode,
        messages: conversationHistory,
        timestamp: new Date().toISOString(),
        messageCount: conversationHistory.length,
        lastMessage: conversationHistory[conversationHistory.length - 1]?.content?.substring(0, 100) || ''
      }

      conversations.push(conversationData)

      // Keep only recent conversations (based on preferences)
      const maxHistory = this.getPreferences().maxHistoryLength
      if (conversations.length > maxHistory) {
        conversations.splice(0, conversations.length - maxHistory)
      }

      // Try to save with storage quota handling
      this.saveToStorageWithQuotaHandling(this.conversationKey, conversations)
      console.log('💾 Conversation saved to memory')

      return conversationData.id
    } catch (error) {
      console.error('❌ Failed to save conversation:', error)
      return null
    }
  }

  // Save to localStorage with quota handling
  saveToStorageWithQuotaHandling(key, data) {
    try {
      const jsonString = JSON.stringify(data)
      localStorage.setItem(key, jsonString)
    } catch (error) {
      if (error.name === 'QuotaExceededError') {
        console.warn('⚠️ Storage quota exceeded, cleaning up old data...')

        // If it's conversations, remove older ones more aggressively
        if (key === this.conversationKey && Array.isArray(data)) {
          const reducedData = data.slice(-Math.floor(data.length / 2)) // Keep only half
          try {
            localStorage.setItem(key, JSON.stringify(reducedData))
            console.log(`📦 Reduced storage: kept ${reducedData.length} of ${data.length} conversations`)
            return
          } catch (retryError) {
            // If still failing, keep even fewer
            const minimalData = data.slice(-5) // Keep only last 5
            localStorage.setItem(key, JSON.stringify(minimalData))
            console.log(`📦 Minimal storage: kept only last 5 conversations`)
            return
          }
        }

        // For other data, try to clear some space
        this.clearOldestConversations(10)
        localStorage.setItem(key, JSON.stringify(data))
      } else {
        throw error
      }
    }
  }

  // Clear oldest conversations to free up space
  clearOldestConversations(count = 5) {
    try {
      const conversations = this.getConversations()
      if (conversations.length > count) {
        const remaining = conversations.slice(count)
        localStorage.setItem(this.conversationKey, JSON.stringify(remaining))
        console.log(`🗑️ Cleared ${count} oldest conversations to free up space`)
      }
    } catch (error) {
      console.error('❌ Failed to clear old conversations:', error)
    }
  }

  // Get all conversations
  getConversations() {
    try {
      const conversations = localStorage.getItem(this.conversationKey)
      return conversations ? JSON.parse(conversations) : []
    } catch (error) {
      console.error('❌ Failed to get conversations:', error)
      return []
    }
  }

  // Get conversations by mode
  getConversationsByMode(mode) {
    return this.getConversations().filter(conv => conv.mode === mode)
  }

  // Get recent conversations (last N conversations)
  getRecentConversations(limit = 5) {
    const conversations = this.getConversations()
    return conversations.slice(-limit).reverse() // Most recent first
  }

  // Search conversations by content
  searchConversations(query, limit = 10) {
    const conversations = this.getConversations()
    const searchTerm = query.toLowerCase()
    
    return conversations
      .filter(conv => {
        // Search in last message or any message content
        const lastMessage = conv.lastMessage.toLowerCase()
        const hasMatch = lastMessage.includes(searchTerm) ||
          conv.messages.some(msg => msg.content.toLowerCase().includes(searchTerm))
        return hasMatch
      })
      .slice(-limit)
      .reverse()
  }

  // Generate conversation ID
  generateConversationId() {
    return `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Save user preferences
  savePreferences(preferences) {
    try {
      const currentPrefs = this.getPreferences()
      const updatedPrefs = { ...currentPrefs, ...preferences }
      this.saveToStorageWithQuotaHandling(this.preferencesKey, updatedPrefs)
      console.log('⚙️ Preferences saved')
      return updatedPrefs
    } catch (error) {
      console.error('❌ Failed to save preferences:', error)
      return this.defaultPreferences
    }
  }

  // Get user preferences
  getPreferences() {
    try {
      const preferences = localStorage.getItem(this.preferencesKey)
      return preferences ? { ...this.defaultPreferences, ...JSON.parse(preferences) } : this.defaultPreferences
    } catch (error) {
      console.error('❌ Failed to get preferences:', error)
      return this.defaultPreferences
    }
  }

  // Learn from user interaction patterns
  learnFromInteraction(interaction) {
    try {
      const preferences = this.getPreferences()
      let updated = false

      // Learn response length preference
      if (interaction.userFeedback === 'too_long') {
        if (preferences.responseStyle !== 'concise') {
          preferences.responseStyle = 'concise'
          updated = true
        }
      } else if (interaction.userFeedback === 'too_short') {
        if (preferences.responseStyle !== 'detailed') {
          preferences.responseStyle = 'detailed'
          updated = true
        }
      }

      // Learn code language preference
      if (interaction.mode === 'code' && interaction.detectedLanguage) {
        preferences.codeLanguagePreference = interaction.detectedLanguage
        updated = true
      }

      // Learn conversation tone from user messages
      if (interaction.userMessage) {
        const message = interaction.userMessage.toLowerCase()
        if (message.includes('please') || message.includes('thank you')) {
          preferences.conversationTone = 'professional'
          updated = true
        } else if (message.includes('hey') || message.includes('cool')) {
          preferences.conversationTone = 'casual'
          updated = true
        }
      }

      if (updated) {
        this.savePreferences(preferences)
        console.log('🧠 Learned from user interaction')
      }

      return preferences
    } catch (error) {
      console.error('❌ Failed to learn from interaction:', error)
      return this.getPreferences()
    }
  }

  // Get context for current conversation
  getConversationContext(currentHistory = [], mode = 'chat') {
    const preferences = this.getPreferences()
    const recentConversations = this.getRecentConversations(3)
    
    return {
      preferences,
      recentConversations,
      currentSession: this.currentSession,
      mode,
      historyLength: currentHistory.length,
      lastInteractionTime: recentConversations[0]?.timestamp || null
    }
  }

  // Clear all memory (for privacy/reset)
  clearMemory() {
    try {
      localStorage.removeItem(this.conversationKey)
      localStorage.removeItem(this.preferencesKey)
      localStorage.removeItem(this.profileKey)
      this.initializeStorage()
      console.log('🗑️ Memory cleared successfully')
      return true
    } catch (error) {
      console.error('❌ Failed to clear memory:', error)
      return false
    }
  }

  // Get memory statistics
  getMemoryStats() {
    const conversations = this.getConversations()
    const preferences = this.getPreferences()
    
    return {
      totalConversations: conversations.length,
      totalMessages: conversations.reduce((sum, conv) => sum + conv.messageCount, 0),
      oldestConversation: conversations[0]?.timestamp || null,
      newestConversation: conversations[conversations.length - 1]?.timestamp || null,
      preferenceCount: Object.keys(preferences).length,
      storageUsed: this.getStorageUsage(),
      currentSession: this.currentSession
    }
  }

  // Get storage usage in KB
  getStorageUsage() {
    try {
      let total = 0
      for (let key in localStorage) {
        if (key.startsWith(this.storagePrefix)) {
          total += localStorage[key].length
        }
      }
      return Math.round(total / 1024 * 100) / 100 // KB with 2 decimal places
    } catch (error) {
      return 0
    }
  }

  // Export memory data
  exportMemory() {
    try {
      return {
        conversations: this.getConversations(),
        preferences: this.getPreferences(),
        stats: this.getMemoryStats(),
        exportDate: new Date().toISOString()
      }
    } catch (error) {
      console.error('❌ Failed to export memory:', error)
      return null
    }
  }

  // Import memory data
  importMemory(memoryData) {
    try {
      if (memoryData.conversations) {
        localStorage.setItem(this.conversationKey, JSON.stringify(memoryData.conversations))
      }
      if (memoryData.preferences) {
        this.savePreferences(memoryData.preferences)
      }
      console.log('📥 Memory imported successfully')
      return true
    } catch (error) {
      console.error('❌ Failed to import memory:', error)
      return false
    }
  }
}

// Create singleton instance
const memoryService = new MemoryService()
export default memoryService
