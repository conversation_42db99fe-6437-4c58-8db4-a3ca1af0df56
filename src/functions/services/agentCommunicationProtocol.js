// Agent Communication Protocol - Standardized communication system for multi-agent collaboration
import agentFramework from './agentFramework.js'

class AgentCommunicationProtocol {
  constructor() {
    this.name = 'agent_communication_protocol'
    this.version = '1.0.0'
    this.messageQueue = new Map() // agentId -> messages[]
    this.communicationChannels = new Map()
    this.messageHistory = []
    this.protocolRules = new Map()
    this.performanceMetrics = {
      totalMessages: 0,
      successfulDeliveries: 0,
      averageResponseTime: 0,
      protocolViolations: 0
    }
  }

  // Initialize communication protocol
  async initialize() {
    console.log('📡 Initializing Agent Communication Protocol...')
    
    try {
      // Setup communication channels
      this.setupCommunicationChannels()
      
      // Define protocol rules
      this.defineProtocolRules()
      
      // Initialize message routing
      this.initializeMessageRouting()
      
      console.log('✅ Agent Communication Protocol initialized successfully')
      return true
    } catch (error) {
      console.error('❌ Failed to initialize Agent Communication Protocol:', error)
      return false
    }
  }

  // Setup different types of communication channels
  setupCommunicationChannels() {
    console.log('🔧 Setting up communication channels...')
    
    // Direct messaging channel
    this.communicationChannels.set('direct', {
      name: 'Direct Messaging',
      description: 'One-to-one communication between agents',
      priority: 'high',
      reliability: 'guaranteed',
      latency: 'low',
      messageTypes: ['task_assignment', 'result_sharing', 'status_update', 'request', 'response']
    })
    
    // Broadcast channel
    this.communicationChannels.set('broadcast', {
      name: 'Broadcast Channel',
      description: 'One-to-many communication for announcements',
      priority: 'medium',
      reliability: 'best_effort',
      latency: 'low',
      messageTypes: ['announcement', 'status_broadcast', 'coordination_update']
    })
    
    // Coordination channel
    this.communicationChannels.set('coordination', {
      name: 'Coordination Channel',
      description: 'Structured coordination messages between coordinator and agents',
      priority: 'high',
      reliability: 'guaranteed',
      latency: 'medium',
      messageTypes: ['coordination_request', 'workflow_update', 'resource_allocation', 'conflict_resolution']
    })
    
    // Emergency channel
    this.communicationChannels.set('emergency', {
      name: 'Emergency Channel',
      description: 'High-priority emergency communications',
      priority: 'critical',
      reliability: 'guaranteed',
      latency: 'immediate',
      messageTypes: ['error_alert', 'failure_notification', 'urgent_request', 'system_alert']
    })
    
    // Data sharing channel
    this.communicationChannels.set('data_sharing', {
      name: 'Data Sharing Channel',
      description: 'Large data transfers and result sharing',
      priority: 'medium',
      reliability: 'guaranteed',
      latency: 'high',
      messageTypes: ['data_transfer', 'result_package', 'file_sharing', 'bulk_update']
    })
    
    console.log(`📡 Setup ${this.communicationChannels.size} communication channels`)
  }

  // Define protocol rules for different message types
  defineProtocolRules() {
    console.log('📋 Defining protocol rules...')
    
    // Task assignment protocol
    this.protocolRules.set('task_assignment', {
      requiredFields: ['taskId', 'description', 'priority', 'deadline'],
      optionalFields: ['dependencies', 'resources', 'context'],
      responseRequired: true,
      responseTimeout: 30000, // 30 seconds
      retryAttempts: 3,
      acknowledgmentRequired: true
    })
    
    // Result sharing protocol
    this.protocolRules.set('result_sharing', {
      requiredFields: ['taskId', 'result', 'status', 'timestamp'],
      optionalFields: ['metadata', 'quality_score', 'confidence', 'notes'],
      responseRequired: false,
      responseTimeout: 0,
      retryAttempts: 2,
      acknowledgmentRequired: true
    })
    
    // Status update protocol
    this.protocolRules.set('status_update', {
      requiredFields: ['agentId', 'status', 'timestamp'],
      optionalFields: ['progress', 'eta', 'issues', 'resource_usage'],
      responseRequired: false,
      responseTimeout: 0,
      retryAttempts: 1,
      acknowledgmentRequired: false
    })
    
    // Coordination request protocol
    this.protocolRules.set('coordination_request', {
      requiredFields: ['requestId', 'type', 'description', 'urgency'],
      optionalFields: ['context', 'alternatives', 'constraints'],
      responseRequired: true,
      responseTimeout: 60000, // 60 seconds
      retryAttempts: 2,
      acknowledgmentRequired: true
    })
    
    // Error alert protocol
    this.protocolRules.set('error_alert', {
      requiredFields: ['errorId', 'severity', 'description', 'timestamp'],
      optionalFields: ['stackTrace', 'context', 'suggestedActions'],
      responseRequired: true,
      responseTimeout: 15000, // 15 seconds
      retryAttempts: 5,
      acknowledgmentRequired: true
    })
    
    console.log(`📋 Defined ${this.protocolRules.size} protocol rules`)
  }

  // Initialize message routing system
  initializeMessageRouting() {
    console.log('🔀 Initializing message routing...')
    
    // Setup routing tables
    this.routingTable = new Map()
    
    // Setup message queues for each agent
    const agents = agentFramework.getActiveAgents()
    agents.forEach(agent => {
      this.messageQueue.set(agent.id, [])
    })
    
    // Setup periodic queue processing
    this.startQueueProcessor()
    
    console.log('🔀 Message routing initialized')
  }

  // Send message between agents
  async sendMessage(fromAgentId, toAgentId, messageType, content, options = {}) {
    console.log(`📤 Sending ${messageType} message from ${fromAgentId} to ${toAgentId}`)
    
    try {
      // Validate message against protocol rules
      const validationResult = this.validateMessage(messageType, content)
      if (!validationResult.valid) {
        throw new Error(`Protocol violation: ${validationResult.errors.join(', ')}`)
      }
      
      // Create message object
      const message = this.createMessage(fromAgentId, toAgentId, messageType, content, options)
      
      // Determine appropriate channel
      const channel = this.selectChannel(messageType, options.priority)
      
      // Route message
      const deliveryResult = await this.routeMessage(message, channel)
      
      // Update metrics
      this.updateMetrics(message, deliveryResult)
      
      // Store in history
      this.messageHistory.push({
        ...message,
        channel: channel.name,
        deliveryResult,
        timestamp: new Date().toISOString()
      })
      
      return {
        success: true,
        messageId: message.id,
        channel: channel.name,
        deliveryTime: deliveryResult.deliveryTime,
        acknowledgment: deliveryResult.acknowledgment
      }
      
    } catch (error) {
      console.error(`❌ Failed to send message from ${fromAgentId} to ${toAgentId}:`, error)
      
      this.performanceMetrics.protocolViolations++
      
      return {
        success: false,
        error: error.message,
        messageType,
        fromAgent: fromAgentId,
        toAgent: toAgentId
      }
    }
  }

  // Broadcast message to multiple agents
  async broadcastMessage(fromAgentId, messageType, content, targetAgents = 'all', options = {}) {
    console.log(`📢 Broadcasting ${messageType} message from ${fromAgentId}`)
    
    try {
      // Determine target agents
      const targets = this.resolveTargetAgents(targetAgents, fromAgentId)
      
      // Send to each target
      const results = []
      for (const targetId of targets) {
        const result = await this.sendMessage(fromAgentId, targetId, messageType, content, {
          ...options,
          broadcast: true
        })
        results.push({ targetId, result })
      }
      
      const successCount = results.filter(r => r.result.success).length
      
      return {
        success: successCount > 0,
        totalTargets: targets.length,
        successfulDeliveries: successCount,
        failedDeliveries: targets.length - successCount,
        results
      }
      
    } catch (error) {
      console.error(`❌ Failed to broadcast message from ${fromAgentId}:`, error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // Request-response pattern for agent communication
  async requestResponse(fromAgentId, toAgentId, requestType, requestContent, timeout = 30000) {
    console.log(`🔄 Request-response: ${fromAgentId} → ${toAgentId} (${requestType})`)
    
    try {
      // Send request
      const requestResult = await this.sendMessage(fromAgentId, toAgentId, requestType, {
        ...requestContent,
        responseRequired: true,
        requestId: this.generateRequestId()
      })
      
      if (!requestResult.success) {
        throw new Error(`Failed to send request: ${requestResult.error}`)
      }
      
      // Wait for response
      const response = await this.waitForResponse(requestResult.messageId, timeout)
      
      return {
        success: true,
        request: requestResult,
        response,
        roundTripTime: response.receivedAt - requestResult.sentAt
      }
      
    } catch (error) {
      console.error(`❌ Request-response failed: ${fromAgentId} → ${toAgentId}:`, error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // Validate message against protocol rules
  validateMessage(messageType, content) {
    const rules = this.protocolRules.get(messageType)
    if (!rules) {
      return {
        valid: false,
        errors: [`Unknown message type: ${messageType}`]
      }
    }
    
    const errors = []
    
    // Check required fields
    rules.requiredFields.forEach(field => {
      if (!(field in content)) {
        errors.push(`Missing required field: ${field}`)
      }
    })
    
    // Validate field types and values
    if (content.priority && !['low', 'medium', 'high', 'critical'].includes(content.priority)) {
      errors.push('Invalid priority value')
    }
    
    if (content.timestamp && isNaN(new Date(content.timestamp).getTime())) {
      errors.push('Invalid timestamp format')
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }

  // Create standardized message object
  createMessage(fromAgentId, toAgentId, messageType, content, options = {}) {
    const messageId = this.generateMessageId()
    
    return {
      id: messageId,
      from: fromAgentId,
      to: toAgentId,
      type: messageType,
      content: {
        ...content,
        messageId // Include in content for reference
      },
      metadata: {
        timestamp: new Date().toISOString(),
        priority: options.priority || 'medium',
        ttl: options.ttl || 300000, // 5 minutes default TTL
        retryCount: 0,
        maxRetries: options.maxRetries || this.protocolRules.get(messageType)?.retryAttempts || 1,
        requiresAcknowledgment: options.requiresAcknowledgment ?? this.protocolRules.get(messageType)?.acknowledgmentRequired ?? false,
        requiresResponse: options.requiresResponse ?? this.protocolRules.get(messageType)?.responseRequired ?? false,
        correlationId: options.correlationId || null,
        broadcast: options.broadcast || false
      },
      status: 'pending'
    }
  }

  // Select appropriate communication channel
  selectChannel(messageType, priority = 'medium') {
    // Emergency messages always use emergency channel
    if (priority === 'critical' || messageType.includes('error') || messageType.includes('alert')) {
      return this.communicationChannels.get('emergency')
    }
    
    // Coordination messages use coordination channel
    if (messageType.includes('coordination') || messageType.includes('workflow')) {
      return this.communicationChannels.get('coordination')
    }
    
    // Data sharing messages use data channel
    if (messageType.includes('data') || messageType.includes('result') || messageType.includes('file')) {
      return this.communicationChannels.get('data_sharing')
    }
    
    // Broadcast messages use broadcast channel
    if (messageType.includes('broadcast') || messageType.includes('announcement')) {
      return this.communicationChannels.get('broadcast')
    }
    
    // Default to direct messaging
    return this.communicationChannels.get('direct')
  }

  // Route message through appropriate channel
  async routeMessage(message, channel) {
    const startTime = Date.now()
    
    try {
      // Add to recipient's message queue
      const recipientQueue = this.messageQueue.get(message.to)
      if (!recipientQueue) {
        throw new Error(`No message queue found for agent: ${message.to}`)
      }
      
      // Apply channel-specific processing
      const processedMessage = await this.applyChannelProcessing(message, channel)
      
      // Add to queue
      recipientQueue.push(processedMessage)
      
      // Notify recipient if agent is active
      const recipient = agentFramework.getAgent(message.to)
      if (recipient) {
        this.notifyAgent(recipient, processedMessage)
      }
      
      const deliveryTime = Date.now() - startTime
      
      return {
        success: true,
        deliveryTime,
        channel: channel.name,
        queueSize: recipientQueue.length,
        acknowledgment: processedMessage.metadata.requiresAcknowledgment ? 'pending' : 'not_required'
      }
      
    } catch (error) {
      console.error(`❌ Message routing failed:`, error)
      return {
        success: false,
        error: error.message,
        deliveryTime: Date.now() - startTime
      }
    }
  }

  // Apply channel-specific processing
  async applyChannelProcessing(message, channel) {
    const processedMessage = { ...message }
    
    // Apply channel-specific transformations
    switch (channel.name) {
      case 'Emergency Channel':
        processedMessage.metadata.priority = 'critical'
        processedMessage.metadata.ttl = 60000 // 1 minute for emergency
        break
        
      case 'Data Sharing Channel':
        processedMessage.metadata.ttl = 600000 // 10 minutes for data
        // Could add compression here for large data
        break
        
      case 'Broadcast Channel':
        processedMessage.metadata.requiresAcknowledgment = false
        break
        
      case 'Coordination Channel':
        processedMessage.metadata.requiresAcknowledgment = true
        break
    }
    
    // Add channel metadata
    processedMessage.channel = {
      name: channel.name,
      priority: channel.priority,
      reliability: channel.reliability,
      processedAt: new Date().toISOString()
    }
    
    return processedMessage
  }

  // Notify agent of new message
  notifyAgent(agent, message) {
    try {
      // Use agent framework's message delivery
      agent.receiveMessage(message)
      
      console.log(`📬 Notified agent ${agent.id} of new ${message.type} message`)
    } catch (error) {
      console.error(`❌ Failed to notify agent ${agent.id}:`, error)
    }
  }

  // Start queue processor for handling message delivery
  startQueueProcessor() {
    setInterval(() => {
      this.processMessageQueues()
    }, 1000) // Process every second
  }

  // Process message queues
  processMessageQueues() {
    this.messageQueue.forEach((queue, agentId) => {
      if (queue.length > 0) {
        // Process expired messages
        const now = Date.now()
        const validMessages = queue.filter(msg => {
          const messageAge = now - new Date(msg.metadata.timestamp).getTime()
          return messageAge < msg.metadata.ttl
        })
        
        // Update queue with valid messages only
        this.messageQueue.set(agentId, validMessages)
        
        // Log if messages were expired
        const expiredCount = queue.length - validMessages.length
        if (expiredCount > 0) {
          console.log(`⏰ Expired ${expiredCount} messages for agent ${agentId}`)
        }
      }
    })
  }

  // Wait for response to a request
  async waitForResponse(requestMessageId, timeout = 30000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now()
      
      const checkForResponse = () => {
        // Look for response in message history
        const response = this.messageHistory.find(msg => 
          msg.content.correlationId === requestMessageId ||
          msg.content.responseToMessageId === requestMessageId
        )
        
        if (response) {
          resolve({
            ...response,
            receivedAt: Date.now()
          })
          return
        }
        
        // Check timeout
        if (Date.now() - startTime > timeout) {
          reject(new Error(`Response timeout after ${timeout}ms`))
          return
        }
        
        // Continue checking
        setTimeout(checkForResponse, 100)
      }
      
      checkForResponse()
    })
  }

  // Resolve target agents for broadcast
  resolveTargetAgents(targetAgents, excludeAgentId) {
    if (targetAgents === 'all') {
      return agentFramework.getActiveAgents()
        .map(agent => agent.id)
        .filter(id => id !== excludeAgentId)
    }
    
    if (Array.isArray(targetAgents)) {
      return targetAgents.filter(id => id !== excludeAgentId)
    }
    
    if (typeof targetAgents === 'string') {
      // Could be agent type or specific agent ID
      const agentsByType = agentFramework.getAgentsByType(targetAgents)
      if (agentsByType.length > 0) {
        return agentsByType.map(agent => agent.id).filter(id => id !== excludeAgentId)
      }
      
      return [targetAgents].filter(id => id !== excludeAgentId)
    }
    
    return []
  }

  // Update performance metrics
  updateMetrics(message, deliveryResult) {
    this.performanceMetrics.totalMessages++
    
    if (deliveryResult.success) {
      this.performanceMetrics.successfulDeliveries++
      
      // Update average response time
      const totalResponseTime = this.performanceMetrics.averageResponseTime * 
                               (this.performanceMetrics.successfulDeliveries - 1) + 
                               deliveryResult.deliveryTime
      this.performanceMetrics.averageResponseTime = totalResponseTime / this.performanceMetrics.successfulDeliveries
    }
  }

  // Generate unique message ID
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`
  }

  // Generate unique request ID
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`
  }

  // Get messages for specific agent
  getMessagesForAgent(agentId, messageType = null, limit = 10) {
    const queue = this.messageQueue.get(agentId) || []

    let messages = [...queue]

    // Filter by message type if specified
    if (messageType) {
      messages = messages.filter(msg => msg.type === messageType)
    }

    // Sort by timestamp (newest first)
    messages.sort((a, b) => new Date(b.metadata.timestamp) - new Date(a.metadata.timestamp))

    // Apply limit
    return messages.slice(0, limit)
  }

  // Mark message as read/processed
  markMessageAsProcessed(agentId, messageId) {
    const queue = this.messageQueue.get(agentId)
    if (!queue) return false

    const messageIndex = queue.findIndex(msg => msg.id === messageId)
    if (messageIndex === -1) return false

    // Remove from queue
    queue.splice(messageIndex, 1)

    console.log(`✅ Message ${messageId} marked as processed for agent ${agentId}`)
    return true
  }

  // Send acknowledgment for received message
  async sendAcknowledgment(agentId, originalMessage, status = 'received') {
    const ackContent = {
      originalMessageId: originalMessage.id,
      status,
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - new Date(originalMessage.metadata.timestamp).getTime()
    }

    return await this.sendMessage(
      agentId,
      originalMessage.from,
      'acknowledgment',
      ackContent,
      { priority: 'high' }
    )
  }

  // Handle communication errors and retries
  async handleCommunicationError(message, error, attempt = 1) {
    console.log(`⚠️ Communication error for message ${message.id}, attempt ${attempt}: ${error.message}`)

    const maxRetries = message.metadata.maxRetries

    if (attempt < maxRetries) {
      // Exponential backoff
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000)

      console.log(`🔄 Retrying message ${message.id} in ${delay}ms...`)

      setTimeout(async () => {
        try {
          message.metadata.retryCount = attempt
          const channel = this.selectChannel(message.type, message.metadata.priority)
          await this.routeMessage(message, channel)
        } catch (retryError) {
          await this.handleCommunicationError(message, retryError, attempt + 1)
        }
      }, delay)

      return { retrying: true, attempt, nextRetryIn: delay }
    } else {
      // Max retries exceeded
      console.error(`❌ Message ${message.id} failed after ${maxRetries} attempts`)

      // Send failure notification to sender
      if (message.from) {
        await this.sendMessage(
          'system',
          message.from,
          'delivery_failure',
          {
            originalMessageId: message.id,
            recipient: message.to,
            error: error.message,
            attempts: maxRetries
          },
          { priority: 'high' }
        )
      }

      return { failed: true, attempts: maxRetries, error: error.message }
    }
  }

  // Create communication session for extended interactions
  createCommunicationSession(participants, sessionType = 'collaboration') {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`

    const session = {
      id: sessionId,
      type: sessionType,
      participants: new Set(participants),
      createdAt: new Date().toISOString(),
      status: 'active',
      messageCount: 0,
      lastActivity: new Date().toISOString(),
      metadata: {
        priority: 'medium',
        maxInactivityTime: 300000, // 5 minutes
        autoCleanup: true
      }
    }

    // Create dedicated channel for session
    this.communicationChannels.set(sessionId, {
      name: `Session Channel ${sessionId}`,
      description: `Dedicated channel for ${sessionType} session`,
      priority: 'high',
      reliability: 'guaranteed',
      latency: 'low',
      messageTypes: ['session_message', 'session_update', 'session_control'],
      session: session
    })

    console.log(`🎯 Created communication session ${sessionId} with ${participants.length} participants`)

    return session
  }

  // Send message within a communication session
  async sendSessionMessage(sessionId, fromAgentId, messageType, content, options = {}) {
    const channel = this.communicationChannels.get(sessionId)
    if (!channel || !channel.session) {
      throw new Error(`Communication session ${sessionId} not found`)
    }

    const session = channel.session

    // Verify sender is participant
    if (!session.participants.has(fromAgentId)) {
      throw new Error(`Agent ${fromAgentId} is not a participant in session ${sessionId}`)
    }

    // Send to all other participants
    const results = []
    for (const participantId of session.participants) {
      if (participantId !== fromAgentId) {
        const result = await this.sendMessage(
          fromAgentId,
          participantId,
          messageType,
          {
            ...content,
            sessionId,
            sessionMessage: true
          },
          {
            ...options,
            correlationId: sessionId
          }
        )
        results.push({ participantId, result })
      }
    }

    // Update session activity
    session.messageCount++
    session.lastActivity = new Date().toISOString()

    return {
      success: true,
      sessionId,
      messagesSent: results.length,
      results
    }
  }

  // Close communication session
  closeCommunicationSession(sessionId, reason = 'completed') {
    const channel = this.communicationChannels.get(sessionId)
    if (!channel || !channel.session) {
      return false
    }

    const session = channel.session
    session.status = 'closed'
    session.closedAt = new Date().toISOString()
    session.closeReason = reason

    // Notify all participants
    for (const participantId of session.participants) {
      this.sendMessage(
        'system',
        participantId,
        'session_closed',
        {
          sessionId,
          reason,
          duration: new Date(session.closedAt) - new Date(session.createdAt),
          messageCount: session.messageCount
        },
        { priority: 'medium' }
      ).catch(error => {
        console.error(`Failed to notify ${participantId} of session closure:`, error)
      })
    }

    // Remove channel after delay to allow final message delivery
    setTimeout(() => {
      this.communicationChannels.delete(sessionId)
    }, 5000)

    console.log(`🔚 Closed communication session ${sessionId}: ${reason}`)
    return true
  }

  // Monitor communication health
  monitorCommunicationHealth() {
    const health = {
      status: 'healthy',
      metrics: { ...this.performanceMetrics },
      issues: [],
      recommendations: []
    }

    // Check success rate
    const successRate = this.performanceMetrics.totalMessages > 0 ?
      this.performanceMetrics.successfulDeliveries / this.performanceMetrics.totalMessages : 1

    if (successRate < 0.9) {
      health.status = 'degraded'
      health.issues.push(`Low message delivery success rate: ${Math.round(successRate * 100)}%`)
      health.recommendations.push('Check agent availability and network connectivity')
    }

    // Check average response time
    if (this.performanceMetrics.averageResponseTime > 5000) {
      health.status = 'degraded'
      health.issues.push(`High average response time: ${this.performanceMetrics.averageResponseTime}ms`)
      health.recommendations.push('Optimize message processing or reduce message size')
    }

    // Check protocol violations
    const violationRate = this.performanceMetrics.totalMessages > 0 ?
      this.performanceMetrics.protocolViolations / this.performanceMetrics.totalMessages : 0

    if (violationRate > 0.05) {
      health.status = 'degraded'
      health.issues.push(`High protocol violation rate: ${Math.round(violationRate * 100)}%`)
      health.recommendations.push('Review message validation and agent implementations')
    }

    // Check queue sizes
    const queueSizes = Array.from(this.messageQueue.values()).map(queue => queue.length)
    const maxQueueSize = Math.max(...queueSizes, 0)

    if (maxQueueSize > 50) {
      health.status = 'degraded'
      health.issues.push(`Large message queue detected: ${maxQueueSize} messages`)
      health.recommendations.push('Increase message processing rate or add more agents')
    }

    return health
  }

  // Get communication statistics
  getCommunicationStats() {
    const stats = {
      ...this.performanceMetrics,
      channels: {
        total: this.communicationChannels.size,
        active: Array.from(this.communicationChannels.values()).filter(ch => ch.session?.status === 'active').length
      },
      queues: {
        total: this.messageQueue.size,
        totalMessages: Array.from(this.messageQueue.values()).reduce((sum, queue) => sum + queue.length, 0),
        averageQueueSize: this.messageQueue.size > 0 ?
          Array.from(this.messageQueue.values()).reduce((sum, queue) => sum + queue.length, 0) / this.messageQueue.size : 0
      },
      recentActivity: {
        messagesLast5Min: this.messageHistory.filter(msg =>
          Date.now() - new Date(msg.timestamp).getTime() < 300000
        ).length,
        uniqueAgentsLast5Min: new Set(
          this.messageHistory
            .filter(msg => Date.now() - new Date(msg.timestamp).getTime() < 300000)
            .flatMap(msg => [msg.from, msg.to])
        ).size
      }
    }

    return stats
  }

  // Cleanup old messages and sessions
  cleanup() {
    const now = Date.now()
    const maxHistoryAge = 24 * 60 * 60 * 1000 // 24 hours

    // Clean message history
    const originalHistorySize = this.messageHistory.length
    this.messageHistory = this.messageHistory.filter(msg =>
      now - new Date(msg.timestamp).getTime() < maxHistoryAge
    )

    const removedMessages = originalHistorySize - this.messageHistory.length
    if (removedMessages > 0) {
      console.log(`🧹 Cleaned up ${removedMessages} old messages from history`)
    }

    // Clean expired sessions
    const expiredSessions = []
    this.communicationChannels.forEach((channel, channelId) => {
      if (channel.session && channel.session.status === 'active') {
        const inactiveTime = now - new Date(channel.session.lastActivity).getTime()
        if (inactiveTime > channel.session.metadata.maxInactivityTime) {
          expiredSessions.push(channelId)
        }
      }
    })

    expiredSessions.forEach(sessionId => {
      this.closeCommunicationSession(sessionId, 'expired')
    })

    if (expiredSessions.length > 0) {
      console.log(`🧹 Cleaned up ${expiredSessions.length} expired communication sessions`)
    }
  }

  // Emergency broadcast to all agents
  async emergencyBroadcast(message, severity = 'high') {
    console.log(`🚨 Emergency broadcast: ${message}`)

    const emergencyMessage = {
      alert: message,
      severity,
      timestamp: new Date().toISOString(),
      requiresAcknowledgment: true
    }

    return await this.broadcastMessage(
      'system',
      'emergency_alert',
      emergencyMessage,
      'all',
      { priority: 'critical' }
    )
  }
}

// Create singleton instance
const agentCommunicationProtocol = new AgentCommunicationProtocol()
export default agentCommunicationProtocol
