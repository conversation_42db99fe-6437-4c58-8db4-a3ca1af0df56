// Agent Result Synthesis - Advanced system for combining multi-agent outputs
import geminiService from './geminiService.js'
import reasoningService from './reasoningService.js'

class AgentResultSynthesis {
  constructor() {
    this.name = 'agent_result_synthesis'
    this.version = '1.0.0'
    this.synthesisHistory = []
    this.synthesisStrategies = new Map()
    this.qualityMetrics = {
      totalSyntheses: 0,
      averageQualityScore: 0,
      averageCoherenceScore: 0,
      averageCompletenessScore: 0,
      conflictResolutionRate: 0
    }
  }

  // Initialize synthesis system
  async initialize() {
    console.log('🔄 Initializing Agent Result Synthesis...')
    
    try {
      // Register synthesis strategies
      this.registerSynthesisStrategies()
      
      console.log('✅ Agent Result Synthesis initialized successfully')
      return true
    } catch (error) {
      console.error('❌ Failed to initialize Agent Result Synthesis:', error)
      return false
    }
  }

  // Register different synthesis strategies
  registerSynthesisStrategies() {
    // Hierarchical synthesis - coordinator leads integration
    this.synthesisStrategies.set('hierarchical', {
      name: 'Hierarchical Synthesis',
      description: 'Coordinator agent leads the synthesis process',
      bestFor: ['complex_tasks', 'conflicting_results', 'quality_critical'],
      process: 'coordinator_led',
      qualityFocus: 'high'
    })
    
    // Collaborative synthesis - agents work together
    this.synthesisStrategies.set('collaborative', {
      name: 'Collaborative Synthesis',
      description: 'All agents contribute to synthesis process',
      bestFor: ['creative_tasks', 'brainstorming', 'diverse_perspectives'],
      process: 'peer_to_peer',
      qualityFocus: 'medium'
    })
    
    // Weighted synthesis - results weighted by agent expertise
    this.synthesisStrategies.set('weighted', {
      name: 'Weighted Synthesis',
      description: 'Results weighted by agent expertise and confidence',
      bestFor: ['expert_domains', 'technical_analysis', 'data_driven'],
      process: 'expertise_weighted',
      qualityFocus: 'high'
    })
    
    // Sequential synthesis - results combined in logical order
    this.synthesisStrategies.set('sequential', {
      name: 'Sequential Synthesis',
      description: 'Results combined following logical dependencies',
      bestFor: ['step_by_step', 'workflow_tasks', 'dependent_results'],
      process: 'dependency_ordered',
      qualityFocus: 'medium'
    })
    
    // Consensus synthesis - find common ground
    this.synthesisStrategies.set('consensus', {
      name: 'Consensus Synthesis',
      description: 'Focus on areas of agreement between agents',
      bestFor: ['conflicting_opinions', 'validation_tasks', 'fact_checking'],
      process: 'agreement_focused',
      qualityFocus: 'high'
    })
    
    console.log(`📋 Registered ${this.synthesisStrategies.size} synthesis strategies`)
  }

  // Main synthesis method
  async synthesizeResults(agentResults, originalTask, synthesisOptions = {}) {
    console.log(`🔄 Synthesizing results from ${agentResults.length} agents...`)
    
    const synthesisId = this.generateSynthesisId()
    const startTime = Date.now()
    
    try {
      // Step 1: Analyze agent results
      const resultAnalysis = await this.analyzeAgentResults(agentResults, originalTask)
      
      // Step 2: Select optimal synthesis strategy
      const strategy = this.selectSynthesisStrategy(resultAnalysis, synthesisOptions)
      
      // Step 3: Detect and resolve conflicts
      const conflictResolution = await this.detectAndResolveConflicts(agentResults, resultAnalysis)
      
      // Step 4: Execute synthesis
      const synthesisResult = await this.executeSynthesis(
        agentResults, 
        strategy, 
        conflictResolution, 
        originalTask
      )
      
      // Step 5: Validate and enhance synthesis
      const validatedResult = await this.validateAndEnhanceSynthesis(synthesisResult, resultAnalysis)
      
      // Step 6: Calculate quality metrics
      const qualityAssessment = this.assessSynthesisQuality(validatedResult, agentResults)
      
      const completionTime = Date.now() - startTime
      
      const finalResult = {
        synthesisId,
        originalTask,
        agentResults: agentResults.map(r => ({
          agentType: r.agentType,
          success: r.success,
          confidence: r.confidence || 75,
          contentLength: r.content?.length || 0
        })),
        strategy: strategy.name,
        conflictResolution,
        synthesis: validatedResult,
        quality: qualityAssessment,
        metrics: {
          completionTime,
          agentCount: agentResults.length,
          conflictsResolved: conflictResolution.conflicts.length,
          synthesisComplexity: this.calculateSynthesisComplexity(agentResults, strategy)
        },
        timestamp: new Date().toISOString()
      }
      
      // Store in history and update metrics
      this.synthesisHistory.push(finalResult)
      this.updateQualityMetrics(finalResult)
      
      return finalResult
      
    } catch (error) {
      console.error(`❌ Synthesis failed for ${synthesisId}:`, error)
      
      return {
        synthesisId,
        success: false,
        error: error.message,
        fallbackResult: this.createFallbackSynthesis(agentResults, originalTask),
        timestamp: new Date().toISOString()
      }
    }
  }

  // Analyze agent results for synthesis planning
  async analyzeAgentResults(agentResults, originalTask) {
    console.log('📊 Analyzing agent results...')
    
    const analysis = {
      resultCount: agentResults.length,
      successfulResults: agentResults.filter(r => r.success).length,
      agentTypes: [...new Set(agentResults.map(r => r.agentType))],
      averageConfidence: this.calculateAverageConfidence(agentResults),
      contentSimilarity: await this.calculateContentSimilarity(agentResults),
      domainCoverage: this.analyzeDomainCoverage(agentResults),
      qualityDistribution: this.analyzeQualityDistribution(agentResults),
      conflictIndicators: this.identifyConflictIndicators(agentResults),
      synthesisComplexity: 'medium' // Will be calculated based on factors
    }
    
    // Determine synthesis complexity
    analysis.synthesisComplexity = this.determineSynthesisComplexity(analysis)
    
    return analysis
  }

  // Calculate average confidence across results
  calculateAverageConfidence(agentResults) {
    const confidenceValues = agentResults
      .filter(r => r.success && r.confidence)
      .map(r => r.confidence)
    
    return confidenceValues.length > 0 ? 
      confidenceValues.reduce((sum, conf) => sum + conf, 0) / confidenceValues.length : 50
  }

  // Calculate content similarity between results
  async calculateContentSimilarity(agentResults) {
    const successfulResults = agentResults.filter(r => r.success && r.content)
    
    if (successfulResults.length < 2) {
      return { score: 1.0, analysis: 'Single or no successful results' }
    }
    
    // Simple similarity calculation based on common keywords
    const allContents = successfulResults.map(r => r.content.toLowerCase())
    const commonWords = this.findCommonWords(allContents)
    
    const similarityScore = commonWords.length / Math.max(
      ...allContents.map(content => content.split(/\s+/).length)
    )
    
    return {
      score: Math.min(1.0, similarityScore * 2), // Normalize
      commonWords: commonWords.slice(0, 10),
      analysis: similarityScore > 0.3 ? 'High similarity' : 
                similarityScore > 0.1 ? 'Moderate similarity' : 'Low similarity'
    }
  }

  // Find common words across content
  findCommonWords(contents) {
    if (contents.length === 0) return []
    
    const wordSets = contents.map(content => 
      new Set(content.split(/\s+/).filter(word => word.length > 3))
    )
    
    const firstSet = wordSets[0]
    const commonWords = []
    
    for (const word of firstSet) {
      if (wordSets.every(set => set.has(word))) {
        commonWords.push(word)
      }
    }
    
    return commonWords
  }

  // Analyze domain coverage
  analyzeDomainCoverage(agentResults) {
    const domains = {}
    
    agentResults.forEach(result => {
      const domain = result.agentType
      if (!domains[domain]) {
        domains[domain] = {
          count: 0,
          successRate: 0,
          averageConfidence: 0,
          results: []
        }
      }
      
      domains[domain].count++
      domains[domain].results.push(result)
      
      if (result.success) {
        domains[domain].successRate++
      }
      
      if (result.confidence) {
        domains[domain].averageConfidence += result.confidence
      }
    })
    
    // Calculate averages
    Object.values(domains).forEach(domain => {
      domain.successRate = domain.successRate / domain.count
      domain.averageConfidence = domain.averageConfidence / domain.count
    })
    
    return {
      domains: Object.keys(domains),
      domainStats: domains,
      coverage: Object.keys(domains).length,
      balance: this.calculateDomainBalance(domains)
    }
  }

  // Calculate domain balance
  calculateDomainBalance(domains) {
    const counts = Object.values(domains).map(d => d.count)
    const maxCount = Math.max(...counts)
    const minCount = Math.min(...counts)
    
    return minCount / maxCount // 1.0 = perfectly balanced, lower = imbalanced
  }

  // Analyze quality distribution
  analyzeQualityDistribution(agentResults) {
    const qualityScores = agentResults
      .filter(r => r.quality_score || r.confidence)
      .map(r => r.quality_score || r.confidence)
    
    if (qualityScores.length === 0) {
      return { average: 50, distribution: 'unknown', variance: 0 }
    }
    
    const average = qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length
    const variance = qualityScores.reduce((sum, score) => sum + Math.pow(score - average, 2), 0) / qualityScores.length
    
    let distribution = 'uniform'
    if (variance < 100) distribution = 'consistent'
    else if (variance > 400) distribution = 'varied'
    
    return {
      average,
      variance,
      distribution,
      range: { min: Math.min(...qualityScores), max: Math.max(...qualityScores) }
    }
  }

  // Identify potential conflicts between results
  identifyConflictIndicators(agentResults) {
    const indicators = []
    
    // Check for contradictory success/failure patterns
    const successCount = agentResults.filter(r => r.success).length
    const failureCount = agentResults.length - successCount
    
    if (successCount > 0 && failureCount > 0) {
      indicators.push({
        type: 'mixed_success',
        severity: 'medium',
        description: `${successCount} successful, ${failureCount} failed results`
      })
    }
    
    // Check for widely varying confidence levels
    const confidences = agentResults.filter(r => r.confidence).map(r => r.confidence)
    if (confidences.length > 1) {
      const maxConf = Math.max(...confidences)
      const minConf = Math.min(...confidences)
      
      if (maxConf - minConf > 40) {
        indicators.push({
          type: 'confidence_variance',
          severity: 'medium',
          description: `Confidence varies from ${minConf}% to ${maxConf}%`
        })
      }
    }
    
    // Check for content length disparities
    const contentLengths = agentResults
      .filter(r => r.content)
      .map(r => r.content.length)
    
    if (contentLengths.length > 1) {
      const maxLength = Math.max(...contentLengths)
      const minLength = Math.min(...contentLengths)
      
      if (maxLength > minLength * 3) {
        indicators.push({
          type: 'content_disparity',
          severity: 'low',
          description: `Content length varies significantly`
        })
      }
    }
    
    return indicators
  }

  // Determine synthesis complexity
  determineSynthesisComplexity(analysis) {
    let complexityScore = 0
    
    // More agents = more complexity
    complexityScore += Math.min(analysis.resultCount * 0.5, 3)
    
    // More domains = more complexity
    complexityScore += Math.min(analysis.domainCoverage.coverage * 0.5, 2)
    
    // Conflicts increase complexity
    complexityScore += analysis.conflictIndicators.length * 0.5
    
    // Low similarity increases complexity
    if (analysis.contentSimilarity.score < 0.3) complexityScore += 1
    
    // Quality variance increases complexity
    if (analysis.qualityDistribution.variance > 300) complexityScore += 1
    
    if (complexityScore < 2) return 'simple'
    if (complexityScore < 4) return 'medium'
    if (complexityScore < 6) return 'complex'
    return 'very_complex'
  }

  // Select optimal synthesis strategy
  selectSynthesisStrategy(resultAnalysis, synthesisOptions = {}) {
    console.log('🎯 Selecting synthesis strategy...')
    
    // User preference override
    if (synthesisOptions.strategy && this.synthesisStrategies.has(synthesisOptions.strategy)) {
      return this.synthesisStrategies.get(synthesisOptions.strategy)
    }
    
    // Strategy selection logic
    const { conflictIndicators, domainCoverage, synthesisComplexity, contentSimilarity } = resultAnalysis
    
    // High conflicts -> consensus strategy
    if (conflictIndicators.length > 2) {
      return this.synthesisStrategies.get('consensus')
    }
    
    // Multiple domains with expertise -> weighted strategy
    if (domainCoverage.coverage > 2 && domainCoverage.balance > 0.5) {
      return this.synthesisStrategies.get('weighted')
    }
    
    // Complex tasks -> hierarchical strategy
    if (synthesisComplexity === 'complex' || synthesisComplexity === 'very_complex') {
      return this.synthesisStrategies.get('hierarchical')
    }
    
    // Creative or diverse content -> collaborative strategy
    if (contentSimilarity.score < 0.3 && domainCoverage.domains.includes('creative')) {
      return this.synthesisStrategies.get('collaborative')
    }
    
    // Sequential dependencies -> sequential strategy
    if (synthesisOptions.hasSequentialDependencies) {
      return this.synthesisStrategies.get('sequential')
    }
    
    // Default to hierarchical for quality
    return this.synthesisStrategies.get('hierarchical')
  }

  // Detect and resolve conflicts between agent results
  async detectAndResolveConflicts(agentResults, resultAnalysis) {
    console.log('🔍 Detecting and resolving conflicts...')
    
    const conflicts = []
    const resolutions = []
    
    // Detect factual conflicts
    const factualConflicts = await this.detectFactualConflicts(agentResults)
    conflicts.push(...factualConflicts)
    
    // Detect approach conflicts
    const approachConflicts = this.detectApproachConflicts(agentResults)
    conflicts.push(...approachConflicts)
    
    // Detect quality conflicts
    const qualityConflicts = this.detectQualityConflicts(agentResults, resultAnalysis)
    conflicts.push(...qualityConflicts)
    
    // Resolve each conflict
    for (const conflict of conflicts) {
      const resolution = await this.resolveConflict(conflict, agentResults)
      resolutions.push(resolution)
    }
    
    return {
      conflicts,
      resolutions,
      conflictCount: conflicts.length,
      resolutionSuccess: resolutions.filter(r => r.success).length / Math.max(resolutions.length, 1)
    }
  }

  // Detect factual conflicts between results
  async detectFactualConflicts(agentResults) {
    const conflicts = []
    
    // Simple conflict detection based on contradictory statements
    const successfulResults = agentResults.filter(r => r.success && r.content)
    
    for (let i = 0; i < successfulResults.length; i++) {
      for (let j = i + 1; j < successfulResults.length; j++) {
        const result1 = successfulResults[i]
        const result2 = successfulResults[j]
        
        // Check for contradictory keywords
        const contradictions = this.findContradictions(result1.content, result2.content)
        
        if (contradictions.length > 0) {
          conflicts.push({
            type: 'factual_conflict',
            severity: 'high',
            agents: [result1.agentType, result2.agentType],
            contradictions,
            description: `Factual disagreement between ${result1.agentType} and ${result2.agentType}`
          })
        }
      }
    }
    
    return conflicts
  }

  // Find contradictions between two pieces of content
  findContradictions(content1, content2) {
    const contradictions = []
    
    // Simple contradiction patterns
    const contradictionPairs = [
      ['true', 'false'],
      ['yes', 'no'],
      ['correct', 'incorrect'],
      ['valid', 'invalid'],
      ['possible', 'impossible'],
      ['increase', 'decrease'],
      ['positive', 'negative']
    ]
    
    const content1Lower = content1.toLowerCase()
    const content2Lower = content2.toLowerCase()
    
    contradictionPairs.forEach(([word1, word2]) => {
      if (content1Lower.includes(word1) && content2Lower.includes(word2)) {
        contradictions.push({ term1: word1, term2: word2, type: 'direct_contradiction' })
      }
      if (content1Lower.includes(word2) && content2Lower.includes(word1)) {
        contradictions.push({ term1: word2, term2: word1, type: 'direct_contradiction' })
      }
    })
    
    return contradictions
  }

  // Detect approach conflicts
  detectApproachConflicts(agentResults) {
    const conflicts = []
    
    // Check for significantly different approaches to the same task
    const approaches = agentResults
      .filter(r => r.success)
      .map(r => ({
        agentType: r.agentType,
        approach: this.extractApproach(r.content || ''),
        confidence: r.confidence || 50
      }))
    
    // If approaches are very different, flag as conflict
    if (approaches.length > 1) {
      const approachSimilarity = this.calculateApproachSimilarity(approaches)
      
      if (approachSimilarity < 0.3) {
        conflicts.push({
          type: 'approach_conflict',
          severity: 'medium',
          agents: approaches.map(a => a.agentType),
          description: 'Agents used significantly different approaches',
          similarity: approachSimilarity
        })
      }
    }
    
    return conflicts
  }

  // Extract approach from content
  extractApproach(content) {
    // Simple approach extraction based on method keywords
    const methodKeywords = [
      'analyze', 'research', 'implement', 'create', 'design',
      'calculate', 'evaluate', 'compare', 'synthesize', 'generate'
    ]
    
    const contentLower = content.toLowerCase()
    const foundMethods = methodKeywords.filter(keyword => contentLower.includes(keyword))
    
    return foundMethods.join(', ') || 'general'
  }

  // Calculate approach similarity
  calculateApproachSimilarity(approaches) {
    if (approaches.length < 2) return 1.0
    
    const approachSets = approaches.map(a => new Set(a.approach.split(', ')))
    
    let totalSimilarity = 0
    let comparisons = 0
    
    for (let i = 0; i < approachSets.length; i++) {
      for (let j = i + 1; j < approachSets.length; j++) {
        const intersection = new Set([...approachSets[i]].filter(x => approachSets[j].has(x)))
        const union = new Set([...approachSets[i], ...approachSets[j]])
        
        const similarity = intersection.size / union.size
        totalSimilarity += similarity
        comparisons++
      }
    }
    
    return comparisons > 0 ? totalSimilarity / comparisons : 0
  }

  // Detect quality conflicts
  detectQualityConflicts(agentResults, resultAnalysis) {
    const conflicts = []
    
    // Check for significant quality disparities
    if (resultAnalysis.qualityDistribution.variance > 400) {
      const highQuality = agentResults.filter(r => (r.quality_score || r.confidence || 0) > 80)
      const lowQuality = agentResults.filter(r => (r.quality_score || r.confidence || 0) < 40)
      
      if (highQuality.length > 0 && lowQuality.length > 0) {
        conflicts.push({
          type: 'quality_conflict',
          severity: 'medium',
          description: 'Significant quality disparity between agent results',
          highQualityAgents: highQuality.map(r => r.agentType),
          lowQualityAgents: lowQuality.map(r => r.agentType)
        })
      }
    }
    
    return conflicts
  }

  // Resolve individual conflict
  async resolveConflict(conflict, agentResults) {
    console.log(`🔧 Resolving ${conflict.type} conflict...`)
    
    try {
      switch (conflict.type) {
        case 'factual_conflict':
          return await this.resolveFactualConflict(conflict, agentResults)
        
        case 'approach_conflict':
          return await this.resolveApproachConflict(conflict, agentResults)
        
        case 'quality_conflict':
          return await this.resolveQualityConflict(conflict, agentResults)
        
        default:
          return this.createGenericResolution(conflict)
      }
    } catch (error) {
      console.error(`❌ Failed to resolve ${conflict.type} conflict:`, error)
      return {
        success: false,
        conflict: conflict.type,
        error: error.message,
        fallback: 'Use highest confidence result'
      }
    }
  }

  // Resolve factual conflicts
  async resolveFactualConflict(conflict, agentResults) {
    // Use research agent or highest confidence result for fact-checking
    const researchResults = agentResults.filter(r => r.agentType === 'research' && r.success)
    
    if (researchResults.length > 0) {
      const bestResearch = researchResults.reduce((best, current) => 
        (current.confidence || 0) > (best.confidence || 0) ? current : best
      )
      
      return {
        success: true,
        method: 'research_authority',
        resolution: `Defer to research agent findings: ${bestResearch.agentType}`,
        authorityResult: bestResearch,
        confidence: bestResearch.confidence || 75
      }
    }
    
    // Fallback: use highest confidence result
    const highestConfidence = agentResults
      .filter(r => r.success)
      .reduce((best, current) => 
        (current.confidence || 0) > (best.confidence || 0) ? current : best
      )
    
    return {
      success: true,
      method: 'highest_confidence',
      resolution: `Use result with highest confidence: ${highestConfidence.agentType}`,
      selectedResult: highestConfidence,
      confidence: highestConfidence.confidence || 50
    }
  }

  // Resolve approach conflicts
  async resolveApproachConflict(conflict, agentResults) {
    // Combine different approaches or select most comprehensive
    const conflictingResults = agentResults.filter(r => 
      conflict.agents.includes(r.agentType) && r.success
    )
    
    if (conflictingResults.length > 1) {
      return {
        success: true,
        method: 'approach_combination',
        resolution: 'Combine different approaches for comprehensive solution',
        approaches: conflictingResults.map(r => ({
          agent: r.agentType,
          approach: this.extractApproach(r.content || ''),
          confidence: r.confidence || 50
        })),
        recommendation: 'Present multiple valid approaches'
      }
    }
    
    return this.createGenericResolution(conflict)
  }

  // Resolve quality conflicts
  async resolveQualityConflict(conflict, agentResults) {
    // Prioritize high-quality results
    const highQualityResults = agentResults.filter(r => 
      (r.quality_score || r.confidence || 0) > 70 && r.success
    )
    
    if (highQualityResults.length > 0) {
      return {
        success: true,
        method: 'quality_prioritization',
        resolution: 'Prioritize high-quality results',
        selectedResults: highQualityResults.map(r => ({
          agent: r.agentType,
          quality: r.quality_score || r.confidence || 0
        })),
        averageQuality: highQualityResults.reduce((sum, r) => 
          sum + (r.quality_score || r.confidence || 0), 0
        ) / highQualityResults.length
      }
    }
    
    return this.createGenericResolution(conflict)
  }

  // Create generic resolution
  createGenericResolution(conflict) {
    return {
      success: true,
      method: 'generic_resolution',
      resolution: `Acknowledge ${conflict.type} and present multiple perspectives`,
      recommendation: 'Include disclaimer about conflicting information'
    }
  }

  // Generate synthesis ID
  generateSynthesisId() {
    return `synthesis_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`
  }

  // Calculate synthesis complexity
  calculateSynthesisComplexity(agentResults, strategy) {
    let complexity = 1

    complexity += agentResults.length * 0.2
    complexity += strategy.qualityFocus === 'high' ? 0.5 : 0
    complexity += agentResults.filter(r => !r.success).length * 0.3

    return Math.round(complexity * 10) / 10
  }

  // Execute synthesis based on selected strategy
  async executeSynthesis(agentResults, strategy, conflictResolution, originalTask) {
    console.log(`🔄 Executing ${strategy.name} synthesis...`)

    const successfulResults = agentResults.filter(r => r.success)

    if (successfulResults.length === 0) {
      throw new Error('No successful results to synthesize')
    }

    switch (strategy.process) {
      case 'coordinator_led':
        return await this.executeHierarchicalSynthesis(successfulResults, conflictResolution, originalTask)

      case 'peer_to_peer':
        return await this.executeCollaborativeSynthesis(successfulResults, conflictResolution, originalTask)

      case 'expertise_weighted':
        return await this.executeWeightedSynthesis(successfulResults, conflictResolution, originalTask)

      case 'dependency_ordered':
        return await this.executeSequentialSynthesis(successfulResults, conflictResolution, originalTask)

      case 'agreement_focused':
        return await this.executeConsensusSynthesis(successfulResults, conflictResolution, originalTask)

      default:
        return await this.executeHierarchicalSynthesis(successfulResults, conflictResolution, originalTask)
    }
  }

  // Execute hierarchical synthesis
  async executeHierarchicalSynthesis(results, conflictResolution, originalTask) {
    const prompt = `As a coordinator, synthesize these agent results into a comprehensive response:

Original Task: ${originalTask}

Agent Results:
${results.map((r, i) => `
${i + 1}. ${r.agentType} Agent (Confidence: ${r.confidence || 'N/A'}%):
${r.content || r.result || 'No content'}
`).join('\n')}

Conflict Resolutions:
${conflictResolution.resolutions.map(res => `- ${res.resolution}`).join('\n')}

Provide a well-structured, comprehensive synthesis that:
1. Integrates all valuable insights
2. Resolves any conflicts appropriately
3. Maintains high quality and coherence
4. Addresses the original task completely
5. Acknowledges different perspectives where relevant`

    try {
      const response = await geminiService.generateResponse(prompt, 'synthesis')

      return {
        content: response.content,
        method: 'hierarchical',
        structure: this.extractSynthesisStructure(response.content),
        keyInsights: this.extractKeyInsights(response.content),
        confidence: this.calculateSynthesisConfidence(results, conflictResolution)
      }
    } catch (error) {
      throw new Error(`Hierarchical synthesis failed: ${error.message}`)
    }
  }

  // Execute collaborative synthesis
  async executeCollaborativeSynthesis(results, conflictResolution, originalTask) {
    const prompt = `Collaboratively synthesize these diverse agent perspectives:

Original Task: ${originalTask}

Agent Contributions:
${results.map((r, i) => `
${r.agentType}: ${r.content || r.result || 'No content'}
`).join('\n')}

Create a synthesis that:
1. Celebrates diverse perspectives
2. Finds creative connections between ideas
3. Builds upon each agent's strengths
4. Creates something greater than the sum of parts
5. Maintains creative flow and innovation`

    try {
      const response = await geminiService.generateResponse(prompt, 'creative')

      return {
        content: response.content,
        method: 'collaborative',
        perspectives: results.map(r => r.agentType),
        creativity: this.assessCreativity(response.content),
        confidence: this.calculateSynthesisConfidence(results, conflictResolution)
      }
    } catch (error) {
      throw new Error(`Collaborative synthesis failed: ${error.message}`)
    }
  }

  // Execute weighted synthesis
  async executeWeightedSynthesis(results, conflictResolution, originalTask) {
    // Calculate weights based on agent expertise and confidence
    const weightedResults = results.map(result => ({
      ...result,
      weight: this.calculateAgentWeight(result, originalTask)
    }))

    // Sort by weight (highest first)
    weightedResults.sort((a, b) => b.weight - a.weight)

    const prompt = `Synthesize these expert results with weighted importance:

Original Task: ${originalTask}

Weighted Results (by expertise and confidence):
${weightedResults.map((r, i) => `
${i + 1}. ${r.agentType} Agent (Weight: ${r.weight.toFixed(2)}, Confidence: ${r.confidence || 'N/A'}%):
${r.content || r.result || 'No content'}
`).join('\n')}

Create a synthesis that:
1. Gives appropriate weight to expert opinions
2. Prioritizes high-confidence results
3. Maintains technical accuracy
4. Provides evidence-based conclusions
5. Acknowledges uncertainty where appropriate`

    try {
      const response = await geminiService.generateResponse(prompt, 'analysis')

      return {
        content: response.content,
        method: 'weighted',
        weights: weightedResults.map(r => ({ agent: r.agentType, weight: r.weight })),
        expertiseLevel: 'high',
        confidence: this.calculateWeightedConfidence(weightedResults)
      }
    } catch (error) {
      throw new Error(`Weighted synthesis failed: ${error.message}`)
    }
  }

  // Execute sequential synthesis
  async executeSequentialSynthesis(results, conflictResolution, originalTask) {
    // Order results by logical dependencies or agent type priority
    const orderedResults = this.orderResultsSequentially(results)

    const prompt = `Synthesize these results in logical sequence:

Original Task: ${originalTask}

Sequential Results:
${orderedResults.map((r, i) => `
Step ${i + 1} - ${r.agentType} Agent:
${r.content || r.result || 'No content'}
`).join('\n')}

Create a synthesis that:
1. Follows logical progression
2. Builds each step on previous ones
3. Maintains workflow coherence
4. Shows clear cause-and-effect relationships
5. Provides step-by-step insights`

    try {
      const response = await geminiService.generateResponse(prompt, 'logical_reasoning')

      return {
        content: response.content,
        method: 'sequential',
        sequence: orderedResults.map(r => r.agentType),
        workflow: this.extractWorkflow(response.content),
        confidence: this.calculateSynthesisConfidence(results, conflictResolution)
      }
    } catch (error) {
      throw new Error(`Sequential synthesis failed: ${error.message}`)
    }
  }

  // Execute consensus synthesis
  async executeConsensusSynthesis(results, conflictResolution, originalTask) {
    // Find areas of agreement
    const consensus = this.findConsensusAreas(results)

    const prompt = `Find consensus and resolve conflicts in these agent results:

Original Task: ${originalTask}

Agent Results:
${results.map((r, i) => `
${r.agentType}: ${r.content || r.result || 'No content'}
`).join('\n')}

Areas of Agreement:
${consensus.agreements.join('\n')}

Areas of Disagreement:
${consensus.disagreements.join('\n')}

Create a synthesis that:
1. Emphasizes areas of consensus
2. Addresses disagreements transparently
3. Provides balanced perspective
4. Builds confidence through agreement
5. Acknowledges limitations and uncertainties`

    try {
      const response = await geminiService.generateResponse(prompt, 'analysis')

      return {
        content: response.content,
        method: 'consensus',
        consensus: consensus,
        agreementLevel: consensus.agreementScore,
        confidence: Math.min(90, consensus.agreementScore * 100)
      }
    } catch (error) {
      throw new Error(`Consensus synthesis failed: ${error.message}`)
    }
  }

  // Calculate agent weight for weighted synthesis
  calculateAgentWeight(result, originalTask) {
    let weight = 0.5 // Base weight

    // Confidence weight
    if (result.confidence) {
      weight += (result.confidence / 100) * 0.3
    }

    // Agent type relevance weight
    const taskLower = originalTask.toLowerCase()
    const relevanceBonus = {
      'research': taskLower.includes('research') || taskLower.includes('find') ? 0.2 : 0,
      'coding': taskLower.includes('code') || taskLower.includes('program') ? 0.2 : 0,
      'analysis': taskLower.includes('analyze') || taskLower.includes('data') ? 0.2 : 0,
      'creative': taskLower.includes('create') || taskLower.includes('design') ? 0.2 : 0,
      'coordinator': 0.1 // Always gets some bonus for coordination
    }

    weight += relevanceBonus[result.agentType] || 0

    // Quality weight
    if (result.quality_score) {
      weight += (result.quality_score / 100) * 0.2
    }

    return Math.min(1.0, weight)
  }

  // Calculate weighted confidence
  calculateWeightedConfidence(weightedResults) {
    const totalWeight = weightedResults.reduce((sum, r) => sum + r.weight, 0)
    const weightedConfidence = weightedResults.reduce((sum, r) =>
      sum + (r.confidence || 50) * r.weight, 0
    )

    return totalWeight > 0 ? weightedConfidence / totalWeight : 50
  }

  // Order results sequentially
  orderResultsSequentially(results) {
    // Simple ordering by agent type priority for workflow
    const priorityOrder = ['research', 'analysis', 'coding', 'creative', 'coordinator']

    return results.sort((a, b) => {
      const priorityA = priorityOrder.indexOf(a.agentType)
      const priorityB = priorityOrder.indexOf(b.agentType)

      // If both found in priority list, use that order
      if (priorityA !== -1 && priorityB !== -1) {
        return priorityA - priorityB
      }

      // If only one found, prioritize it
      if (priorityA !== -1) return -1
      if (priorityB !== -1) return 1

      // If neither found, maintain original order
      return 0
    })
  }

  // Find consensus areas
  findConsensusAreas(results) {
    const agreements = []
    const disagreements = []

    // Simple consensus finding based on common themes
    const allContent = results.map(r => r.content || r.result || '').join(' ').toLowerCase()
    const words = allContent.split(/\s+/).filter(word => word.length > 3)

    // Find frequently mentioned concepts
    const wordFreq = {}
    words.forEach(word => {
      wordFreq[word] = (wordFreq[word] || 0) + 1
    })

    const commonWords = Object.entries(wordFreq)
      .filter(([word, freq]) => freq >= Math.ceil(results.length / 2))
      .map(([word]) => word)
      .slice(0, 10)

    if (commonWords.length > 0) {
      agreements.push(`Common themes: ${commonWords.join(', ')}`)
    }

    // Check for contradictions
    const contradictions = this.findOverallContradictions(results)
    disagreements.push(...contradictions)

    const agreementScore = Math.max(0.1, commonWords.length / 10)

    return {
      agreements,
      disagreements,
      agreementScore,
      consensusStrength: agreementScore > 0.5 ? 'strong' : agreementScore > 0.2 ? 'moderate' : 'weak'
    }
  }

  // Find overall contradictions
  findOverallContradictions(results) {
    const contradictions = []

    // Check for conflicting conclusions
    const conclusions = results.map(r => this.extractConclusion(r.content || r.result || ''))

    for (let i = 0; i < conclusions.length; i++) {
      for (let j = i + 1; j < conclusions.length; j++) {
        const conflicts = this.findContradictions(conclusions[i], conclusions[j])
        if (conflicts.length > 0) {
          contradictions.push(`${results[i].agentType} vs ${results[j].agentType}: ${conflicts[0].type}`)
        }
      }
    }

    return contradictions
  }

  // Extract conclusion from content
  extractConclusion(content) {
    // Look for conclusion indicators
    const conclusionPatterns = [
      /conclusion[:\s](.+?)(?:\n|$)/i,
      /in summary[:\s](.+?)(?:\n|$)/i,
      /therefore[:\s](.+?)(?:\n|$)/i,
      /result[:\s](.+?)(?:\n|$)/i
    ]

    for (const pattern of conclusionPatterns) {
      const match = content.match(pattern)
      if (match) {
        return match[1].trim()
      }
    }

    // Fallback: use last sentence
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0)
    return sentences.length > 0 ? sentences[sentences.length - 1].trim() : content.substring(0, 100)
  }

  // Validate and enhance synthesis
  async validateAndEnhanceSynthesis(synthesisResult, resultAnalysis) {
    console.log('✅ Validating and enhancing synthesis...')

    // Basic validation
    const validation = {
      hasContent: !!synthesisResult.content && synthesisResult.content.length > 50,
      isCoherent: this.assessCoherence(synthesisResult.content),
      isComplete: this.assessCompleteness(synthesisResult.content, resultAnalysis),
      qualityScore: 0
    }

    validation.qualityScore = (
      (validation.hasContent ? 30 : 0) +
      (validation.isCoherent * 35) +
      (validation.isComplete * 35)
    )

    // Enhancement if needed
    if (validation.qualityScore < 70) {
      const enhanced = await this.enhanceSynthesis(synthesisResult, validation)
      return { ...synthesisResult, ...enhanced, validation }
    }

    return { ...synthesisResult, validation }
  }

  // Assess coherence
  assessCoherence(content) {
    if (!content) return 0

    let score = 0.5 // Base score

    // Check for logical flow indicators
    const flowIndicators = ['first', 'then', 'next', 'finally', 'however', 'therefore', 'additionally']
    const foundIndicators = flowIndicators.filter(indicator =>
      content.toLowerCase().includes(indicator)
    ).length

    score += Math.min(0.3, foundIndicators * 0.05)

    // Check for proper structure
    if (content.includes('\n') || content.includes('.')) score += 0.1
    if (content.length > 200) score += 0.1

    return Math.min(1.0, score)
  }

  // Assess completeness
  assessCompleteness(content, resultAnalysis) {
    if (!content) return 0

    let score = 0.5 // Base score

    // Check if synthesis addresses multiple domains
    const mentionedDomains = resultAnalysis.domainCoverage.domains.filter(domain =>
      content.toLowerCase().includes(domain)
    ).length

    score += (mentionedDomains / resultAnalysis.domainCoverage.domains.length) * 0.3

    // Check for comprehensive coverage
    if (content.length > 300) score += 0.1
    if (content.includes('conclusion') || content.includes('summary')) score += 0.1

    return Math.min(1.0, score)
  }

  // Enhance synthesis if quality is low
  async enhanceSynthesis(synthesisResult, validation) {
    const enhancementPrompt = `Enhance this synthesis to improve quality:

Current Synthesis:
${synthesisResult.content}

Issues to address:
${!validation.hasContent ? '- Add more substantial content\n' : ''}
${validation.isCoherent < 0.7 ? '- Improve logical flow and coherence\n' : ''}
${validation.isComplete < 0.7 ? '- Ensure comprehensive coverage\n' : ''}

Provide an enhanced version that is more comprehensive, coherent, and complete.`

    try {
      const response = await geminiService.generateResponse(enhancementPrompt, 'enhancement')

      return {
        content: response.content,
        enhanced: true,
        enhancementReason: 'Quality improvement',
        originalQuality: validation.qualityScore
      }
    } catch (error) {
      console.error('Enhancement failed:', error)
      return { enhanced: false, enhancementError: error.message }
    }
  }

  // Assess synthesis quality
  assessSynthesisQuality(synthesisResult, originalResults) {
    const quality = {
      overall: 0,
      coherence: 0,
      completeness: 0,
      accuracy: 0,
      innovation: 0,
      breakdown: {}
    }

    // Coherence assessment
    quality.coherence = this.assessCoherence(synthesisResult.content)

    // Completeness assessment
    quality.completeness = synthesisResult.validation?.isComplete || 0.5

    // Accuracy assessment (based on confidence and conflict resolution)
    quality.accuracy = Math.min(1.0, (synthesisResult.confidence || 50) / 100)

    // Innovation assessment (for creative syntheses)
    quality.innovation = synthesisResult.creativity || this.assessInnovation(synthesisResult.content)

    // Overall quality
    quality.overall = (
      quality.coherence * 0.3 +
      quality.completeness * 0.3 +
      quality.accuracy * 0.25 +
      quality.innovation * 0.15
    )

    quality.breakdown = {
      coherence: Math.round(quality.coherence * 100),
      completeness: Math.round(quality.completeness * 100),
      accuracy: Math.round(quality.accuracy * 100),
      innovation: Math.round(quality.innovation * 100)
    }

    return quality
  }

  // Assess innovation in content
  assessInnovation(content) {
    if (!content) return 0

    let score = 0.3 // Base score

    // Check for creative/innovative language
    const innovativeWords = ['innovative', 'creative', 'novel', 'unique', 'breakthrough', 'revolutionary']
    const foundWords = innovativeWords.filter(word =>
      content.toLowerCase().includes(word)
    ).length

    score += Math.min(0.4, foundWords * 0.1)

    // Check for synthesis of ideas
    if (content.includes('combine') || content.includes('integrate') || content.includes('merge')) {
      score += 0.2
    }

    // Check for new insights
    if (content.includes('insight') || content.includes('realize') || content.includes('discover')) {
      score += 0.1
    }

    return Math.min(1.0, score)
  }

  // Calculate synthesis confidence
  calculateSynthesisConfidence(results, conflictResolution) {
    const avgConfidence = results.reduce((sum, r) => sum + (r.confidence || 50), 0) / results.length
    const conflictPenalty = conflictResolution.conflicts.length * 5
    const resolutionBonus = conflictResolution.resolutionSuccess * 10

    return Math.max(10, Math.min(95, avgConfidence - conflictPenalty + resolutionBonus))
  }

  // Extract synthesis structure
  extractSynthesisStructure(content) {
    const structure = {
      sections: [],
      hasIntroduction: false,
      hasConclusion: false,
      hasSubheadings: false
    }

    // Look for numbered sections or bullet points
    const numberedSections = content.match(/(?:^|\n)\d+\.\s*(.+)/gm)
    if (numberedSections) {
      structure.sections = numberedSections.map(s => s.replace(/(?:^|\n)\d+\.\s*/, '').trim())
    }

    // Check for introduction/conclusion
    structure.hasIntroduction = /introduction|overview|summary/i.test(content.substring(0, 200))
    structure.hasConclusion = /conclusion|summary|in summary|therefore/i.test(content.substring(-200))

    // Check for subheadings
    structure.hasSubheadings = /#{1,3}\s/.test(content) || /\*\*[^*]+\*\*/.test(content)

    return structure
  }

  // Extract key insights
  extractKeyInsights(content) {
    const insights = []

    // Look for insight indicators
    const insightPatterns = [
      /key insight[s]?[:\s]*(.+?)(?:\n|$)/gi,
      /important[ly]?[:\s]*(.+?)(?:\n|$)/gi,
      /notably?[:\s]*(.+?)(?:\n|$)/gi,
      /significantly?[:\s]*(.+?)(?:\n|$)/gi
    ]

    insightPatterns.forEach(pattern => {
      const matches = [...content.matchAll(pattern)]
      matches.forEach(match => insights.push(match[1].trim()))
    })

    return insights.slice(0, 5) // Limit to top 5 insights
  }

  // Extract workflow from sequential synthesis
  extractWorkflow(content) {
    const workflow = []

    // Look for step indicators
    const stepMatches = content.match(/(?:step|phase)\s*\d+[:\s]*(.+?)(?:\n|$)/gi)
    if (stepMatches) {
      workflow.push(...stepMatches.map(step =>
        step.replace(/(?:step|phase)\s*\d+[:\s]*/i, '').trim()
      ))
    }

    return workflow
  }

  // Assess creativity for collaborative synthesis
  assessCreativity(content) {
    let score = 0.3 // Base score

    // Check for creative elements
    const creativeIndicators = ['imagine', 'creative', 'innovative', 'unique', 'original']
    const foundIndicators = creativeIndicators.filter(indicator =>
      content.toLowerCase().includes(indicator)
    ).length

    score += Math.min(0.4, foundIndicators * 0.1)

    // Check for metaphors or analogies
    if (/like|as if|similar to|reminds|metaphor/i.test(content)) {
      score += 0.2
    }

    // Check for diverse perspectives
    if (/perspective|viewpoint|approach|angle/i.test(content)) {
      score += 0.1
    }

    return Math.min(1.0, score)
  }

  // Create fallback synthesis
  createFallbackSynthesis(agentResults, originalTask) {
    const successfulResults = agentResults.filter(r => r.success)

    if (successfulResults.length === 0) {
      return {
        content: `Unable to complete task: ${originalTask}. No successful agent results available.`,
        method: 'fallback',
        confidence: 0
      }
    }

    // Simple concatenation fallback
    const combinedContent = successfulResults
      .map(r => `${r.agentType}: ${r.content || r.result || 'No content'}`)
      .join('\n\n---\n\n')

    return {
      content: `Task: ${originalTask}\n\nAgent Results:\n${combinedContent}`,
      method: 'fallback_concatenation',
      confidence: 40,
      note: 'Fallback synthesis due to processing error'
    }
  }

  // Update quality metrics
  updateQualityMetrics(synthesisResult) {
    this.qualityMetrics.totalSyntheses++

    if (synthesisResult.quality) {
      const total = this.qualityMetrics.totalSyntheses
      const newQuality = synthesisResult.quality.overall * 100

      this.qualityMetrics.averageQualityScore =
        (this.qualityMetrics.averageQualityScore * (total - 1) + newQuality) / total

      this.qualityMetrics.averageCoherenceScore =
        (this.qualityMetrics.averageCoherenceScore * (total - 1) + synthesisResult.quality.breakdown.coherence) / total

      this.qualityMetrics.averageCompletenessScore =
        (this.qualityMetrics.averageCompletenessScore * (total - 1) + synthesisResult.quality.breakdown.completeness) / total
    }

    if (synthesisResult.conflictResolution) {
      const conflictCount = synthesisResult.conflictResolution.conflicts.length
      const resolutionCount = synthesisResult.conflictResolution.resolutions.filter(r => r.success).length

      if (conflictCount > 0) {
        const resolutionRate = resolutionCount / conflictCount
        this.qualityMetrics.conflictResolutionRate =
          (this.qualityMetrics.conflictResolutionRate + resolutionRate) / 2
      }
    }
  }

  // Get synthesis statistics
  getSynthesisStats() {
    return {
      ...this.qualityMetrics,
      strategies: {
        available: Array.from(this.synthesisStrategies.keys()),
        usage: this.calculateStrategyUsage()
      },
      recentSyntheses: this.synthesisHistory.slice(-5).map(s => ({
        id: s.synthesisId,
        strategy: s.strategy,
        agentCount: s.agentResults.length,
        quality: s.quality?.overall || 0,
        timestamp: s.timestamp
      }))
    }
  }

  // Calculate strategy usage
  calculateStrategyUsage() {
    const usage = {}

    this.synthesisHistory.forEach(synthesis => {
      const strategy = synthesis.strategy
      usage[strategy] = (usage[strategy] || 0) + 1
    })

    return usage
  }

  // Cleanup old synthesis history
  cleanup() {
    const maxHistorySize = 50
    if (this.synthesisHistory.length > maxHistorySize) {
      this.synthesisHistory = this.synthesisHistory.slice(-maxHistorySize)
      console.log('🧹 Cleaned up old synthesis history')
    }
  }
}

// Create singleton instance
const agentResultSynthesis = new AgentResultSynthesis()
export default agentResultSynthesis
