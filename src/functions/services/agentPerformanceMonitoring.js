// Agent Performance Monitoring - Advanced monitoring and optimization for multi-agent systems
import agentFramework from './agentFramework.js'
import agentCoordinationSystem from './agentCoordination.js'
import agentResultSynthesis from './agentResultSynthesis.js'

class AgentPerformanceMonitoring {
  constructor() {
    this.name = 'agent_performance_monitoring'
    this.version = '1.0.0'
    this.performanceHistory = []
    this.alertThresholds = new Map()
    this.optimizationRules = new Map()
    this.monitoringInterval = null
    this.realTimeMetrics = {
      activeAgents: 0,
      averageResponseTime: 0,
      successRate: 0,
      collaborationEfficiency: 0,
      resourceUtilization: 0,
      lastUpdated: null
    }
  }

  // Initialize performance monitoring
  async initialize() {
    console.log('📊 Initializing Agent Performance Monitoring...')
    
    try {
      // Setup alert thresholds
      this.setupAlertThresholds()
      
      // Setup optimization rules
      this.setupOptimizationRules()
      
      // Start real-time monitoring
      this.startRealTimeMonitoring()
      
      console.log('✅ Agent Performance Monitoring initialized successfully')
      return true
    } catch (error) {
      console.error('❌ Failed to initialize Agent Performance Monitoring:', error)
      return false
    }
  }

  // Setup alert thresholds for different metrics
  setupAlertThresholds() {
    console.log('⚠️ Setting up alert thresholds...')
    
    // Success rate thresholds
    this.alertThresholds.set('success_rate', {
      critical: 0.5,  // 50% - Critical alert
      warning: 0.7,   // 70% - Warning alert
      target: 0.9     // 90% - Target performance
    })
    
    // Response time thresholds (in milliseconds)
    this.alertThresholds.set('response_time', {
      critical: 30000,  // 30 seconds - Critical
      warning: 15000,   // 15 seconds - Warning
      target: 5000      // 5 seconds - Target
    })
    
    // Collaboration efficiency thresholds
    this.alertThresholds.set('collaboration_efficiency', {
      critical: 0.4,  // 40% - Critical
      warning: 0.6,   // 60% - Warning
      target: 0.8     // 80% - Target
    })
    
    // Resource utilization thresholds
    this.alertThresholds.set('resource_utilization', {
      critical: 0.95,  // 95% - Critical (overloaded)
      warning: 0.85,   // 85% - Warning
      target: 0.7      // 70% - Target
    })
    
    // Agent availability thresholds
    this.alertThresholds.set('agent_availability', {
      critical: 0.3,  // 30% - Critical
      warning: 0.5,   // 50% - Warning
      target: 0.8     // 80% - Target
    })
    
    console.log(`⚠️ Setup ${this.alertThresholds.size} alert threshold categories`)
  }

  // Setup optimization rules
  setupOptimizationRules() {
    console.log('⚡ Setting up optimization rules...')
    
    // Load balancing rule
    this.optimizationRules.set('load_balancing', {
      name: 'Load Balancing Optimization',
      condition: (metrics) => metrics.resourceUtilization > 0.8,
      action: 'redistribute_tasks',
      priority: 'high',
      description: 'Redistribute tasks when resource utilization is high'
    })
    
    // Agent scaling rule
    this.optimizationRules.set('agent_scaling', {
      name: 'Agent Scaling',
      condition: (metrics) => metrics.activeAgents < 2 && metrics.taskQueue > 5,
      action: 'scale_up_agents',
      priority: 'medium',
      description: 'Create additional agents when task queue is large'
    })
    
    // Performance degradation rule
    this.optimizationRules.set('performance_degradation', {
      name: 'Performance Degradation Response',
      condition: (metrics) => metrics.successRate < 0.7,
      action: 'performance_intervention',
      priority: 'critical',
      description: 'Intervene when success rate drops significantly'
    })
    
    // Collaboration optimization rule
    this.optimizationRules.set('collaboration_optimization', {
      name: 'Collaboration Efficiency',
      condition: (metrics) => metrics.collaborationEfficiency < 0.6,
      action: 'optimize_collaboration',
      priority: 'medium',
      description: 'Optimize collaboration when efficiency is low'
    })
    
    // Resource cleanup rule
    this.optimizationRules.set('resource_cleanup', {
      name: 'Resource Cleanup',
      condition: (metrics) => metrics.idleAgents > 3,
      action: 'cleanup_idle_agents',
      priority: 'low',
      description: 'Clean up idle agents to free resources'
    })
    
    console.log(`⚡ Setup ${this.optimizationRules.size} optimization rules`)
  }

  // Start real-time monitoring
  startRealTimeMonitoring() {
    console.log('🔄 Starting real-time monitoring...')
    
    // Monitor every 30 seconds
    this.monitoringInterval = setInterval(() => {
      this.updateRealTimeMetrics()
      this.checkAlerts()
      this.applyOptimizations()
    }, 30000)
    
    // Initial metrics update
    this.updateRealTimeMetrics()
  }

  // Update real-time metrics
  async updateRealTimeMetrics() {
    try {
      // Get framework stats
      const frameworkStats = agentFramework.getFrameworkStats()
      
      // Get coordination stats
      const coordinationStats = agentCoordinationSystem.getCoordinationStats()
      
      // Get synthesis stats
      const synthesisStats = agentResultSynthesis.getSynthesisStats()
      
      // Calculate derived metrics
      const metrics = this.calculateDerivedMetrics(frameworkStats, coordinationStats, synthesisStats)
      
      // Update real-time metrics
      this.realTimeMetrics = {
        ...metrics,
        lastUpdated: new Date().toISOString()
      }
      
      // Store in history
      this.performanceHistory.push({
        timestamp: new Date().toISOString(),
        metrics: { ...this.realTimeMetrics }
      })
      
      // Limit history size
      if (this.performanceHistory.length > 1000) {
        this.performanceHistory = this.performanceHistory.slice(-1000)
      }
      
    } catch (error) {
      console.error('❌ Failed to update real-time metrics:', error)
    }
  }

  // Calculate derived metrics from raw stats
  calculateDerivedMetrics(frameworkStats, coordinationStats, synthesisStats) {
    const metrics = {
      // Basic counts
      activeAgents: frameworkStats.totalAgentInstances || 0,
      totalCoordinations: coordinationStats.totalCoordinations || 0,
      totalSyntheses: synthesisStats.totalSyntheses || 0,
      
      // Performance metrics
      successRate: frameworkStats.performanceOverview?.averageSuccessRate || 0,
      averageResponseTime: frameworkStats.performanceOverview?.averageCompletionTime || 0,
      
      // Collaboration metrics
      collaborationEfficiency: this.calculateCollaborationEfficiency(coordinationStats),
      synthesisQuality: synthesisStats.averageQualityScore || 0,
      
      // Resource metrics
      resourceUtilization: this.calculateResourceUtilization(frameworkStats),
      agentAvailability: this.calculateAgentAvailability(frameworkStats),
      
      // Trend metrics
      performanceTrend: this.calculatePerformanceTrend(),
      efficiencyTrend: this.calculateEfficiencyTrend(),
      
      // Agent type breakdown
      agentTypeStats: frameworkStats.agentTypeStats || {},
      
      // Recent activity
      recentActivity: {
        coordinationsLast5Min: this.countRecentActivity(coordinationStats, 5),
        synthesesLast5Min: this.countRecentActivity(synthesisStats, 5),
        averageTasksPerAgent: this.calculateAverageTasksPerAgent(frameworkStats)
      }
    }
    
    return metrics
  }

  // Calculate collaboration efficiency
  calculateCollaborationEfficiency(coordinationStats) {
    if (!coordinationStats.totalCoordinations || coordinationStats.totalCoordinations === 0) {
      return 0.5 // Neutral when no data
    }
    
    const successRate = coordinationStats.successRate || 0
    const avgCompletionTime = coordinationStats.averageCompletionTime || 10000
    
    // Efficiency based on success rate and completion time
    const timeEfficiency = Math.max(0, 1 - (avgCompletionTime / 30000)) // 30 seconds as baseline
    const efficiency = (successRate + timeEfficiency) / 2
    
    return Math.max(0, Math.min(1, efficiency))
  }

  // Calculate resource utilization
  calculateResourceUtilization(frameworkStats) {
    const totalAgents = frameworkStats.totalAgentInstances || 0
    const activeAgents = Object.values(frameworkStats.agentTypeStats || {})
      .reduce((sum, type) => sum + (type.activeInstances || 0), 0)
    
    if (totalAgents === 0) return 0
    
    return activeAgents / totalAgents
  }

  // Calculate agent availability
  calculateAgentAvailability(frameworkStats) {
    const totalAgents = frameworkStats.totalAgentInstances || 0
    const activeAgents = Object.values(frameworkStats.agentTypeStats || {})
      .reduce((sum, type) => sum + (type.activeInstances || 0), 0)
    
    if (totalAgents === 0) return 1 // Full availability when no agents
    
    const availableAgents = totalAgents - activeAgents
    return availableAgents / totalAgents
  }

  // Calculate performance trend
  calculatePerformanceTrend() {
    if (this.performanceHistory.length < 2) return 'stable'
    
    const recent = this.performanceHistory.slice(-5)
    const successRates = recent.map(h => h.metrics.successRate)
    
    const trend = this.calculateTrend(successRates)
    return trend
  }

  // Calculate efficiency trend
  calculateEfficiencyTrend() {
    if (this.performanceHistory.length < 2) return 'stable'
    
    const recent = this.performanceHistory.slice(-5)
    const efficiencyRates = recent.map(h => h.metrics.collaborationEfficiency)
    
    const trend = this.calculateTrend(efficiencyRates)
    return trend
  }

  // Calculate trend from array of values
  calculateTrend(values) {
    if (values.length < 2) return 'stable'
    
    const first = values[0]
    const last = values[values.length - 1]
    const change = last - first
    
    if (Math.abs(change) < 0.05) return 'stable'
    return change > 0 ? 'improving' : 'declining'
  }

  // Count recent activity
  countRecentActivity(stats, minutes) {
    // This would need to be implemented based on actual stats structure
    // For now, return a placeholder
    return Math.floor(Math.random() * 10)
  }

  // Calculate average tasks per agent
  calculateAverageTasksPerAgent(frameworkStats) {
    const totalTasks = frameworkStats.performanceOverview?.totalTasksCompleted || 0
    const totalAgents = frameworkStats.totalAgentInstances || 1
    
    return totalTasks / totalAgents
  }

  // Check for alerts based on current metrics
  checkAlerts() {
    const alerts = []
    
    // Check each metric against thresholds
    this.alertThresholds.forEach((thresholds, metricName) => {
      const currentValue = this.getMetricValue(metricName)
      const alert = this.evaluateThreshold(metricName, currentValue, thresholds)
      
      if (alert) {
        alerts.push(alert)
      }
    })
    
    // Process alerts
    if (alerts.length > 0) {
      this.processAlerts(alerts)
    }
  }

  // Get current value for a metric
  getMetricValue(metricName) {
    switch (metricName) {
      case 'success_rate':
        return this.realTimeMetrics.successRate
      case 'response_time':
        return this.realTimeMetrics.averageResponseTime
      case 'collaboration_efficiency':
        return this.realTimeMetrics.collaborationEfficiency
      case 'resource_utilization':
        return this.realTimeMetrics.resourceUtilization
      case 'agent_availability':
        return this.realTimeMetrics.agentAvailability
      default:
        return 0
    }
  }

  // Evaluate threshold for a metric
  evaluateThreshold(metricName, currentValue, thresholds) {
    let severity = null
    let message = ''
    
    // For metrics where lower is worse (success_rate, collaboration_efficiency, agent_availability)
    if (['success_rate', 'collaboration_efficiency', 'agent_availability'].includes(metricName)) {
      if (currentValue <= thresholds.critical) {
        severity = 'critical'
        message = `${metricName} is critically low: ${Math.round(currentValue * 100)}%`
      } else if (currentValue <= thresholds.warning) {
        severity = 'warning'
        message = `${metricName} is below warning threshold: ${Math.round(currentValue * 100)}%`
      }
    }
    // For metrics where higher is worse (response_time, resource_utilization)
    else if (['response_time', 'resource_utilization'].includes(metricName)) {
      if (currentValue >= thresholds.critical) {
        severity = 'critical'
        message = `${metricName} is critically high: ${currentValue}`
      } else if (currentValue >= thresholds.warning) {
        severity = 'warning'
        message = `${metricName} is above warning threshold: ${currentValue}`
      }
    }
    
    if (severity) {
      return {
        metric: metricName,
        severity,
        message,
        currentValue,
        threshold: severity === 'critical' ? thresholds.critical : thresholds.warning,
        timestamp: new Date().toISOString()
      }
    }
    
    return null
  }

  // Process alerts
  processAlerts(alerts) {
    console.log(`⚠️ Processing ${alerts.length} alerts...`)
    
    alerts.forEach(alert => {
      console.log(`${alert.severity.toUpperCase()}: ${alert.message}`)
      
      // Store alert in history
      this.storeAlert(alert)
      
      // Trigger automatic responses for critical alerts
      if (alert.severity === 'critical') {
        this.handleCriticalAlert(alert)
      }
    })
  }

  // Store alert in history
  storeAlert(alert) {
    if (!this.alertHistory) {
      this.alertHistory = []
    }
    
    this.alertHistory.push(alert)
    
    // Limit alert history
    if (this.alertHistory.length > 100) {
      this.alertHistory = this.alertHistory.slice(-100)
    }
  }

  // Handle critical alerts
  handleCriticalAlert(alert) {
    console.log(`🚨 Handling critical alert: ${alert.metric}`)
    
    switch (alert.metric) {
      case 'success_rate':
        this.handleLowSuccessRate()
        break
      case 'response_time':
        this.handleHighResponseTime()
        break
      case 'collaboration_efficiency':
        this.handleLowCollaborationEfficiency()
        break
      case 'resource_utilization':
        this.handleHighResourceUtilization()
        break
      case 'agent_availability':
        this.handleLowAgentAvailability()
        break
    }
  }

  // Handle low success rate
  handleLowSuccessRate() {
    console.log('🔧 Handling low success rate...')
    
    // Reset underperforming agents
    const frameworkStats = agentFramework.getFrameworkStats()
    Object.entries(frameworkStats.agentTypeStats || {}).forEach(([type, stats]) => {
      if (stats.averagePerformance?.successRate < 0.5) {
        console.log(`🔄 Resetting underperforming ${type} agents`)
        // Could implement agent reset logic here
      }
    })
  }

  // Handle high response time
  handleHighResponseTime() {
    console.log('⚡ Handling high response time...')
    
    // Optimize task distribution
    this.optimizeTaskDistribution()
  }

  // Handle low collaboration efficiency
  handleLowCollaborationEfficiency() {
    console.log('🤝 Handling low collaboration efficiency...')
    
    // Optimize coordination protocols
    this.optimizeCoordinationProtocols()
  }

  // Handle high resource utilization
  handleHighResourceUtilization() {
    console.log('📊 Handling high resource utilization...')
    
    // Scale up agents or redistribute load
    this.scaleUpResources()
  }

  // Handle low agent availability
  handleLowAgentAvailability() {
    console.log('🤖 Handling low agent availability...')
    
    // Create additional agents
    this.createAdditionalAgents()
  }

  // Apply optimization rules
  applyOptimizations() {
    const applicableRules = []
    
    // Check which optimization rules apply
    this.optimizationRules.forEach((rule, ruleName) => {
      if (rule.condition(this.realTimeMetrics)) {
        applicableRules.push({ name: ruleName, ...rule })
      }
    })
    
    // Sort by priority and apply
    applicableRules
      .sort((a, b) => this.getPriorityValue(b.priority) - this.getPriorityValue(a.priority))
      .forEach(rule => {
        this.executeOptimizationAction(rule)
      })
  }

  // Get priority value for sorting
  getPriorityValue(priority) {
    const values = { critical: 3, high: 2, medium: 1, low: 0 }
    return values[priority] || 0
  }

  // Execute optimization action
  executeOptimizationAction(rule) {
    console.log(`⚡ Executing optimization: ${rule.name}`)
    
    switch (rule.action) {
      case 'redistribute_tasks':
        this.redistributeTasks()
        break
      case 'scale_up_agents':
        this.scaleUpAgents()
        break
      case 'performance_intervention':
        this.performanceIntervention()
        break
      case 'optimize_collaboration':
        this.optimizeCollaboration()
        break
      case 'cleanup_idle_agents':
        this.cleanupIdleAgents()
        break
    }
  }

  // Optimization action implementations
  redistributeTasks() {
    console.log('🔄 Redistributing tasks...')
    // Implementation would redistribute tasks among agents
  }

  scaleUpAgents() {
    console.log('📈 Scaling up agents...')
    // Implementation would create additional agents
  }

  performanceIntervention() {
    console.log('🚨 Performance intervention...')
    // Implementation would reset or optimize underperforming components
  }

  optimizeCollaboration() {
    console.log('🤝 Optimizing collaboration...')
    // Implementation would optimize coordination protocols
  }

  cleanupIdleAgents() {
    console.log('🧹 Cleaning up idle agents...')
    // Implementation would remove unnecessary idle agents
  }

  // Additional optimization methods
  optimizeTaskDistribution() {
    console.log('📊 Optimizing task distribution...')
    // Implement task distribution optimization
  }

  optimizeCoordinationProtocols() {
    console.log('🔧 Optimizing coordination protocols...')
    // Implement coordination optimization
  }

  scaleUpResources() {
    console.log('📈 Scaling up resources...')
    // Implement resource scaling
  }

  createAdditionalAgents() {
    console.log('🤖 Creating additional agents...')
    // Implement agent creation
  }

  // Get comprehensive performance report
  getPerformanceReport(timeRange = '1h') {
    const report = {
      summary: {
        ...this.realTimeMetrics,
        reportTime: new Date().toISOString(),
        timeRange
      },
      trends: this.calculateTrends(timeRange),
      alerts: this.getRecentAlerts(timeRange),
      recommendations: this.generateRecommendations(),
      agentBreakdown: this.getAgentPerformanceBreakdown(),
      systemHealth: this.assessSystemHealth()
    }
    
    return report
  }

  // Calculate trends over time range
  calculateTrends(timeRange) {
    const cutoffTime = this.getTimeRangeCutoff(timeRange)
    const relevantHistory = this.performanceHistory.filter(h => 
      new Date(h.timestamp) >= cutoffTime
    )
    
    if (relevantHistory.length < 2) {
      return { insufficient_data: true }
    }
    
    return {
      successRate: this.calculateMetricTrend(relevantHistory, 'successRate'),
      responseTime: this.calculateMetricTrend(relevantHistory, 'averageResponseTime'),
      collaborationEfficiency: this.calculateMetricTrend(relevantHistory, 'collaborationEfficiency'),
      resourceUtilization: this.calculateMetricTrend(relevantHistory, 'resourceUtilization')
    }
  }

  // Get time range cutoff
  getTimeRangeCutoff(timeRange) {
    const now = new Date()
    const ranges = {
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000
    }
    
    const milliseconds = ranges[timeRange] || ranges['1h']
    return new Date(now.getTime() - milliseconds)
  }

  // Calculate metric trend
  calculateMetricTrend(history, metricName) {
    const values = history.map(h => h.metrics[metricName]).filter(v => v !== undefined)
    
    if (values.length < 2) return { trend: 'stable', change: 0 }
    
    const first = values[0]
    const last = values[values.length - 1]
    const change = last - first
    const percentChange = first !== 0 ? (change / first) * 100 : 0
    
    let trend = 'stable'
    if (Math.abs(percentChange) > 5) {
      trend = percentChange > 0 ? 'increasing' : 'decreasing'
    }
    
    return {
      trend,
      change: percentChange,
      first,
      last,
      average: values.reduce((sum, v) => sum + v, 0) / values.length
    }
  }

  // Get recent alerts
  getRecentAlerts(timeRange) {
    if (!this.alertHistory) return []
    
    const cutoffTime = this.getTimeRangeCutoff(timeRange)
    return this.alertHistory.filter(alert => 
      new Date(alert.timestamp) >= cutoffTime
    )
  }

  // Generate recommendations
  generateRecommendations() {
    const recommendations = []
    const metrics = this.realTimeMetrics
    
    // Success rate recommendations
    if (metrics.successRate < 0.8) {
      recommendations.push({
        type: 'performance',
        priority: 'high',
        title: 'Improve Success Rate',
        description: 'Success rate is below optimal. Consider agent training or task simplification.',
        actions: ['Review failed tasks', 'Optimize agent algorithms', 'Improve error handling']
      })
    }
    
    // Response time recommendations
    if (metrics.averageResponseTime > 10000) {
      recommendations.push({
        type: 'performance',
        priority: 'medium',
        title: 'Reduce Response Time',
        description: 'Response times are higher than target. Consider optimization.',
        actions: ['Optimize algorithms', 'Increase parallelization', 'Cache common results']
      })
    }
    
    // Collaboration recommendations
    if (metrics.collaborationEfficiency < 0.7) {
      recommendations.push({
        type: 'collaboration',
        priority: 'medium',
        title: 'Improve Collaboration',
        description: 'Collaboration efficiency could be improved.',
        actions: ['Optimize communication protocols', 'Improve task distribution', 'Enhance coordination']
      })
    }
    
    // Resource recommendations
    if (metrics.resourceUtilization > 0.9) {
      recommendations.push({
        type: 'resources',
        priority: 'high',
        title: 'Scale Resources',
        description: 'Resource utilization is very high. Consider scaling up.',
        actions: ['Add more agents', 'Optimize resource allocation', 'Implement load balancing']
      })
    }
    
    return recommendations
  }

  // Get agent performance breakdown
  getAgentPerformanceBreakdown() {
    const frameworkStats = agentFramework.getFrameworkStats()
    const breakdown = {}
    
    Object.entries(frameworkStats.agentTypeStats || {}).forEach(([type, stats]) => {
      breakdown[type] = {
        instances: stats.instances,
        activeInstances: stats.activeInstances,
        totalCreated: stats.totalCreated,
        performance: stats.averagePerformance || {},
        utilization: stats.activeInstances / Math.max(stats.instances, 1),
        efficiency: this.calculateAgentTypeEfficiency(stats)
      }
    })
    
    return breakdown
  }

  // Calculate agent type efficiency
  calculateAgentTypeEfficiency(stats) {
    const performance = stats.averagePerformance || {}
    const successRate = performance.successRate || 0
    const completionTime = performance.completionTime || 10000
    
    // Efficiency based on success rate and completion time
    const timeEfficiency = Math.max(0, 1 - (completionTime / 15000)) // 15 seconds baseline
    return (successRate + timeEfficiency) / 2
  }

  // Assess overall system health
  assessSystemHealth() {
    const metrics = this.realTimeMetrics
    let healthScore = 100
    const issues = []
    
    // Check success rate
    if (metrics.successRate < 0.5) {
      healthScore -= 30
      issues.push('Critical: Very low success rate')
    } else if (metrics.successRate < 0.7) {
      healthScore -= 15
      issues.push('Warning: Low success rate')
    }
    
    // Check response time
    if (metrics.averageResponseTime > 20000) {
      healthScore -= 20
      issues.push('Critical: Very high response time')
    } else if (metrics.averageResponseTime > 10000) {
      healthScore -= 10
      issues.push('Warning: High response time')
    }
    
    // Check collaboration efficiency
    if (metrics.collaborationEfficiency < 0.4) {
      healthScore -= 20
      issues.push('Critical: Very low collaboration efficiency')
    } else if (metrics.collaborationEfficiency < 0.6) {
      healthScore -= 10
      issues.push('Warning: Low collaboration efficiency')
    }
    
    // Check resource utilization
    if (metrics.resourceUtilization > 0.95) {
      healthScore -= 15
      issues.push('Critical: Resource overload')
    } else if (metrics.resourceUtilization > 0.85) {
      healthScore -= 8
      issues.push('Warning: High resource utilization')
    }
    
    let status = 'healthy'
    if (healthScore < 50) status = 'critical'
    else if (healthScore < 70) status = 'degraded'
    else if (healthScore < 85) status = 'warning'
    
    return {
      status,
      score: Math.max(0, healthScore),
      issues,
      lastCheck: new Date().toISOString()
    }
  }

  // Stop monitoring
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
      console.log('⏹️ Stopped performance monitoring')
    }
  }

  // Cleanup old data
  cleanup() {
    // Clean performance history
    const maxHistoryAge = 24 * 60 * 60 * 1000 // 24 hours
    const cutoffTime = new Date(Date.now() - maxHistoryAge)
    
    const originalSize = this.performanceHistory.length
    this.performanceHistory = this.performanceHistory.filter(h => 
      new Date(h.timestamp) >= cutoffTime
    )
    
    const removedCount = originalSize - this.performanceHistory.length
    if (removedCount > 0) {
      console.log(`🧹 Cleaned up ${removedCount} old performance records`)
    }
    
    // Clean alert history
    if (this.alertHistory) {
      const originalAlertSize = this.alertHistory.length
      this.alertHistory = this.alertHistory.filter(alert => 
        new Date(alert.timestamp) >= cutoffTime
      )
      
      const removedAlerts = originalAlertSize - this.alertHistory.length
      if (removedAlerts > 0) {
        console.log(`🧹 Cleaned up ${removedAlerts} old alerts`)
      }
    }
  }
}

// Create singleton instance
const agentPerformanceMonitoring = new AgentPerformanceMonitoring()
export default agentPerformanceMonitoring
