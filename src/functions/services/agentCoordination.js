// Agent Coordination System - Manages multi-agent collaboration and communication
import agentFramework from './agentFramework.js'
import specializedAgents from './specializedAgents.js'
import geminiService from './geminiService.js'

class AgentCoordinationSystem {
  constructor() {
    this.name = 'agent_coordination'
    this.version = '1.0.0'
    this.activeCoordinations = new Map()
    this.coordinationHistory = []
    this.performanceMetrics = {
      totalCoordinations: 0,
      successRate: 0,
      averageCompletionTime: 0,
      agentUtilization: {}
    }
  }

  // Initialize coordination system
  async initialize() {
    console.log('🤝 Initializing Agent Coordination System...')
    
    try {
      // Initialize dependencies
      await agentFramework.initialize()
      await specializedAgents.initialize()
      
      console.log('✅ Agent Coordination System initialized successfully')
      return true
    } catch (error) {
      console.error('❌ Failed to initialize Agent Coordination System:', error)
      return false
    }
  }

  // Coordinate multi-agent task execution
  async coordinateTask(task, requirements = {}) {
    console.log(`🎯 Coordinating multi-agent task: ${task.description || task}`)
    
    const coordinationId = this.generateCoordinationId()
    const startTime = Date.now()
    
    try {
      // Step 1: Analyze task and determine agent requirements
      const taskAnalysis = await this.analyzeTaskRequirements(task, requirements)
      
      // Step 2: Select and allocate agents
      const agentAllocation = await this.allocateAgents(taskAnalysis)
      
      // Step 3: Create coordination plan
      const coordinationPlan = this.createCoordinationPlan(taskAnalysis, agentAllocation)
      
      // Step 4: Execute coordinated task
      const executionResult = await this.executeCoordinatedTask(coordinationPlan, coordinationId)
      
      // Step 5: Synthesize results
      const finalResult = await this.synthesizeResults(executionResult, taskAnalysis)
      
      // Step 6: Update metrics and cleanup
      const completionTime = Date.now() - startTime
      this.updateCoordinationMetrics(coordinationId, finalResult, completionTime)
      
      return {
        success: true,
        coordinationId,
        task: taskAnalysis,
        allocation: agentAllocation,
        plan: coordinationPlan,
        execution: executionResult,
        result: finalResult,
        metrics: {
          completionTime,
          agentsUsed: agentAllocation.agents.length,
          successRate: finalResult.success ? 1 : 0
        }
      }
      
    } catch (error) {
      console.error(`❌ Coordination failed for task: ${task}`, error)
      
      const completionTime = Date.now() - startTime
      this.updateCoordinationMetrics(coordinationId, { success: false, error: error.message }, completionTime)
      
      return {
        success: false,
        coordinationId,
        error: error.message,
        metrics: { completionTime, agentsUsed: 0, successRate: 0 }
      }
    }
  }

  // Analyze task to determine agent requirements
  async analyzeTaskRequirements(task, requirements) {
    console.log('📊 Analyzing task requirements...')
    
    const taskDescription = typeof task === 'string' ? task : task.description || task.query
    
    // Use coordinator agent to analyze the task
    const coordinatorAgent = agentFramework.findBestAgentForTask('task_coordination')
    if (!coordinatorAgent) {
      // Create coordinator if none exists
      agentFramework.createAgent('coordinator')
    }
    
    const analysisTask = {
      type: 'task_coordination',
      description: taskDescription,
      requirements: {
        ...requirements,
        analysis_focus: 'agent_requirements'
      }
    }
    
    const analysisResult = await specializedAgents.executeTask('coordinator', analysisTask)
    
    return {
      originalTask: task,
      description: taskDescription,
      complexity: analysisResult.analysis?.complexity || 'medium',
      domains: analysisResult.analysis?.domains || ['general'],
      estimatedTime: analysisResult.analysis?.estimated_time || 10,
      priority: requirements.priority || 'medium',
      dependencies: analysisResult.analysis?.dependencies || [],
      requiredCapabilities: this.extractRequiredCapabilities(taskDescription),
      coordinationNeeded: this.assessCoordinationNeeds(taskDescription, requirements)
    }
  }

  // Extract required capabilities from task description
  extractRequiredCapabilities(description) {
    const capabilities = []
    const capabilityMap = {
      'research': ['search', 'find', 'investigate', 'study', 'research', 'gather'],
      'coding': ['code', 'program', 'implement', 'develop', 'debug', 'test'],
      'analysis': ['analyze', 'examine', 'evaluate', 'assess', 'compare', 'calculate'],
      'creative': ['create', 'design', 'write', 'generate', 'brainstorm', 'imagine'],
      'coordination': ['coordinate', 'manage', 'organize', 'plan', 'synthesize']
    }
    
    const descLower = description.toLowerCase()
    
    Object.entries(capabilityMap).forEach(([capability, keywords]) => {
      if (keywords.some(keyword => descLower.includes(keyword))) {
        capabilities.push(capability)
      }
    })
    
    return capabilities.length > 0 ? capabilities : ['general']
  }

  // Assess if coordination is needed
  assessCoordinationNeeds(description, requirements) {
    const coordinationIndicators = [
      'multiple', 'various', 'different', 'combine', 'integrate',
      'comprehensive', 'complete', 'thorough', 'detailed'
    ]
    
    const needsCoordination = coordinationIndicators.some(indicator => 
      description.toLowerCase().includes(indicator)
    )
    
    return {
      needed: needsCoordination || requirements.multiAgent === true,
      complexity: needsCoordination ? 'high' : 'low',
      reason: needsCoordination ? 'Multiple capabilities required' : 'Single agent sufficient'
    }
  }

  // Allocate agents based on task analysis
  async allocateAgents(taskAnalysis) {
    console.log('👥 Allocating agents for task...')
    
    const allocation = {
      agents: [],
      coordinator: null,
      allocation_strategy: 'capability_based',
      estimated_load: {}
    }
    
    // Allocate agents for each required capability
    for (const capability of taskAnalysis.requiredCapabilities) {
      const agent = agentFramework.findBestAgentForTask(capability)
      
      if (agent) {
        allocation.agents.push({
          agent: agent,
          capability: capability,
          role: this.defineAgentRole(capability, taskAnalysis),
          priority: this.calculateAgentPriority(capability, taskAnalysis)
        })
      } else {
        // Create new agent if none available
        const newAgent = agentFramework.createAgent(capability)
        allocation.agents.push({
          agent: newAgent,
          capability: capability,
          role: this.defineAgentRole(capability, taskAnalysis),
          priority: this.calculateAgentPriority(capability, taskAnalysis)
        })
      }
    }
    
    // Assign coordinator if needed
    if (taskAnalysis.coordinationNeeded.needed || allocation.agents.length > 1) {
      const coordinatorAgent = agentFramework.findBestAgentForTask('task_coordination') || 
                             agentFramework.createAgent('coordinator')
      
      allocation.coordinator = {
        agent: coordinatorAgent,
        role: 'coordination_and_synthesis',
        responsibilities: ['Manage workflow', 'Coordinate agents', 'Synthesize results']
      }
    }
    
    // Calculate estimated load for each agent
    allocation.agents.forEach(agentAlloc => {
      const agentId = agentAlloc.agent.id
      allocation.estimated_load[agentId] = this.estimateAgentLoad(agentAlloc, taskAnalysis)
    })
    
    return allocation
  }

  // Define role for agent based on capability
  defineAgentRole(capability, taskAnalysis) {
    const roles = {
      'research': 'Information gathering and verification',
      'coding': 'Implementation and development',
      'analysis': 'Data processing and insights',
      'creative': 'Content creation and ideation',
      'coordination': 'Workflow management and synthesis'
    }
    
    return roles[capability] || 'Task execution'
  }

  // Calculate agent priority based on task requirements
  calculateAgentPriority(capability, taskAnalysis) {
    // Primary capabilities get high priority
    if (taskAnalysis.requiredCapabilities.indexOf(capability) === 0) {
      return 'high'
    }
    
    // Critical capabilities for complex tasks
    if (taskAnalysis.complexity === 'complex' && ['analysis', 'coordination'].includes(capability)) {
      return 'high'
    }
    
    return 'medium'
  }

  // Estimate load for agent
  estimateAgentLoad(agentAllocation, taskAnalysis) {
    const baseLoad = taskAnalysis.estimatedTime || 10
    const priorityMultiplier = agentAllocation.priority === 'high' ? 1.2 : 1.0
    const complexityMultiplier = taskAnalysis.complexity === 'complex' ? 1.5 : 1.0
    
    return Math.round(baseLoad * priorityMultiplier * complexityMultiplier)
  }

  // Create coordination plan
  createCoordinationPlan(taskAnalysis, agentAllocation) {
    console.log('📋 Creating coordination plan...')
    
    const plan = {
      task: taskAnalysis,
      agents: agentAllocation.agents,
      coordinator: agentAllocation.coordinator,
      execution_strategy: this.determineExecutionStrategy(taskAnalysis, agentAllocation),
      communication_plan: this.createCommunicationPlan(agentAllocation),
      timeline: this.createExecutionTimeline(taskAnalysis, agentAllocation),
      success_criteria: this.defineSuccessCriteria(taskAnalysis),
      contingency_plans: this.createContingencyPlans(taskAnalysis, agentAllocation)
    }
    
    return plan
  }

  // Determine execution strategy
  determineExecutionStrategy(taskAnalysis, agentAllocation) {
    const agentCount = agentAllocation.agents.length
    const hasCoordinator = !!agentAllocation.coordinator
    
    if (agentCount === 1 && !hasCoordinator) {
      return {
        type: 'single_agent',
        description: 'Direct execution by single agent',
        coordination_level: 'none'
      }
    }
    
    if (agentCount <= 3 && hasCoordinator) {
      return {
        type: 'coordinated_parallel',
        description: 'Parallel execution with coordinator oversight',
        coordination_level: 'moderate'
      }
    }
    
    return {
      type: 'complex_coordination',
      description: 'Sequential and parallel phases with active coordination',
      coordination_level: 'high'
    }
  }

  // Create communication plan
  createCommunicationPlan(agentAllocation) {
    return {
      primary_channel: 'agent_framework_messaging',
      update_frequency: agentAllocation.agents.length > 2 ? 'high' : 'medium',
      status_reporting: {
        interval: '2_minutes',
        format: 'structured_update',
        recipients: agentAllocation.coordinator ? [agentAllocation.coordinator.agent.id] : 'all'
      },
      result_sharing: {
        method: 'immediate_broadcast',
        format: 'structured_result'
      },
      escalation_protocol: {
        timeout: '5_minutes',
        escalation_target: agentAllocation.coordinator?.agent.id || 'system'
      }
    }
  }

  // Create execution timeline
  createExecutionTimeline(taskAnalysis, agentAllocation) {
    const totalTime = taskAnalysis.estimatedTime
    const agentCount = agentAllocation.agents.length
    const hasCoordinator = !!agentAllocation.coordinator
    
    const timeline = []
    let currentTime = 0
    
    // Planning phase
    if (hasCoordinator) {
      timeline.push({
        phase: 'planning',
        start_time: currentTime,
        duration: Math.max(1, totalTime * 0.1),
        agents: [agentAllocation.coordinator.agent.id],
        description: 'Task planning and agent briefing'
      })
      currentTime += timeline[timeline.length - 1].duration
    }
    
    // Execution phase(s)
    if (agentCount === 1) {
      // Single agent execution
      timeline.push({
        phase: 'execution',
        start_time: currentTime,
        duration: totalTime * 0.8,
        agents: [agentAllocation.agents[0].agent.id],
        description: 'Task execution'
      })
      currentTime += timeline[timeline.length - 1].duration
    } else {
      // Multi-agent execution
      const executionTime = totalTime * (hasCoordinator ? 0.7 : 0.9)
      timeline.push({
        phase: 'parallel_execution',
        start_time: currentTime,
        duration: executionTime,
        agents: agentAllocation.agents.map(a => a.agent.id),
        description: 'Parallel task execution by multiple agents'
      })
      currentTime += timeline[timeline.length - 1].duration
    }
    
    // Synthesis phase
    if (hasCoordinator && agentCount > 1) {
      timeline.push({
        phase: 'synthesis',
        start_time: currentTime,
        duration: totalTime * 0.2,
        agents: [agentAllocation.coordinator.agent.id],
        description: 'Result synthesis and finalization'
      })
    }
    
    return timeline
  }

  // Define success criteria
  defineSuccessCriteria(taskAnalysis) {
    const criteria = [
      'Task completed successfully',
      'All required capabilities utilized',
      'Quality output delivered'
    ]
    
    if (taskAnalysis.complexity === 'complex') {
      criteria.push('Effective agent coordination', 'Comprehensive result synthesis')
    }
    
    if (taskAnalysis.priority === 'high') {
      criteria.push('Timely completion within estimated timeframe')
    }
    
    return criteria
  }

  // Create contingency plans
  createContingencyPlans(taskAnalysis, agentAllocation) {
    return [
      {
        scenario: 'Agent failure or timeout',
        response: 'Reassign task to backup agent or redistribute workload',
        trigger: 'Agent non-responsive for > 5 minutes'
      },
      {
        scenario: 'Poor quality intermediate results',
        response: 'Request revision or escalate to coordinator',
        trigger: 'Quality score < 60%'
      },
      {
        scenario: 'Communication breakdown',
        response: 'Reset communication channels and re-establish coordination',
        trigger: 'No status updates for > 3 minutes'
      }
    ]
  }

  // Execute coordinated task
  async executeCoordinatedTask(coordinationPlan, coordinationId) {
    console.log(`🚀 Executing coordinated task: ${coordinationId}`)
    
    // Store active coordination
    this.activeCoordinations.set(coordinationId, {
      plan: coordinationPlan,
      status: 'executing',
      startTime: Date.now(),
      results: new Map()
    })
    
    const execution = {
      phases: [],
      agent_results: new Map(),
      communication_log: [],
      success: true,
      errors: []
    }
    
    try {
      // Execute each phase in the timeline
      for (const phase of coordinationPlan.timeline) {
        console.log(`📍 Executing phase: ${phase.phase}`)
        
        const phaseResult = await this.executePhase(phase, coordinationPlan, coordinationId)
        execution.phases.push(phaseResult)
        
        // Collect agent results
        if (phaseResult.agent_results) {
          phaseResult.agent_results.forEach((result, agentId) => {
            execution.agent_results.set(agentId, result)
          })
        }
        
        // Check for phase failure
        if (!phaseResult.success) {
          execution.success = false
          execution.errors.push(`Phase ${phase.phase} failed: ${phaseResult.error}`)
          
          // Apply contingency plan if available
          const contingency = this.applyContingencyPlan(phaseResult, coordinationPlan)
          if (contingency.applied) {
            console.log(`🔄 Applied contingency: ${contingency.action}`)
            execution.communication_log.push({
              type: 'contingency',
              action: contingency.action,
              timestamp: new Date().toISOString()
            })
          }
        }
      }
      
    } catch (error) {
      console.error(`❌ Execution failed for coordination ${coordinationId}:`, error)
      execution.success = false
      execution.errors.push(error.message)
    }
    
    // Update coordination status
    const coordination = this.activeCoordinations.get(coordinationId)
    if (coordination) {
      coordination.status = execution.success ? 'completed' : 'failed'
      coordination.endTime = Date.now()
      coordination.results = execution.agent_results
    }
    
    return execution
  }

  // Execute individual phase
  async executePhase(phase, coordinationPlan, coordinationId) {
    const phaseResult = {
      phase: phase.phase,
      success: true,
      agent_results: new Map(),
      duration: 0,
      error: null
    }
    
    const startTime = Date.now()
    
    try {
      // Execute tasks for each agent in this phase
      const agentTasks = []
      
      for (const agentId of phase.agents) {
        const agentAllocation = coordinationPlan.agents.find(a => a.agent.id === agentId) ||
                              (coordinationPlan.coordinator?.agent.id === agentId ? coordinationPlan.coordinator : null)
        
        if (agentAllocation) {
          const task = this.createAgentTask(phase, agentAllocation, coordinationPlan)
          agentTasks.push(this.executeAgentTask(agentAllocation.agent, task))
        }
      }
      
      // Wait for all agent tasks to complete
      const results = await Promise.allSettled(agentTasks)
      
      // Process results
      results.forEach((result, index) => {
        const agentId = phase.agents[index]
        
        if (result.status === 'fulfilled') {
          phaseResult.agent_results.set(agentId, result.value)
        } else {
          phaseResult.success = false
          phaseResult.error = result.reason?.message || 'Agent task failed'
          console.error(`❌ Agent ${agentId} failed in phase ${phase.phase}:`, result.reason)
        }
      })
      
    } catch (error) {
      phaseResult.success = false
      phaseResult.error = error.message
      console.error(`❌ Phase ${phase.phase} execution failed:`, error)
    }
    
    phaseResult.duration = Date.now() - startTime
    return phaseResult
  }

  // Create task for specific agent in phase
  createAgentTask(phase, agentAllocation, coordinationPlan) {
    const baseTask = coordinationPlan.task
    
    return {
      type: this.mapCapabilityToTaskType(agentAllocation.capability),
      description: this.adaptTaskForAgent(baseTask.description, agentAllocation),
      requirements: {
        ...baseTask,
        phase: phase.phase,
        role: agentAllocation.role,
        priority: agentAllocation.priority,
        coordination_context: {
          coordination_id: coordinationPlan.coordination_id,
          other_agents: coordinationPlan.agents.filter(a => a.agent.id !== agentAllocation.agent.id),
          timeline: coordinationPlan.timeline
        }
      }
    }
  }

  // Map capability to task type
  mapCapabilityToTaskType(capability) {
    const mapping = {
      'research': 'research_synthesis',
      'coding': 'code_generation',
      'analysis': 'data_analysis',
      'creative': 'content_creation',
      'coordination': 'task_coordination'
    }
    
    return mapping[capability] || 'general_processing'
  }

  // Adapt task description for specific agent
  adaptTaskForAgent(description, agentAllocation) {
    const capability = agentAllocation.capability
    const role = agentAllocation.role
    
    return `${role}: ${description} (Focus: ${capability} capabilities)`
  }

  // Execute task for specific agent
  async executeAgentTask(agent, task) {
    console.log(`🤖 Executing task for agent ${agent.id} (${agent.type})`)
    
    try {
      const result = await specializedAgents.executeTask(agent.type, task)
      
      // Update agent performance
      agentFramework.updateAgentPerformance(agent.id, {
        success: result.success,
        completionTime: result.completionTime,
        collaborationRating: 0.8 // Default collaboration rating
      })
      
      return result
    } catch (error) {
      console.error(`❌ Agent ${agent.id} task execution failed:`, error)
      
      // Update agent performance with failure
      agentFramework.updateAgentPerformance(agent.id, {
        success: false,
        completionTime: 0,
        collaborationRating: 0.3
      })
      
      throw error
    }
  }

  // Apply contingency plan
  applyContingencyPlan(phaseResult, coordinationPlan) {
    const contingencies = coordinationPlan.contingency_plans || []
    
    for (const contingency of contingencies) {
      if (this.shouldApplyContingency(contingency, phaseResult)) {
        console.log(`🔄 Applying contingency: ${contingency.response}`)
        
        // Simple contingency application (in real implementation, this would be more sophisticated)
        return {
          applied: true,
          action: contingency.response,
          scenario: contingency.scenario
        }
      }
    }
    
    return { applied: false }
  }

  // Check if contingency should be applied
  shouldApplyContingency(contingency, phaseResult) {
    // Simple trigger checking (in real implementation, this would be more sophisticated)
    if (contingency.scenario.includes('failure') && !phaseResult.success) {
      return true
    }
    
    if (contingency.scenario.includes('timeout') && phaseResult.duration > 300000) { // 5 minutes
      return true
    }
    
    return false
  }

  // Synthesize results from multiple agents
  async synthesizeResults(executionResult, taskAnalysis) {
    console.log('🔄 Synthesizing results from multiple agents...')
    
    if (executionResult.agent_results.size === 0) {
      return {
        success: false,
        content: 'No results to synthesize',
        confidence: 0,
        synthesis_quality: 0
      }
    }
    
    if (executionResult.agent_results.size === 1) {
      // Single agent result - no synthesis needed
      const singleResult = Array.from(executionResult.agent_results.values())[0]
      return {
        success: singleResult.success,
        content: singleResult.content || singleResult.result || 'Task completed',
        confidence: singleResult.confidence || 75,
        synthesis_quality: 90,
        source: 'single_agent'
      }
    }
    
    // Multi-agent synthesis
    try {
      const coordinatorTask = {
        type: 'result_synthesis',
        description: `Synthesize results from multiple agents for: ${taskAnalysis.description}`,
        requirements: {
          agent_results: Array.from(executionResult.agent_results.entries()),
          original_task: taskAnalysis,
          synthesis_focus: 'comprehensive_integration'
        }
      }
      
      const synthesisResult = await specializedAgents.executeTask('coordinator', coordinatorTask)
      
      return {
        success: synthesisResult.success,
        content: synthesisResult.synthesis?.content || synthesisResult.content || 'Synthesis completed',
        confidence: synthesisResult.synthesis?.confidence || 75,
        synthesis_quality: synthesisResult.quality_score || 80,
        source: 'multi_agent_synthesis',
        individual_results: Array.from(executionResult.agent_results.entries())
      }
      
    } catch (error) {
      console.error('❌ Result synthesis failed:', error)
      
      // Fallback: simple concatenation
      const combinedContent = Array.from(executionResult.agent_results.values())
        .map(result => result.content || result.result || 'No content')
        .join('\n\n---\n\n')
      
      return {
        success: true,
        content: combinedContent,
        confidence: 60,
        synthesis_quality: 40,
        source: 'fallback_concatenation',
        note: 'Synthesis failed, using simple combination'
      }
    }
  }

  // Update coordination metrics
  updateCoordinationMetrics(coordinationId, result, completionTime) {
    this.performanceMetrics.totalCoordinations++
    
    // Update success rate
    const successCount = this.coordinationHistory.filter(h => h.success).length + (result.success ? 1 : 0)
    this.performanceMetrics.successRate = successCount / this.performanceMetrics.totalCoordinations
    
    // Update average completion time
    const totalTime = this.coordinationHistory.reduce((sum, h) => sum + h.completionTime, 0) + completionTime
    this.performanceMetrics.averageCompletionTime = totalTime / this.performanceMetrics.totalCoordinations
    
    // Store in history
    this.coordinationHistory.push({
      coordinationId,
      success: result.success,
      completionTime,
      timestamp: new Date().toISOString(),
      result: result
    })
    
    // Clean up active coordination
    this.activeCoordinations.delete(coordinationId)
    
    console.log(`📊 Updated coordination metrics - Success rate: ${Math.round(this.performanceMetrics.successRate * 100)}%`)
  }

  // Generate coordination ID
  generateCoordinationId() {
    return `coord_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`
  }

  // Get coordination statistics
  getCoordinationStats() {
    return {
      ...this.performanceMetrics,
      activeCoordinations: this.activeCoordinations.size,
      recentHistory: this.coordinationHistory.slice(-10),
      agentFrameworkStats: agentFramework.getFrameworkStats()
    }
  }

  // Cleanup old coordination data
  cleanup() {
    const maxHistorySize = 100
    if (this.coordinationHistory.length > maxHistorySize) {
      this.coordinationHistory = this.coordinationHistory.slice(-maxHistorySize)
    }
    
    // Cleanup agent framework
    agentFramework.cleanupCollaborations()
    
    console.log('🧹 Coordination system cleanup completed')
  }
}

// Create singleton instance
const agentCoordinationSystem = new AgentCoordinationSystem()
export default agentCoordinationSystem
