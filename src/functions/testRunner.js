// Test Runner - Execute multi-agent system tests
import multiAgentSystemTests, { runMultiAgentTests, runTestSuite, getTestCoverage } from './tests/multiAgentSystemTests.js'

// Main test execution function
export async function runAllTests() {
  console.log('🚀 Starting Multi-Agent System Test Suite...')
  console.log('=' .repeat(60))
  
  try {
    const report = await runMultiAgentTests()
    
    console.log('\n📊 TEST RESULTS SUMMARY')
    console.log('=' .repeat(60))
    console.log(`Total Tests: ${report.summary.totalTests}`)
    console.log(`✅ Passed: ${report.summary.totalPassed}`)
    console.log(`❌ Failed: ${report.summary.totalFailed}`)
    console.log(`📈 Success Rate: ${report.summary.successRate}%`)
    console.log(`⏱️ Total Duration: ${Math.round(report.summary.totalDuration / 1000)}s`)
    
    console.log('\n📋 TEST SUITE BREAKDOWN')
    console.log('=' .repeat(60))
    report.suites.forEach(suite => {
      const status = suite.failed === 0 ? '✅' : '❌'
      console.log(`${status} ${suite.name}: ${suite.passed}/${suite.passed + suite.failed} (${suite.successRate}%) - ${Math.round(suite.duration / 1000)}s`)
    })
    
    if (report.failedTests.length > 0) {
      console.log('\n❌ FAILED TESTS')
      console.log('=' .repeat(60))
      report.failedTests.forEach(test => {
        console.log(`${test.suite} > ${test.test}: ${test.error}`)
      })
    }
    
    console.log('\n💡 RECOMMENDATIONS')
    console.log('=' .repeat(60))
    report.recommendations.forEach(rec => {
      const icon = rec.type === 'success' ? '✅' : rec.type === 'critical' ? '🚨' : '⚠️'
      console.log(`${icon} ${rec.message}`)
    })
    
    return report
    
  } catch (error) {
    console.error('❌ Test execution failed:', error)
    return { success: false, error: error.message }
  }
}

// Run specific test suite
export async function runSpecificTestSuite(suiteName) {
  console.log(`🧪 Running ${suiteName} test suite...`)
  
  try {
    const result = await runTestSuite(suiteName)
    
    console.log(`\n📊 ${suiteName.toUpperCase()} RESULTS`)
    console.log('=' .repeat(40))
    console.log(`✅ Passed: ${result.passed}`)
    console.log(`❌ Failed: ${result.failed}`)
    console.log(`⏱️ Duration: ${Math.round(result.duration / 1000)}s`)
    
    if (result.failed > 0) {
      console.log('\n❌ Failed Tests:')
      result.tests.filter(t => !t.success).forEach(test => {
        console.log(`  - ${test.name}: ${test.error}`)
      })
    }
    
    return result
    
  } catch (error) {
    console.error(`❌ ${suiteName} test suite failed:`, error)
    return { success: false, error: error.message }
  }
}

// Show test coverage
export function showTestCoverage() {
  const coverage = getTestCoverage()
  
  console.log('📊 TEST COVERAGE REPORT')
  console.log('=' .repeat(50))
  console.log(`Total Tests: ${coverage.totalTests}`)
  
  console.log('\n🧩 Component Coverage:')
  Object.entries(coverage.components).forEach(([component, testCount]) => {
    console.log(`  ${component}: ${testCount} tests`)
  })
  
  console.log('\n🔍 Test Types:')
  Object.entries(coverage.testTypes).forEach(([type, count]) => {
    console.log(`  ${type}: ${count} tests`)
  })
  
  return coverage
}

// Quick smoke test
export async function runSmokeTest() {
  console.log('💨 Running Quick Smoke Test...')
  
  try {
    // Test basic agent creation
    console.log('  🤖 Testing agent creation...')
    const agentResult = await runTestSuite('agent_framework')
    
    // Test basic coordination
    console.log('  🤝 Testing coordination...')
    const coordResult = await runTestSuite('coordination_system')
    
    // Test communication
    console.log('  📡 Testing communication...')
    const commResult = await runTestSuite('communication_protocol')
    
    const allPassed = agentResult.failed === 0 && coordResult.failed === 0 && commResult.failed === 0
    
    console.log(`\n💨 SMOKE TEST ${allPassed ? 'PASSED' : 'FAILED'}`)
    console.log(`Agent Framework: ${agentResult.failed === 0 ? '✅' : '❌'}`)
    console.log(`Coordination: ${coordResult.failed === 0 ? '✅' : '❌'}`)
    console.log(`Communication: ${commResult.failed === 0 ? '✅' : '❌'}`)
    
    return allPassed
    
  } catch (error) {
    console.error('❌ Smoke test failed:', error)
    return false
  }
}

// Interactive test menu
export async function interactiveTestMenu() {
  console.log('🎮 INTERACTIVE TEST MENU')
  console.log('=' .repeat(40))
  console.log('1. Run All Tests')
  console.log('2. Run Smoke Test')
  console.log('3. Test Agent Framework')
  console.log('4. Test Specialized Agents')
  console.log('5. Test Coordination System')
  console.log('6. Test Communication Protocol')
  console.log('7. Test Task Decomposition')
  console.log('8. Test Performance Monitoring')
  console.log('9. Test Result Synthesis')
  console.log('10. Test Integration')
  console.log('11. Show Test Coverage')
  console.log('0. Exit')
  
  // Note: In a real implementation, you'd use readline or similar for input
  // For now, we'll just run all tests
  console.log('\n🚀 Running all tests...')
  return await runAllTests()
}

// Performance benchmark
export async function runPerformanceBenchmark() {
  console.log('⚡ PERFORMANCE BENCHMARK')
  console.log('=' .repeat(40))
  
  const startTime = Date.now()
  
  try {
    // Run performance-focused tests
    const loadTest = await runTestSuite('integration')
    const endTime = Date.now()
    
    const totalTime = endTime - startTime
    const avgTestTime = totalTime / (loadTest.tests?.length || 1)
    
    console.log(`\n⚡ BENCHMARK RESULTS`)
    console.log(`Total Time: ${Math.round(totalTime / 1000)}s`)
    console.log(`Average Test Time: ${Math.round(avgTestTime)}ms`)
    console.log(`Tests Passed: ${loadTest.passed}/${loadTest.passed + loadTest.failed}`)
    
    // Performance thresholds
    const performanceGrade = totalTime < 30000 ? 'A' : totalTime < 60000 ? 'B' : 'C'
    console.log(`Performance Grade: ${performanceGrade}`)
    
    return {
      totalTime,
      avgTestTime,
      grade: performanceGrade,
      passed: loadTest.failed === 0
    }
    
  } catch (error) {
    console.error('❌ Performance benchmark failed:', error)
    return { success: false, error: error.message }
  }
}

// Export all test functions
export default {
  runAllTests,
  runSpecificTestSuite,
  showTestCoverage,
  runSmokeTest,
  interactiveTestMenu,
  runPerformanceBenchmark
}
