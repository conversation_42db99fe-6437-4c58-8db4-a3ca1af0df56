// Multi-Agent Dashboard - Complete dashboard for multi-agent system management
import React, { useState, useCallback } from 'react'
import MultiAgentVisualization from './MultiAgentVisualization.jsx'
import useMultiAgentData from '../hooks/useMultiAgentData.js'
import './MultiAgentDashboard.css'

const MultiAgentDashboard = ({ 
  onTaskSubmit = () => {},
  onAgentCreate = () => {},
  showAdvancedControls = true 
}) => {
  const {
    agentData,
    collaborationData,
    performanceData,
    loading,
    error,
    lastUpdated,
    fetchAllData,
    createAgent,
    startCollaboration,
    getActiveAgents,
    totalAgents,
    activeAgents,
    activeCollaborations,
    systemHealth
  } = useMultiAgentData({
    refreshInterval: 5000,
    enableRealTimeUpdates: true,
    includePerformanceData: true,
    includeCollaborationData: true
  })

  const [selectedAgents, setSelectedAgents] = useState([])
  const [taskInput, setTaskInput] = useState('')
  const [showCreateAgent, setShowCreateAgent] = useState(false)
  const [newAgentType, setNewAgentType] = useState('research')

  // Handle agent selection
  const handleAgentSelect = useCallback((agent) => {
    setSelectedAgents(prev => {
      const isSelected = prev.some(a => a.id === agent.id)
      if (isSelected) {
        return prev.filter(a => a.id !== agent.id)
      } else {
        return [...prev, agent]
      }
    })
  }, [])

  // Handle task submission
  const handleTaskSubmit = useCallback(async (e) => {
    e.preventDefault()
    if (!taskInput.trim()) return

    try {
      let result
      
      if (selectedAgents.length > 1) {
        // Multi-agent collaboration
        result = await startCollaboration(
          selectedAgents.map(a => a.id),
          taskInput
        )
      } else {
        // Single agent or auto-assign
        result = await onTaskSubmit(taskInput, selectedAgents[0]?.id)
      }

      if (result) {
        setTaskInput('')
        setSelectedAgents([])
        console.log('✅ Task submitted successfully')
      }
    } catch (err) {
      console.error('❌ Failed to submit task:', err)
    }
  }, [taskInput, selectedAgents, startCollaboration, onTaskSubmit])

  // Handle agent creation
  const handleCreateAgent = useCallback(async () => {
    try {
      const newAgent = await createAgent(newAgentType)
      if (newAgent) {
        setShowCreateAgent(false)
        onAgentCreate(newAgent)
        console.log(`✅ Created new ${newAgentType} agent`)
      }
    } catch (err) {
      console.error(`❌ Failed to create ${newAgentType} agent:`, err)
    }
  }, [newAgentType, createAgent, onAgentCreate])

  // Get system status color
  const getSystemStatusColor = (status) => {
    const colors = {
      'healthy': '#10b981',
      'warning': '#f59e0b',
      'degraded': '#ef4444',
      'critical': '#dc2626',
      'unknown': '#6b7280'
    }
    return colors[status] || colors.unknown
  }

  // Render system status
  const renderSystemStatus = () => (
    <div className="system-status">
      <div className="status-header">
        <h3>System Status</h3>
        <div 
          className="status-indicator"
          style={{ backgroundColor: getSystemStatusColor(systemHealth) }}
        />
        <span className="status-text">{systemHealth}</span>
      </div>
      
      <div className="status-metrics">
        <div className="status-metric">
          <span className="metric-label">Total Agents</span>
          <span className="metric-value">{totalAgents}</span>
        </div>
        <div className="status-metric">
          <span className="metric-label">Active</span>
          <span className="metric-value">{activeAgents}</span>
        </div>
        <div className="status-metric">
          <span className="metric-label">Collaborations</span>
          <span className="metric-value">{activeCollaborations}</span>
        </div>
        <div className="status-metric">
          <span className="metric-label">Success Rate</span>
          <span className="metric-value">
            {Math.round((performanceData.successRate || 0) * 100)}%
          </span>
        </div>
      </div>

      {lastUpdated && (
        <div className="last-updated">
          Last updated: {lastUpdated.toLocaleTimeString()}
        </div>
      )}
    </div>
  )

  // Render task submission form
  const renderTaskForm = () => (
    <div className="task-form">
      <h3>Submit Task</h3>
      <form onSubmit={handleTaskSubmit}>
        <div className="form-group">
          <label htmlFor="task-input">Task Description</label>
          <textarea
            id="task-input"
            value={taskInput}
            onChange={(e) => setTaskInput(e.target.value)}
            placeholder="Describe the task you want the agents to work on..."
            rows={3}
            required
          />
        </div>

        {selectedAgents.length > 0 && (
          <div className="selected-agents">
            <label>Selected Agents ({selectedAgents.length})</label>
            <div className="agent-chips">
              {selectedAgents.map(agent => (
                <div key={agent.id} className="agent-chip">
                  <span className="agent-chip-icon">
                    {agent.type === 'research' ? '🔍' :
                     agent.type === 'coding' ? '💻' :
                     agent.type === 'analysis' ? '📊' :
                     agent.type === 'creative' ? '🎨' :
                     agent.type === 'coordinator' ? '🎯' : '🤖'}
                  </span>
                  <span className="agent-chip-name">{agent.type}</span>
                  <button
                    type="button"
                    className="agent-chip-remove"
                    onClick={() => handleAgentSelect(agent)}
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="form-actions">
          <button 
            type="submit" 
            className="submit-button"
            disabled={!taskInput.trim()}
          >
            {selectedAgents.length > 1 ? 'Start Collaboration' : 'Submit Task'}
          </button>
          
          {selectedAgents.length > 0 && (
            <button
              type="button"
              className="clear-selection-button"
              onClick={() => setSelectedAgents([])}
            >
              Clear Selection
            </button>
          )}
        </div>
      </form>
    </div>
  )

  // Render agent creation form
  const renderCreateAgentForm = () => (
    <div className="create-agent-form">
      <h3>Create New Agent</h3>
      <div className="form-group">
        <label htmlFor="agent-type">Agent Type</label>
        <select
          id="agent-type"
          value={newAgentType}
          onChange={(e) => setNewAgentType(e.target.value)}
        >
          <option value="research">🔍 Research Agent</option>
          <option value="coding">💻 Coding Agent</option>
          <option value="analysis">📊 Analysis Agent</option>
          <option value="creative">🎨 Creative Agent</option>
          <option value="coordinator">🎯 Coordinator Agent</option>
        </select>
      </div>
      
      <div className="form-actions">
        <button 
          type="button" 
          className="create-button"
          onClick={handleCreateAgent}
        >
          Create Agent
        </button>
        <button
          type="button"
          className="cancel-button"
          onClick={() => setShowCreateAgent(false)}
        >
          Cancel
        </button>
      </div>
    </div>
  )

  // Render quick actions
  const renderQuickActions = () => (
    <div className="quick-actions">
      <h3>Quick Actions</h3>
      <div className="action-buttons">
        <button
          className="action-button"
          onClick={() => setShowCreateAgent(true)}
          disabled={showCreateAgent}
        >
          <span className="action-icon">➕</span>
          Create Agent
        </button>
        
        <button
          className="action-button"
          onClick={fetchAllData}
          disabled={loading}
        >
          <span className="action-icon">🔄</span>
          Refresh Data
        </button>
        
        <button
          className="action-button"
          onClick={() => setSelectedAgents(getActiveAgents())}
        >
          <span className="action-icon">🎯</span>
          Select Active
        </button>
        
        <button
          className="action-button"
          onClick={() => setSelectedAgents([])}
          disabled={selectedAgents.length === 0}
        >
          <span className="action-icon">🗑️</span>
          Clear Selection
        </button>
      </div>
    </div>
  )

  // Render performance summary
  const renderPerformanceSummary = () => (
    <div className="performance-summary">
      <h3>Performance Summary</h3>
      <div className="performance-cards">
        <div className="performance-card">
          <div className="card-header">
            <span className="card-title">Response Time</span>
            <span className="card-icon">⚡</span>
          </div>
          <div className="card-value">
            {Math.round((performanceData.averageResponseTime || 0) / 1000)}s
          </div>
          <div className="card-trend">
            {performanceData.trends?.responseTime === 'improving' ? '📈 Improving' :
             performanceData.trends?.responseTime === 'declining' ? '📉 Declining' : '➡️ Stable'}
          </div>
        </div>

        <div className="performance-card">
          <div className="card-header">
            <span className="card-title">Collaboration</span>
            <span className="card-icon">🤝</span>
          </div>
          <div className="card-value">
            {Math.round((performanceData.collaborationEfficiency || 0) * 100)}%
          </div>
          <div className="card-trend">
            {performanceData.trends?.collaboration === 'improving' ? '📈 Improving' :
             performanceData.trends?.collaboration === 'declining' ? '📉 Declining' : '➡️ Stable'}
          </div>
        </div>

        <div className="performance-card">
          <div className="card-header">
            <span className="card-title">Quality Score</span>
            <span className="card-icon">⭐</span>
          </div>
          <div className="card-value">
            {Math.round(collaborationData.synthesisQuality || 0)}
          </div>
          <div className="card-trend">
            High Quality
          </div>
        </div>
      </div>
    </div>
  )

  if (loading) {
    return (
      <div className="multi-agent-dashboard loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Initializing Multi-Agent System...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="multi-agent-dashboard error">
        <div className="error-message">
          <h3>❌ System Error</h3>
          <p>{error}</p>
          <button onClick={fetchAllData} className="retry-button">
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="multi-agent-dashboard">
      <div className="dashboard-header">
        <h1>Multi-Agent Collaboration System</h1>
        <div className="header-info">
          <span className="agent-count">{totalAgents} Agents</span>
          <span className="active-count">{activeAgents} Active</span>
          <span className="collaboration-count">{activeCollaborations} Collaborating</span>
        </div>
      </div>

      <div className="dashboard-content">
        <div className="dashboard-sidebar">
          {renderSystemStatus()}
          {renderTaskForm()}
          {showCreateAgent && renderCreateAgentForm()}
          {showAdvancedControls && renderQuickActions()}
          {renderPerformanceSummary()}
        </div>

        <div className="dashboard-main">
          <MultiAgentVisualization
            agentData={agentData}
            collaborationData={collaborationData}
            performanceData={performanceData}
            onAgentSelect={handleAgentSelect}
            showPerformanceMetrics={true}
            showCollaborationFlow={true}
          />
        </div>
      </div>

      {performanceData.alerts && performanceData.alerts.length > 0 && (
        <div className="dashboard-alerts">
          <h3>System Alerts</h3>
          {performanceData.alerts.slice(0, 3).map((alert, index) => (
            <div key={index} className={`dashboard-alert alert-${alert.severity}`}>
              <span className="alert-icon">
                {alert.severity === 'critical' ? '🚨' : 
                 alert.severity === 'warning' ? '⚠️' : 'ℹ️'}
              </span>
              <div className="alert-content">
                <span className="alert-message">{alert.message}</span>
                <span className="alert-time">
                  {new Date(alert.timestamp).toLocaleTimeString()}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default MultiAgentDashboard
