import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import {
  Brain,
  TrendingUp,
  TrendingDown,
  Minus,
  Star,
  Target,
  Lightbulb,
  BarChart3,
  RefreshCw,
  Trash2,
  CheckCircle,
  AlertCircle,
  Info,
  Activity
} from 'lucide-react'
import learningService from '../services/learningService'
import LearningInsights from './LearningInsights'

const LearningDashboard = ({ isVisible = false, onClose }) => {
  const [learningStats, setLearningStats] = useState(null)
  const [performanceAnalytics, setPerformanceAnalytics] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [activeTab, setActiveTab] = useState('overview') // overview, analytics, recommendations
  const [showInsights, setShowInsights] = useState(false)

  // Load learning statistics
  const loadLearningStats = async () => {
    try {
      setIsLoading(true)
      const stats = learningService.getLearningStats()
      const analytics = learningService.getPerformanceAnalytics()
      setLearningStats(stats)
      setPerformanceAnalytics(analytics)
      console.log('📊 Learning stats loaded:', stats)
      console.log('📈 Performance analytics loaded:', analytics)
    } catch (error) {
      console.error('❌ Failed to load learning stats:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Refresh learning data
  const handleRefresh = async () => {
    setRefreshing(true)
    await loadLearningStats()
    setRefreshing(false)
  }

  // Clear learning data
  const handleClearData = async () => {
    if (window.confirm('Are you sure you want to clear all learning data? This cannot be undone.')) {
      learningService.clearLearningData()
      await loadLearningStats()
    }
  }

  // Load data on component mount
  useEffect(() => {
    if (isVisible) {
      loadLearningStats()
    }
  }, [isVisible])

  if (!isVisible) return null

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-400" />
      case 'stable': return <Minus className="h-4 w-4 text-yellow-400" />
      default: return <Info className="h-4 w-4 text-gray-400" />
    }
  }

  const getTrendColor = (trend) => {
    switch (trend) {
      case 'improving': return 'text-green-300'
      case 'declining': return 'text-red-300'
      case 'stable': return 'text-yellow-300'
      default: return 'text-gray-300'
    }
  }

  const getRatingColor = (rating) => {
    if (rating >= 4) return 'text-green-400'
    if (rating >= 3) return 'text-yellow-400'
    return 'text-red-400'
  }

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
        <Card className="bg-gray-900 border-gray-700 w-96">
          <CardContent className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-400 mx-auto mb-4"></div>
            <p className="text-gray-300">Loading learning data...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 rounded-lg border border-gray-700 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <Brain className="h-6 w-6 text-purple-400" />
            <div>
              <h2 className="text-xl font-bold text-white">Learning Dashboard</h2>
              <p className="text-gray-400 text-sm">AGI adaptation and improvement metrics</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setShowInsights(true)}
              className="text-blue-400 border-blue-400 hover:bg-blue-500/20"
            >
              <Activity className="h-4 w-4 mr-1" />
              Insights
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleRefresh}
              disabled={refreshing}
              className="text-gray-400 hover:text-gray-300"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleClearData}
              className="text-red-400 hover:text-red-300"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-300"
            >
              ✕
            </Button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-gray-700">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'analytics', label: 'Analytics', icon: TrendingUp },
            { id: 'recommendations', label: 'Recommendations', icon: Lightbulb }
          ].map(tab => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-6 py-3 text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'text-purple-400 border-b-2 border-purple-400 bg-purple-500/10'
                    : 'text-gray-400 hover:text-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                {tab.label}
              </button>
            )
          })}
        </div>

        <div className="p-6 space-y-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-400">
                  {learningStats?.totalFeedback || 0}
                </div>
                <div className="text-sm text-gray-400">Total Feedback</div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-4 text-center">
                <div className={`text-2xl font-bold ${getRatingColor(learningStats?.averageRating || 0)}`}>
                  {learningStats?.averageRating || 0}/5
                </div>
                <div className="text-sm text-gray-400">Average Rating</div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center gap-2">
                  {getTrendIcon(learningStats?.improvementTrend)}
                  <span className={`text-lg font-bold ${getTrendColor(learningStats?.improvementTrend)}`}>
                    {learningStats?.improvementTrend || 'Unknown'}
                  </span>
                </div>
                <div className="text-sm text-gray-400">Trend</div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-400">
                  {learningStats?.activeAdaptations || 0}
                </div>
                <div className="text-sm text-gray-400">Active Adaptations</div>
              </CardContent>
            </Card>
          </div>

          {/* Top Adaptations */}
          {learningStats?.topAdaptations && learningStats.topAdaptations.length > 0 && (
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Target className="h-5 w-5 text-orange-400" />
                  Active Adaptations
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Current behavioral adaptations based on user feedback
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {learningStats.topAdaptations.map((adaptation, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline" className="text-xs">
                        {adaptation.adaptation.replace(/_/g, ' ')}
                      </Badge>
                      <span className="text-gray-300 text-sm">
                        Triggered {adaptation.frequency} times
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Progress 
                        value={adaptation.weight * 100} 
                        className="w-20 h-2"
                      />
                      <span className="text-xs text-gray-400">
                        {Math.round(adaptation.weight * 100)}%
                      </span>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* User Profile */}
          {learningStats?.userProfile && (
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-400" />
                  User Profile & Preferences
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Learned preferences and interaction patterns
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Preferences */}
                {learningStats.userProfile.preferences && Object.keys(learningStats.userProfile.preferences).length > 0 && (
                  <div>
                    <h4 className="text-white font-medium mb-2">Preferences</h4>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(learningStats.userProfile.preferences).map(([key, value]) => (
                        <Badge key={key} variant="secondary" className="text-xs">
                          {key}: {value}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Learning Progress */}
                {learningStats.userProfile.learningProgress && (
                  <div>
                    <h4 className="text-white font-medium mb-2">Learning Progress</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gray-700 p-3 rounded-lg">
                        <div className="text-lg font-bold text-blue-400">
                          {learningStats.userProfile.learningProgress.totalInteractions}
                        </div>
                        <div className="text-xs text-gray-400">Total Interactions</div>
                      </div>
                      <div className="bg-gray-700 p-3 rounded-lg">
                        <div className={`text-lg font-bold ${getRatingColor(learningStats.userProfile.learningProgress.averageRating)}`}>
                          {learningStats.userProfile.learningProgress.averageRating}/5
                        </div>
                        <div className="text-xs text-gray-400">User Satisfaction</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Improvement Areas */}
                {learningStats.userProfile.learningProgress?.improvementAreas && learningStats.userProfile.learningProgress.improvementAreas.length > 0 && (
                  <div>
                    <h4 className="text-white font-medium mb-2">Focus Areas</h4>
                    <div className="flex flex-wrap gap-2">
                      {learningStats.userProfile.learningProgress.improvementAreas.map((area, index) => (
                        <Badge key={index} variant="destructive" className="text-xs">
                          {area.replace(/_/g, ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Strengths */}
                {learningStats.userProfile.learningProgress?.strengths && learningStats.userProfile.learningProgress.strengths.length > 0 && (
                  <div>
                    <h4 className="text-white font-medium mb-2">Strengths</h4>
                    <div className="flex flex-wrap gap-2">
                      {learningStats.userProfile.learningProgress.strengths.map((strength, index) => (
                        <Badge key={index} variant="default" className="text-xs bg-green-600">
                          {strength}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Recent Feedback */}
          {learningStats?.recentFeedback && learningStats.recentFeedback.length > 0 && (
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-green-400" />
                  Recent Feedback
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Latest user feedback and ratings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {learningStats.recentFeedback.slice(0, 5).map((feedback, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className={`h-3 w-3 ${
                              star <= feedback.rating 
                                ? 'text-yellow-400 fill-current' 
                                : 'text-gray-500'
                            }`}
                          />
                        ))}
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {feedback.type || 'general'}
                      </Badge>
                      {feedback.reasoningUsed && (
                        <Badge variant="secondary" className="text-xs">
                          reasoning
                        </Badge>
                      )}
                    </div>
                    <div className="text-xs text-gray-400">
                      {new Date(feedback.timestamp).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

              {/* No Data State */}
              {(!learningStats || learningStats.totalFeedback === 0) && (
                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-8 text-center">
                    <Brain className="h-12 w-12 text-gray-500 mx-auto mb-4" />
                    <h3 className="text-white font-medium mb-2">No Learning Data Yet</h3>
                    <p className="text-gray-400 text-sm mb-4">
                      Start using the AGI and providing feedback to see learning insights here.
                    </p>
                    <Button onClick={onClose} className="bg-purple-600 hover:bg-purple-700">
                      Start Chatting
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {/* Analytics Tab */}
          {activeTab === 'analytics' && performanceAnalytics && (
            <div className="space-y-6">
              {/* Trend Analysis */}
              {performanceAnalytics.trends && !performanceAnalytics.trends.insufficient_data && (
                <Card className="bg-gray-800 border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-blue-400" />
                      Performance Trends
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {performanceAnalytics.trends.overall && (
                      <div className="bg-gray-700 p-4 rounded-lg">
                        <h4 className="text-white font-medium mb-2">Overall Trend</h4>
                        <div className="flex items-center gap-2">
                          {getTrendIcon(performanceAnalytics.trends.overall.trend)}
                          <span className={`font-medium ${getTrendColor(performanceAnalytics.trends.overall.trend)}`}>
                            {performanceAnalytics.trends.overall.trend}
                          </span>
                          {performanceAnalytics.trends.overall.change && (
                            <span className="text-gray-400 text-sm">
                              ({performanceAnalytics.trends.overall.change > 0 ? '+' : ''}{performanceAnalytics.trends.overall.change})
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Aspect Analysis */}
              {performanceAnalytics.aspectAnalysis && Object.keys(performanceAnalytics.aspectAnalysis).length > 0 && (
                <Card className="bg-gray-800 border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Target className="h-5 w-5 text-orange-400" />
                      Aspect Performance
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {Object.entries(performanceAnalytics.aspectAnalysis).map(([aspect, data]) => (
                      <div key={aspect} className="bg-gray-700 p-4 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-white font-medium capitalize">{aspect}</h4>
                          <div className="flex items-center gap-2">
                            {getTrendIcon(data.trend)}
                            <span className={`text-lg font-bold ${getRatingColor(data.average)}`}>
                              {data.average}/5
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-400">Recent: {data.recentAverage}/5</span>
                          <span className="text-gray-400">{data.totalRatings} ratings</span>
                        </div>
                        <Progress value={(data.average / 5) * 100} className="mt-2" />
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}

              {/* User Satisfaction Distribution */}
              {performanceAnalytics.userSatisfactionTrends && !performanceAnalytics.userSatisfactionTrends.insufficient_data && (
                <Card className="bg-gray-800 border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Star className="h-5 w-5 text-yellow-400" />
                      Satisfaction Distribution (Last 30 Days)
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {Object.entries(performanceAnalytics.userSatisfactionTrends.percentages).map(([level, percentage]) => (
                      <div key={level} className="flex items-center justify-between">
                        <span className="text-gray-300 capitalize">{level.replace('_', ' ')}</span>
                        <div className="flex items-center gap-2 w-32">
                          <Progress value={percentage} className="flex-1" />
                          <span className="text-gray-400 text-sm w-8">{percentage}%</span>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {/* Recommendations Tab */}
          {activeTab === 'recommendations' && performanceAnalytics?.improvementRecommendations && (
            <div className="space-y-6">
              <Card className="bg-gray-800 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Lightbulb className="h-5 w-5 text-yellow-400" />
                    Improvement Recommendations
                  </CardTitle>
                  <CardDescription className="text-gray-400">
                    AI-generated suggestions based on user feedback analysis
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {performanceAnalytics.improvementRecommendations.length === 0 ? (
                    <div className="text-center py-8">
                      <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
                      <h3 className="text-white font-medium mb-2">All Good!</h3>
                      <p className="text-gray-400">No specific improvement recommendations at this time.</p>
                    </div>
                  ) : (
                    performanceAnalytics.improvementRecommendations.map((rec, index) => (
                      <div key={index} className={`p-4 rounded-lg border-l-4 ${
                        rec.priority === 'high' ? 'bg-red-500/10 border-red-400' :
                        rec.priority === 'medium' ? 'bg-yellow-500/10 border-yellow-400' :
                        'bg-blue-500/10 border-blue-400'
                      }`}>
                        <div className="flex items-start justify-between mb-2">
                          <Badge variant={rec.priority === 'high' ? 'destructive' : rec.priority === 'medium' ? 'default' : 'secondary'}>
                            {rec.priority} priority
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {rec.category.replace('_', ' ')}
                          </Badge>
                        </div>
                        <h4 className="text-white font-medium mb-1">{rec.issue}</h4>
                        <p className="text-gray-300 text-sm">{rec.suggestion}</p>
                        {rec.aspect && (
                          <div className="mt-2">
                            <Badge variant="secondary" className="text-xs">
                              Focus: {rec.aspect}
                            </Badge>
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>

      {/* Learning Insights Modal */}
      <LearningInsights
        isVisible={showInsights}
        onClose={() => setShowInsights(false)}
      />
    </div>
  )
}

export default LearningDashboard
