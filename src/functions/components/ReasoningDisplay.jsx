import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { 
  Brain, 
  ChevronRight, 
  ChevronDown, 
  CheckCircle, 
  Clock, 
  Target, 
  Lightbulb,
  ArrowRight,
  Zap,
  Eye,
  EyeOff,
  BarChart3,
  TreePine,
  Network
} from 'lucide-react'

const ReasoningDisplay = ({ reasoningData, isVisible = true, onToggleVisibility }) => {
  const [expandedSteps, setExpandedSteps] = useState(new Set())
  const [currentStep, setCurrentStep] = useState(0)
  const [animationSpeed, setAnimationSpeed] = useState('normal')
  const [showMetrics, setShowMetrics] = useState(false)

  // Auto-expand first step
  useEffect(() => {
    if (reasoningData?.reasoningSteps?.length > 0) {
      setExpandedSteps(new Set([1]))
    }
  }, [reasoningData])

  if (!reasoningData || !reasoningData.useReasoning) {
    return null
  }

  const { 
    complexity, 
    decomposition, 
    reasoningSteps, 
    reasoningChain, 
    metadata 
  } = reasoningData

  const toggleStep = (stepId) => {
    const newExpanded = new Set(expandedSteps)
    if (newExpanded.has(stepId)) {
      newExpanded.delete(stepId)
    } else {
      newExpanded.add(stepId)
    }
    setExpandedSteps(newExpanded)
  }

  const expandAllSteps = () => {
    setExpandedSteps(new Set(reasoningSteps.map(step => step.id)))
  }

  const collapseAllSteps = () => {
    setExpandedSteps(new Set())
  }

  const getStepIcon = (step) => {
    switch (step.type) {
      case 'understanding':
        return <Target className="h-4 w-4" />
      case 'information':
        return <Eye className="h-4 w-4" />
      case 'solving':
        return <Zap className="h-4 w-4" />
      case 'integration':
        return <Network className="h-4 w-4" />
      case 'validation':
        return <CheckCircle className="h-4 w-4" />
      default:
        return <Brain className="h-4 w-4" />
    }
  }

  const getStepColor = (step) => {
    switch (step.type) {
      case 'understanding':
        return 'text-blue-400 bg-blue-500/20'
      case 'information':
        return 'text-green-400 bg-green-500/20'
      case 'solving':
        return 'text-purple-400 bg-purple-500/20'
      case 'integration':
        return 'text-orange-400 bg-orange-500/20'
      case 'validation':
        return 'text-cyan-400 bg-cyan-500/20'
      default:
        return 'text-gray-400 bg-gray-500/20'
    }
  }

  const getComplexityColor = (level) => {
    switch (level) {
      case 'very_complex':
        return 'bg-red-500/20 text-red-300 border-red-400'
      case 'complex':
        return 'bg-orange-500/20 text-orange-300 border-orange-400'
      case 'moderate':
        return 'bg-yellow-500/20 text-yellow-300 border-yellow-400'
      default:
        return 'bg-green-500/20 text-green-300 border-green-400'
    }
  }

  const formatConfidence = (confidence) => {
    return `${Math.round(confidence * 100)}%`
  }

  if (!isVisible) {
    return (
      <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg border border-white/10">
        <div className="flex items-center gap-2">
          <Brain className="h-4 w-4 text-purple-400" />
          <span className="text-white text-sm">Reasoning process available</span>
          <Badge variant="outline" className="border-purple-400 text-purple-300">
            {reasoningSteps.length} steps
          </Badge>
        </div>
        <Button
          size="sm"
          variant="ghost"
          onClick={onToggleVisibility}
          className="text-purple-400 hover:text-purple-300 hover:bg-purple-500/20"
        >
          <Eye className="h-3 w-3 mr-1" />
          Show Reasoning
        </Button>
      </div>
    )
  }

  return (
    <Card className="bg-white/10 backdrop-blur-sm border-white/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-400" />
            <CardTitle className="text-white">AGI Reasoning Process</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            {onToggleVisibility && (
              <Button
                size="sm"
                variant="ghost"
                onClick={onToggleVisibility}
                className="text-gray-400 hover:text-gray-300 hover:bg-gray-500/20"
              >
                <EyeOff className="h-3 w-3" />
              </Button>
            )}
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowMetrics(!showMetrics)}
              className="text-gray-400 hover:text-gray-300 hover:bg-gray-500/20"
            >
              <BarChart3 className="h-3 w-3" />
            </Button>
          </div>
        </div>
        <CardDescription className="text-gray-300">
          Step-by-step analysis of how the AGI approaches this problem
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Complexity Overview */}
        <div className="bg-black/20 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-white text-sm font-medium">Problem Analysis</h4>
            <Badge 
              variant="outline" 
              className={getComplexityColor(complexity.level)}
            >
              {complexity.level.replace('_', ' ')} complexity
            </Badge>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Complexity Score:</span>
              <span className="text-white ml-2">{complexity.score}/100</span>
            </div>
            <div>
              <span className="text-gray-400">Reasoning Steps:</span>
              <span className="text-white ml-2">{reasoningSteps.length}</span>
            </div>
          </div>
          
          {complexity.reasoning && (
            <p className="text-gray-300 text-sm mt-2">
              <Lightbulb className="h-3 w-3 inline mr-1" />
              This problem {complexity.reasoning}
            </p>
          )}
        </div>

        {/* Problem Decomposition */}
        {decomposition && (
          <div className="bg-black/20 rounded-lg p-4">
            <h4 className="text-white text-sm font-medium mb-3 flex items-center gap-2">
              <TreePine className="h-4 w-4" />
              Problem Decomposition
            </h4>
            
            <div className="space-y-2">
              <div>
                <span className="text-gray-400 text-sm">Main Goal:</span>
                <p className="text-white text-sm">{decomposition.mainGoal.target}</p>
              </div>
              
              {decomposition.subProblems.length > 0 && (
                <div>
                  <span className="text-gray-400 text-sm">Sub-problems ({decomposition.subProblems.length}):</span>
                  <div className="mt-1 space-y-1">
                    {decomposition.subProblems.slice(0, 3).map((subProblem, index) => (
                      <div key={index} className="text-gray-300 text-xs flex items-center gap-2">
                        <div className="w-1 h-1 bg-purple-400 rounded-full"></div>
                        {subProblem.description}
                      </div>
                    ))}
                    {decomposition.subProblems.length > 3 && (
                      <div className="text-gray-400 text-xs">
                        ... and {decomposition.subProblems.length - 3} more
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Reasoning Steps Controls */}
        <div className="flex items-center justify-between">
          <h4 className="text-white text-sm font-medium">Reasoning Steps</h4>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={expandAllSteps}
              className="text-xs text-gray-400 hover:text-gray-300"
            >
              Expand All
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={collapseAllSteps}
              className="text-xs text-gray-400 hover:text-gray-300"
            >
              Collapse All
            </Button>
          </div>
        </div>

        {/* Reasoning Steps */}
        <div className="space-y-3">
          {reasoningSteps.map((step, index) => {
            const isExpanded = expandedSteps.has(step.id)
            const isLast = index === reasoningSteps.length - 1
            
            return (
              <div key={step.id} className="relative">
                {/* Connection Line */}
                {!isLast && (
                  <div className="absolute left-6 top-12 w-0.5 h-8 bg-gradient-to-b from-purple-400/50 to-transparent"></div>
                )}
                
                <div className="bg-black/20 rounded-lg border border-white/10 overflow-hidden">
                  {/* Step Header */}
                  <div 
                    className="flex items-center justify-between p-3 cursor-pointer hover:bg-white/5 transition-colors"
                    onClick={() => toggleStep(step.id)}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${getStepColor(step)}`}>
                        {getStepIcon(step)}
                      </div>
                      <div>
                        <h5 className="text-white text-sm font-medium">{step.title}</h5>
                        <p className="text-gray-400 text-xs">{step.type.replace('_', ' ')}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {step.confidence && (
                        <Badge variant="outline" className="border-gray-400 text-gray-300 text-xs">
                          {formatConfidence(step.confidence)}
                        </Badge>
                      )}
                      {isExpanded ? (
                        <ChevronDown className="h-4 w-4 text-gray-400" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  </div>

                  {/* Step Details */}
                  {isExpanded && (
                    <div className="px-3 pb-3 space-y-3 border-t border-white/10">
                      <div className="pt-3">
                        <h6 className="text-gray-400 text-xs font-medium mb-1">Description:</h6>
                        <p className="text-gray-300 text-sm">{step.description}</p>
                      </div>
                      
                      <div>
                        <h6 className="text-gray-400 text-xs font-medium mb-1">Reasoning:</h6>
                        <p className="text-gray-300 text-sm">{step.reasoning}</p>
                      </div>

                      {step.inputs && step.inputs.length > 0 && (
                        <div>
                          <h6 className="text-gray-400 text-xs font-medium mb-1">Inputs:</h6>
                          <div className="flex flex-wrap gap-1">
                            {step.inputs.map((input, idx) => (
                              <Badge key={idx} variant="outline" className="border-blue-400 text-blue-300 text-xs">
                                {input}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {step.outputs && step.outputs.length > 0 && (
                        <div>
                          <h6 className="text-gray-400 text-xs font-medium mb-1">Expected Outputs:</h6>
                          <div className="flex flex-wrap gap-1">
                            {step.outputs.map((output, idx) => (
                              <Badge key={idx} variant="outline" className="border-green-400 text-green-300 text-xs">
                                {output}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {step.priority && (
                        <div className="flex items-center gap-2">
                          <span className="text-gray-400 text-xs">Priority:</span>
                          <Badge 
                            variant="outline" 
                            className={
                              step.priority === 'high' ? 'border-red-400 text-red-300' :
                              step.priority === 'medium' ? 'border-yellow-400 text-yellow-300' :
                              'border-gray-400 text-gray-300'
                            }
                          >
                            {step.priority}
                          </Badge>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>

        {/* Metrics Panel */}
        {showMetrics && metadata && (
          <div className="bg-black/20 rounded-lg p-4">
            <h4 className="text-white text-sm font-medium mb-3 flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Reasoning Metrics
            </h4>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Total Steps:</span>
                <span className="text-white ml-2">{metadata.stepCount}</span>
              </div>
              <div>
                <span className="text-gray-400">Difficulty:</span>
                <span className="text-white ml-2">{metadata.estimatedDifficulty}</span>
              </div>
              <div>
                <span className="text-gray-400">Generated:</span>
                <span className="text-white ml-2">
                  {new Date(metadata.timestamp).toLocaleTimeString()}
                </span>
              </div>
              <div>
                <span className="text-gray-400">Avg Confidence:</span>
                <span className="text-white ml-2">
                  {formatConfidence(
                    reasoningSteps.reduce((sum, step) => sum + (step.confidence || 0), 0) / reasoningSteps.length
                  )}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Reasoning Chain Summary */}
        {reasoningChain && (
          <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg p-4 border border-purple-400/20">
            <h4 className="text-white text-sm font-medium mb-2 flex items-center gap-2">
              <ArrowRight className="h-4 w-4" />
              Reasoning Summary
            </h4>
            <p className="text-gray-300 text-sm mb-3">{reasoningChain.introduction}</p>
            
            <div className="space-y-1">
              {reasoningChain.steps.slice(0, 3).map((step, index) => (
                <div key={index} className="text-gray-300 text-xs flex items-start gap-2">
                  <span className="text-purple-400 font-medium">{step.number}.</span>
                  <span>{step.title}: {step.focus}</span>
                </div>
              ))}
              {reasoningChain.steps.length > 3 && (
                <div className="text-gray-400 text-xs">
                  ... {reasoningChain.steps.length - 3} more steps
                </div>
              )}
            </div>
            
            <p className="text-gray-300 text-sm mt-3 italic">{reasoningChain.conclusion}</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default ReasoningDisplay
