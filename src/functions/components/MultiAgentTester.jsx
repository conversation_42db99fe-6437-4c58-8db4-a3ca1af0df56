// Multi-Agent Tester - Interactive testing component
import React, { useState, useCallback, useEffect } from 'react'
import { runFullDemo, runQuickDemo } from '../demos/multiAgentDemo.js'
import { runAllTests, runSpecificTestSuite, showTestCoverage, runSmokeTest } from '../testRunner.js'
import {
  initializeBrowserTests,
  runQuickBrowserTest,
  testAgentTypes,
  testMultiAgentCollaboration,
  testPerformance,
  runCompleteBrowserTests
} from '../browserTests.js'
import './MultiAgentTester.css'

const MultiAgentTester = () => {
  const [testResults, setTestResults] = useState(null)
  const [isRunning, setIsRunning] = useState(false)
  const [selectedTest, setSelectedTest] = useState('browser_quick')
  const [logs, setLogs] = useState([])
  const [browserTestsInitialized, setBrowserTestsInitialized] = useState(false)

  // Add log entry
  const addLog = useCallback((message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, { timestamp, message, type }])
  }, [])

  // Clear logs
  const clearLogs = useCallback(() => {
    setLogs([])
  }, [])

  // Initialize browser tests on component mount
  useEffect(() => {
    const initBrowserTests = async () => {
      try {
        addLog('Initializing browser test environment...', 'info')
        const success = await initializeBrowserTests()
        setBrowserTestsInitialized(success)
        if (success) {
          addLog('Browser test environment initialized successfully!', 'success')
        } else {
          addLog('Failed to initialize browser test environment', 'error')
        }
      } catch (error) {
        addLog(`Browser test initialization error: ${error.message}`, 'error')
        setBrowserTestsInitialized(false)
      }
    }

    initBrowserTests()
  }, [addLog])

  // Run selected test
  const runTest = useCallback(async () => {
    setIsRunning(true)
    setTestResults(null)
    clearLogs()
    
    addLog('Starting test execution...', 'info')
    
    try {
      let result
      
      switch (selectedTest) {
        case 'browser_quick':
          addLog('Running browser quick test...', 'info')
          result = await runQuickBrowserTest()
          break

        case 'browser_agents':
          addLog('Testing individual agent types...', 'info')
          result = await testAgentTypes()
          break

        case 'browser_collaboration':
          addLog('Testing multi-agent collaboration...', 'info')
          result = await testMultiAgentCollaboration()
          break

        case 'browser_performance':
          addLog('Running performance test...', 'info')
          result = await testPerformance()
          break

        case 'browser_complete':
          addLog('Running complete browser test suite...', 'info')
          result = await runCompleteBrowserTests()
          break

        case 'quick_demo':
          addLog('Running quick demo...', 'info')
          result = await runQuickDemo()
          break

        case 'full_demo':
          addLog('Running full demonstration...', 'info')
          result = await runFullDemo()
          break
          
        case 'smoke_test':
          addLog('Running smoke test...', 'info')
          result = await runSmokeTest()
          break
          
        case 'all_tests':
          addLog('Running complete test suite...', 'info')
          result = await runAllTests()
          break
          
        case 'agent_framework':
          addLog('Testing agent framework...', 'info')
          result = await runSpecificTestSuite('agent_framework')
          break
          
        case 'specialized_agents':
          addLog('Testing specialized agents...', 'info')
          result = await runSpecificTestSuite('specialized_agents')
          break
          
        case 'coordination':
          addLog('Testing coordination system...', 'info')
          result = await runSpecificTestSuite('coordination_system')
          break
          
        case 'communication':
          addLog('Testing communication protocol...', 'info')
          result = await runSpecificTestSuite('communication_protocol')
          break
          
        case 'synthesis':
          addLog('Testing result synthesis...', 'info')
          result = await runSpecificTestSuite('result_synthesis')
          break
          
        default:
          throw new Error(`Unknown test type: ${selectedTest}`)
      }
      
      setTestResults(result)
      addLog('Test execution completed!', 'success')
      
    } catch (error) {
      addLog(`Test execution failed: ${error.message}`, 'error')
      setTestResults({ success: false, error: error.message })
    } finally {
      setIsRunning(false)
    }
  }, [selectedTest, addLog, clearLogs])

  // Show test coverage
  const showCoverage = useCallback(() => {
    try {
      const coverage = showTestCoverage()
      setTestResults(coverage)
      addLog('Test coverage report generated', 'info')
    } catch (error) {
      addLog(`Failed to generate coverage report: ${error.message}`, 'error')
    }
  }, [addLog])

  // Render test results
  const renderTestResults = () => {
    if (!testResults) return null

    if (Array.isArray(testResults)) {
      // Demo results
      return (
        <div className="test-results">
          <h3>🎬 Demo Results</h3>
          <div className="demo-summary">
            <span className="result-stat">
              Successful: {testResults.filter(r => r.success).length}/{testResults.length}
            </span>
          </div>
          <div className="demo-list">
            {testResults.map((result, index) => (
              <div key={index} className={`demo-item ${result.success ? 'success' : 'failed'}`}>
                <span className="demo-icon">{result.success ? '✅' : '❌'}</span>
                <span className="demo-name">{result.name}</span>
                {result.error && <span className="demo-error">{result.error}</span>}
              </div>
            ))}
          </div>
        </div>
      )
    }

    if (testResults.summary) {
      // Full test suite results
      return (
        <div className="test-results">
          <h3>🧪 Test Suite Results</h3>
          <div className="test-summary">
            <div className="summary-stats">
              <div className="stat">
                <span className="stat-label">Total Tests</span>
                <span className="stat-value">{testResults.summary.totalTests}</span>
              </div>
              <div className="stat">
                <span className="stat-label">Passed</span>
                <span className="stat-value success">{testResults.summary.totalPassed}</span>
              </div>
              <div className="stat">
                <span className="stat-label">Failed</span>
                <span className="stat-value failed">{testResults.summary.totalFailed}</span>
              </div>
              <div className="stat">
                <span className="stat-label">Success Rate</span>
                <span className="stat-value">{testResults.summary.successRate}%</span>
              </div>
            </div>
          </div>
          
          <div className="test-suites">
            <h4>Test Suites</h4>
            {testResults.suites.map((suite, index) => (
              <div key={index} className={`suite-item ${suite.failed === 0 ? 'success' : 'failed'}`}>
                <span className="suite-icon">{suite.failed === 0 ? '✅' : '❌'}</span>
                <span className="suite-name">{suite.name}</span>
                <span className="suite-stats">{suite.passed}/{suite.passed + suite.failed}</span>
                <span className="suite-duration">{Math.round(suite.duration / 1000)}s</span>
              </div>
            ))}
          </div>

          {testResults.failedTests && testResults.failedTests.length > 0 && (
            <div className="failed-tests">
              <h4>❌ Failed Tests</h4>
              {testResults.failedTests.map((test, index) => (
                <div key={index} className="failed-test">
                  <span className="test-suite">{test.suite}</span>
                  <span className="test-name">{test.test}</span>
                  <span className="test-error">{test.error}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )
    }

    if (testResults.components) {
      // Coverage results
      return (
        <div className="test-results">
          <h3>📊 Test Coverage</h3>
          <div className="coverage-summary">
            <div className="stat">
              <span className="stat-label">Total Tests</span>
              <span className="stat-value">{testResults.totalTests}</span>
            </div>
          </div>
          
          <div className="coverage-components">
            <h4>Component Coverage</h4>
            {Object.entries(testResults.components).map(([component, count]) => (
              <div key={component} className="coverage-item">
                <span className="component-name">{component}</span>
                <span className="component-tests">{count} tests</span>
              </div>
            ))}
          </div>
          
          <div className="coverage-types">
            <h4>Test Types</h4>
            {Object.entries(testResults.testTypes).map(([type, count]) => (
              <div key={type} className="coverage-item">
                <span className="type-name">{type}</span>
                <span className="type-tests">{count} tests</span>
              </div>
            ))}
          </div>
        </div>
      )
    }

    // Simple boolean or other result
    return (
      <div className="test-results">
        <h3>Test Result</h3>
        <div className={`simple-result ${testResults.success !== false ? 'success' : 'failed'}`}>
          {testResults.success !== false ? '✅ Success' : `❌ Failed: ${testResults.error}`}
        </div>
      </div>
    )
  }

  return (
    <div className="multi-agent-tester">
      <div className="tester-header">
        <h2>🧪 Multi-Agent System Tester</h2>
        <p>Test and demonstrate the multi-agent collaboration system</p>
      </div>

      <div className="tester-content">
        <div className="tester-controls">
          <div className="test-selection">
            <label htmlFor="test-select">Select Test:</label>
            <select
              id="test-select"
              value={selectedTest}
              onChange={(e) => setSelectedTest(e.target.value)}
              disabled={isRunning}
            >
              <optgroup label="🌐 Browser Tests (Recommended)">
                <option value="browser_quick">⚡ Quick Browser Test</option>
                <option value="browser_agents">🎨 Test Agent Types</option>
                <option value="browser_collaboration">🤝 Test Collaboration</option>
                <option value="browser_performance">⚡ Performance Test</option>
                <option value="browser_complete">🧪 Complete Browser Suite</option>
              </optgroup>
              <optgroup label="🎬 Node.js Demonstrations">
                <option value="quick_demo">⚡ Quick Demo</option>
                <option value="full_demo">🎬 Full Demo</option>
              </optgroup>
              <optgroup label="🧪 Node.js Test Suites">
                <option value="smoke_test">💨 Smoke Test</option>
                <option value="all_tests">🧪 All Tests</option>
              </optgroup>
              <optgroup label="🔧 Component Tests">
                <option value="agent_framework">🤖 Agent Framework</option>
                <option value="specialized_agents">🎨 Specialized Agents</option>
                <option value="coordination">🤝 Coordination System</option>
                <option value="communication">📡 Communication Protocol</option>
                <option value="synthesis">🔬 Result Synthesis</option>
              </optgroup>
            </select>
          </div>

          <div className="test-actions">
            <button 
              onClick={runTest} 
              disabled={isRunning}
              className="run-button"
            >
              {isRunning ? '⏳ Running...' : '▶️ Run Test'}
            </button>
            
            <button 
              onClick={showCoverage}
              disabled={isRunning}
              className="coverage-button"
            >
              📊 Show Coverage
            </button>
            
            <button 
              onClick={clearLogs}
              disabled={isRunning}
              className="clear-button"
            >
              🗑️ Clear Logs
            </button>
          </div>
        </div>

        <div className="tester-output">
          <div className="logs-section">
            <h3>📝 Execution Logs</h3>
            <div className="logs-container">
              {logs.length === 0 ? (
                <div className="no-logs">No logs yet. Run a test to see output.</div>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className={`log-entry ${log.type}`}>
                    <span className="log-timestamp">{log.timestamp}</span>
                    <span className="log-message">{log.message}</span>
                  </div>
                ))
              )}
            </div>
          </div>

          <div className="results-section">
            <h3>📊 Test Results</h3>
            <div className="results-container">
              {testResults ? renderTestResults() : (
                <div className="no-results">No results yet. Run a test to see results.</div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="tester-info">
        <h3>ℹ️ Test Information</h3>
        <div className="info-grid">
          <div className="info-item">
            <strong>⚡ Quick Browser Test:</strong> Fast test optimized for browser environment
          </div>
          <div className="info-item">
            <strong>🎨 Test Agent Types:</strong> Test all 5 specialized agent types
          </div>
          <div className="info-item">
            <strong>🤝 Test Collaboration:</strong> Test multi-agent coordination and synthesis
          </div>
          <div className="info-item">
            <strong>⚡ Performance Test:</strong> Test system performance and concurrent execution
          </div>
          <div className="info-item">
            <strong>🧪 Complete Browser Suite:</strong> Run all browser tests (recommended)
          </div>
          <div className="info-item">
            <strong>🎬 Node.js Tests:</strong> Server-side tests (may have limitations in browser)
          </div>
          <div className="info-item">
            <strong>📊 Status:</strong> {browserTestsInitialized ? '✅ Browser tests ready' : '⚠️ Initializing...'}
          </div>
        </div>
      </div>
    </div>
  )
}

export default MultiAgentTester
