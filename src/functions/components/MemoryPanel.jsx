// Memory Panel Component for displaying memory statistics and controls
import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { 
  Brain, 
  Database, 
  Clock, 
  MessageSquare, 
  Download, 
  Upload, 
  Trash2, 
  Settings,
  TrendingUp,
  User,
  Search
} from 'lucide-react'

const MemoryPanel = ({ memoryStats, onClearMemory, onExportMemory, onImportMemory, className = '' }) => {
  const [showConfirmClear, setShowConfirmClear] = useState(false)

  // Handle memory export
  const handleExport = () => {
    try {
      const memoryData = onExportMemory()
      if (memoryData) {
        const blob = new Blob([JSON.stringify(memoryData, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `agi-memory-${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        console.log('📥 Memory exported successfully')
      }
    } catch (error) {
      console.error('❌ Failed to export memory:', error)
    }
  }

  // Handle memory import
  const handleImport = (event) => {
    const file = event.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const memoryData = JSON.parse(e.target.result)
          const success = onImportMemory(memoryData)
          if (success) {
            console.log('📤 Memory imported successfully')
          } else {
            console.error('❌ Failed to import memory')
          }
        } catch (error) {
          console.error('❌ Invalid memory file:', error)
        }
      }
      reader.readAsText(file)
    }
    // Reset file input
    event.target.value = ''
  }

  // Handle clear memory with confirmation
  const handleClearMemory = () => {
    if (showConfirmClear) {
      const success = onClearMemory()
      if (success) {
        setShowConfirmClear(false)
      }
    } else {
      setShowConfirmClear(true)
      // Auto-hide confirmation after 5 seconds
      setTimeout(() => setShowConfirmClear(false), 5000)
    }
  }

  // Format storage size
  const formatStorageSize = (kb) => {
    if (kb < 1) return '< 1 KB'
    if (kb < 1024) return `${kb} KB`
    return `${(kb / 1024).toFixed(1)} MB`
  }

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'Never'
    return new Date(dateString).toLocaleDateString()
  }

  if (!memoryStats) {
    return (
      <Card className={`bg-white/10 backdrop-blur-sm border-white/20 ${className}`}>
        <CardContent className="p-6 text-center">
          <Brain className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-400">Loading memory statistics...</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={`bg-white/10 backdrop-blur-sm border-white/20 ${className}`}>
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Brain className="h-5 w-5 text-purple-400" />
          AGI Memory System
        </CardTitle>
        <CardDescription className="text-gray-300">
          Persistent conversation storage and learning
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Memory Statistics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-black/20 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-1">
              <MessageSquare className="h-4 w-4 text-blue-400" />
              <span className="text-blue-400 text-sm font-medium">Conversations</span>
            </div>
            <p className="text-white text-lg font-bold">{memoryStats.totalConversations}</p>
            <p className="text-gray-400 text-xs">{memoryStats.totalMessages} total messages</p>
          </div>

          <div className="bg-black/20 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-1">
              <Database className="h-4 w-4 text-green-400" />
              <span className="text-green-400 text-sm font-medium">Storage</span>
            </div>
            <p className="text-white text-lg font-bold">{formatStorageSize(memoryStats.storageUsed)}</p>
            <p className="text-gray-400 text-xs">{memoryStats.preferenceCount} preferences</p>
          </div>

          <div className="bg-black/20 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-1">
              <Clock className="h-4 w-4 text-orange-400" />
              <span className="text-orange-400 text-sm font-medium">First Chat</span>
            </div>
            <p className="text-white text-sm">{formatDate(memoryStats.oldestConversation)}</p>
          </div>

          <div className="bg-black/20 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-1">
              <TrendingUp className="h-4 w-4 text-cyan-400" />
              <span className="text-cyan-400 text-sm font-medium">Latest</span>
            </div>
            <p className="text-white text-sm">{formatDate(memoryStats.newestConversation)}</p>
          </div>
        </div>

        {/* Session Information */}
        <div className="bg-black/20 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-2">
            <User className="h-4 w-4 text-purple-400" />
            <span className="text-purple-400 text-sm font-medium">Current Session</span>
          </div>
          <p className="text-gray-300 text-xs font-mono">{memoryStats.currentSession}</p>
        </div>

        {/* Memory Controls */}
        <div className="space-y-2">
          <h4 className="text-white text-sm font-medium flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Memory Controls
          </h4>
          
          <div className="flex flex-wrap gap-2">
            {/* Export Memory */}
            <Button
              onClick={handleExport}
              size="sm"
              variant="outline"
              className="border-blue-400 text-blue-300 hover:bg-blue-600 hover:text-white"
            >
              <Download className="h-3 w-3 mr-1" />
              Export
            </Button>

            {/* Import Memory */}
            <label className="cursor-pointer">
              <Button
                size="sm"
                variant="outline"
                className="border-green-400 text-green-300 hover:bg-green-600 hover:text-white"
                asChild
              >
                <span>
                  <Upload className="h-3 w-3 mr-1" />
                  Import
                </span>
              </Button>
              <input
                type="file"
                accept=".json"
                onChange={handleImport}
                className="hidden"
              />
            </label>

            {/* Clear Memory */}
            <Button
              onClick={handleClearMemory}
              size="sm"
              variant="outline"
              className={`${
                showConfirmClear 
                  ? 'border-red-400 text-red-300 bg-red-900/20' 
                  : 'border-gray-400 text-gray-300'
              } hover:bg-red-600 hover:text-white`}
            >
              <Trash2 className="h-3 w-3 mr-1" />
              {showConfirmClear ? 'Confirm Clear' : 'Clear All'}
            </Button>
          </div>

          {showConfirmClear && (
            <div className="bg-red-900/20 border border-red-600/30 rounded-lg p-2">
              <p className="text-red-300 text-xs">
                ⚠️ This will permanently delete all conversations and preferences. Click "Confirm Clear" again to proceed.
              </p>
            </div>
          )}
        </div>

        {/* Memory Status */}
        <div className="flex items-center justify-between pt-2 border-t border-white/10">
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="bg-purple-600/20 text-purple-300">
              Memory Active
            </Badge>
            {memoryStats.totalConversations > 0 && (
              <Badge variant="outline" className="border-green-400 text-green-300">
                Learning Enabled
              </Badge>
            )}
          </div>
          <span className="text-gray-400 text-xs">
            Auto-save: ON
          </span>
        </div>
      </CardContent>
    </Card>
  )
}

export default MemoryPanel
