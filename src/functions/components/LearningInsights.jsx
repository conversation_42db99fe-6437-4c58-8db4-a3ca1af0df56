import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar,
  Clock,
  Star,
  Brain,
  Target,
  Zap,
  BarChart3,
  Activity,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react'
import learningService from '../services/learningService'

const LearningInsights = ({ isVisible = false, onClose }) => {
  const [insights, setInsights] = useState(null)
  const [timeRange, setTimeRange] = useState('all') // all, week, month
  const [isLoading, setIsLoading] = useState(true)

  // Load learning insights
  const loadInsights = async () => {
    try {
      setIsLoading(true)
      const analytics = learningService.getPerformanceAnalytics()
      const stats = learningService.getLearningStats()
      
      const processedInsights = {
        ...analytics,
        overview: stats,
        learningTimeline: generateLearningTimeline(stats.recentFeedback),
        improvementMilestones: identifyImprovementMilestones(stats.recentFeedback),
        learningVelocity: calculateLearningVelocity(stats.recentFeedback)
      }
      
      setInsights(processedInsights)
    } catch (error) {
      console.error('❌ Failed to load learning insights:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Generate learning timeline
  const generateLearningTimeline = (feedbackHistory) => {
    if (!feedbackHistory || feedbackHistory.length === 0) return []
    
    const timeline = feedbackHistory
      .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
      .map((feedback, index) => ({
        id: index,
        timestamp: feedback.timestamp,
        rating: feedback.rating,
        type: feedback.type || 'general',
        adaptationTriggers: feedback.adaptationTriggers || [],
        learningImpact: feedback.learningImpact || 0,
        milestone: identifyMilestone(feedback, index, feedbackHistory)
      }))
    
    return timeline
  }

  // Identify improvement milestones
  const identifyImprovementMilestones = (feedbackHistory) => {
    if (!feedbackHistory || feedbackHistory.length < 5) return []
    
    const milestones = []
    const sortedFeedback = feedbackHistory.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
    
    // First positive feedback
    const firstPositive = sortedFeedback.find(f => f.rating >= 4)
    if (firstPositive) {
      milestones.push({
        type: 'first_positive',
        timestamp: firstPositive.timestamp,
        title: 'First Positive Feedback',
        description: 'Received first rating of 4+ stars',
        impact: 'medium'
      })
    }
    
    // Consistency milestones (5 consecutive good ratings)
    let consecutiveGood = 0
    sortedFeedback.forEach((feedback, index) => {
      if (feedback.rating >= 4) {
        consecutiveGood++
        if (consecutiveGood === 5) {
          milestones.push({
            type: 'consistency',
            timestamp: feedback.timestamp,
            title: 'Consistency Achieved',
            description: '5 consecutive ratings of 4+ stars',
            impact: 'high'
          })
        }
      } else {
        consecutiveGood = 0
      }
    })
    
    // Adaptation milestones
    const adaptationFeedback = sortedFeedback.filter(f => f.adaptationTriggers && f.adaptationTriggers.length > 0)
    if (adaptationFeedback.length > 0) {
      milestones.push({
        type: 'adaptation',
        timestamp: adaptationFeedback[0].timestamp,
        title: 'First Adaptation',
        description: 'AGI began adapting based on feedback',
        impact: 'high'
      })
    }
    
    return milestones.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
  }

  // Calculate learning velocity
  const calculateLearningVelocity = (feedbackHistory) => {
    if (!feedbackHistory || feedbackHistory.length < 10) {
      return { velocity: 'insufficient_data', trend: 'unknown' }
    }
    
    const sortedFeedback = feedbackHistory.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
    const recent = sortedFeedback.slice(-10)
    const older = sortedFeedback.slice(-20, -10)
    
    if (older.length === 0) return { velocity: 'stable', trend: 'unknown' }
    
    const recentAvg = recent.reduce((sum, f) => sum + f.rating, 0) / recent.length
    const olderAvg = older.reduce((sum, f) => sum + f.rating, 0) / older.length
    
    const improvement = recentAvg - olderAvg
    const timeSpan = new Date(recent[recent.length - 1].timestamp) - new Date(recent[0].timestamp)
    const daysSpan = timeSpan / (1000 * 60 * 60 * 24)
    
    let velocity = 'stable'
    if (improvement > 0.5 && daysSpan < 7) velocity = 'fast'
    else if (improvement > 0.3 && daysSpan < 14) velocity = 'moderate'
    else if (improvement > 0.1) velocity = 'slow'
    else if (improvement < -0.3) velocity = 'declining'
    
    return {
      velocity,
      trend: improvement > 0 ? 'improving' : improvement < 0 ? 'declining' : 'stable',
      improvement: Math.round(improvement * 100) / 100,
      timeSpan: Math.round(daysSpan)
    }
  }

  // Identify milestone for feedback
  const identifyMilestone = (feedback, index, history) => {
    if (feedback.rating === 5 && index === 0) return 'first_perfect'
    if (feedback.rating >= 4 && history.slice(0, index).every(f => f.rating < 4)) return 'breakthrough'
    if (feedback.adaptationTriggers && feedback.adaptationTriggers.length > 0) return 'adaptation'
    return null
  }

  // Get velocity color
  const getVelocityColor = (velocity) => {
    switch (velocity) {
      case 'fast': return 'text-green-400'
      case 'moderate': return 'text-blue-400'
      case 'slow': return 'text-yellow-400'
      case 'declining': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  // Get milestone icon
  const getMilestoneIcon = (type) => {
    switch (type) {
      case 'first_positive': return <Star className="h-4 w-4 text-yellow-400" />
      case 'consistency': return <Target className="h-4 w-4 text-green-400" />
      case 'adaptation': return <Brain className="h-4 w-4 text-purple-400" />
      case 'breakthrough': return <Zap className="h-4 w-4 text-blue-400" />
      default: return <CheckCircle className="h-4 w-4 text-gray-400" />
    }
  }

  useEffect(() => {
    if (isVisible) {
      loadInsights()
    }
  }, [isVisible])

  if (!isVisible) return null

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
        <Card className="bg-gray-900 border-gray-700 w-96">
          <CardContent className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-400 mx-auto mb-4"></div>
            <p className="text-gray-300">Loading learning insights...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 rounded-lg border border-gray-700 w-full max-w-5xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <Activity className="h-6 w-6 text-blue-400" />
            <div>
              <h2 className="text-xl font-bold text-white">Learning Insights</h2>
              <p className="text-gray-400 text-sm">Detailed learning progress and improvement analysis</p>
            </div>
          </div>
          <Button
            size="sm"
            variant="ghost"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-300"
          >
            ✕
          </Button>
        </div>

        <div className="p-6 space-y-6">
          {/* Learning Velocity */}
          {insights?.learningVelocity && (
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-green-400" />
                  Learning Velocity
                </CardTitle>
                <CardDescription className="text-gray-400">
                  How quickly the AGI is improving based on feedback
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${getVelocityColor(insights.learningVelocity.velocity)}`}>
                      {insights.learningVelocity.velocity.replace('_', ' ').toUpperCase()}
                    </div>
                    <div className="text-sm text-gray-400">Learning Speed</div>
                  </div>
                  {insights.learningVelocity.improvement !== undefined && (
                    <div className="text-center">
                      <div className={`text-xl font-bold ${insights.learningVelocity.improvement > 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {insights.learningVelocity.improvement > 0 ? '+' : ''}{insights.learningVelocity.improvement}
                      </div>
                      <div className="text-sm text-gray-400">Rating Change</div>
                    </div>
                  )}
                  {insights.learningVelocity.timeSpan && (
                    <div className="text-center">
                      <div className="text-xl font-bold text-blue-400">
                        {insights.learningVelocity.timeSpan}
                      </div>
                      <div className="text-sm text-gray-400">Days</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Improvement Milestones */}
          {insights?.improvementMilestones && insights.improvementMilestones.length > 0 && (
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Target className="h-5 w-5 text-orange-400" />
                  Learning Milestones
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Key achievements in the learning journey
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {insights.improvementMilestones.map((milestone, index) => (
                  <div key={index} className="flex items-center gap-4 p-3 bg-gray-700 rounded-lg">
                    <div className="flex-shrink-0">
                      {getMilestoneIcon(milestone.type)}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-white font-medium">{milestone.title}</h4>
                      <p className="text-gray-400 text-sm">{milestone.description}</p>
                    </div>
                    <div className="text-right">
                      <Badge variant={milestone.impact === 'high' ? 'default' : 'secondary'}>
                        {milestone.impact} impact
                      </Badge>
                      <div className="text-xs text-gray-400 mt-1">
                        {new Date(milestone.timestamp).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Learning Timeline */}
          {insights?.learningTimeline && insights.learningTimeline.length > 0 && (
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Clock className="h-5 w-5 text-blue-400" />
                  Learning Timeline
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Recent feedback and learning progress over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {insights.learningTimeline.slice(-20).map((entry, index) => (
                    <div key={entry.id} className="flex items-center gap-3 p-2 bg-gray-700 rounded">
                      <div className="flex items-center gap-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className={`h-3 w-3 ${
                              star <= entry.rating 
                                ? 'text-yellow-400 fill-current' 
                                : 'text-gray-500'
                            }`}
                          />
                        ))}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {entry.type}
                          </Badge>
                          {entry.milestone && (
                            <Badge variant="secondary" className="text-xs">
                              {entry.milestone}
                            </Badge>
                          )}
                        </div>
                        {entry.adaptationTriggers.length > 0 && (
                          <div className="text-xs text-gray-400 mt-1">
                            Adaptations: {entry.adaptationTriggers.join(', ')}
                          </div>
                        )}
                      </div>
                      <div className="text-xs text-gray-400">
                        {new Date(entry.timestamp).toLocaleDateString()}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* No Data State */}
          {(!insights || !insights.overview || insights.overview.totalFeedback === 0) && (
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-8 text-center">
                <Activity className="h-12 w-12 text-gray-500 mx-auto mb-4" />
                <h3 className="text-white font-medium mb-2">No Learning Data Yet</h3>
                <p className="text-gray-400 text-sm mb-4">
                  Start providing feedback to see detailed learning insights and progress tracking.
                </p>
                <Button onClick={onClose} className="bg-blue-600 hover:bg-blue-700">
                  Start Learning Journey
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

export default LearningInsights
