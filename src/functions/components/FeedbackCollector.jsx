import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '../../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Textarea } from '../../components/ui/textarea'
import { Badge } from '../../components/ui/badge'
import { 
  ThumbsUp, 
  ThumbsDown, 
  Star, 
  MessageSquare, 
  Send,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Brain,
  Target,
  Lightbulb
} from 'lucide-react'

const FeedbackCollector = ({ 
  messageId, 
  messageContent, 
  reasoningData = null,
  validationResult = null,
  onFeedbackSubmit,
  initialFeedback = null,
  compact = false 
}) => {
  const [feedbackState, setFeedbackState] = useState('idle') // idle, collecting, submitted
  const [rating, setRating] = useState(initialFeedback?.rating || 0)
  const [feedbackText, setFeedbackText] = useState(initialFeedback?.text || '')
  const [feedbackType, setFeedbackType] = useState(initialFeedback?.type || null) // helpful, unhelpful, unclear, incorrect
  const [specificAspects, setSpecificAspects] = useState(initialFeedback?.aspects || {
    accuracy: 0,
    clarity: 0,
    completeness: 0,
    reasoning: 0,
    usefulness: 0
  })
  const [isExpanded, setIsExpanded] = useState(false)
  const [submitStatus, setSubmitStatus] = useState(null)

  // Load existing feedback if available
  useEffect(() => {
    if (initialFeedback) {
      setRating(initialFeedback.rating)
      setFeedbackText(initialFeedback.text)
      setFeedbackType(initialFeedback.type)
      setSpecificAspects(initialFeedback.aspects || specificAspects)
      setFeedbackState('submitted')
    }
  }, [initialFeedback])

  const handleQuickFeedback = (type) => {
    setFeedbackType(type)
    
    // Set default ratings based on feedback type
    if (type === 'helpful') {
      setRating(5)
      setSpecificAspects({
        accuracy: 5,
        clarity: 5,
        completeness: 4,
        reasoning: reasoningData ? 5 : 4,
        usefulness: 5
      })
    } else if (type === 'unhelpful') {
      setRating(2)
      setSpecificAspects({
        accuracy: 2,
        clarity: 3,
        completeness: 2,
        reasoning: reasoningData ? 2 : 3,
        usefulness: 2
      })
    }
    
    setFeedbackState('collecting')
    if (!compact) setIsExpanded(true)
  }

  const handleStarRating = (starRating) => {
    setRating(starRating)
    setFeedbackState('collecting')
    
    // Auto-adjust specific aspects based on overall rating
    const aspectRating = Math.max(1, Math.min(5, starRating))
    setSpecificAspects({
      accuracy: aspectRating,
      clarity: aspectRating,
      completeness: aspectRating - (starRating < 3 ? 1 : 0),
      reasoning: reasoningData ? aspectRating : aspectRating - 1,
      usefulness: aspectRating
    })
  }

  const handleAspectRating = (aspect, value) => {
    setSpecificAspects(prev => ({
      ...prev,
      [aspect]: value
    }))
    
    // Update overall rating based on aspects
    const avgRating = Object.values({...specificAspects, [aspect]: value})
      .reduce((sum, val) => sum + val, 0) / 5
    setRating(Math.round(avgRating))
  }

  const handleSubmitFeedback = async () => {
    if (rating === 0 && !feedbackText.trim()) {
      setSubmitStatus({ type: 'error', message: 'Please provide a rating or feedback text' })
      return
    }

    setSubmitStatus({ type: 'loading', message: 'Submitting feedback...' })

    const feedbackData = {
      messageId,
      timestamp: new Date().toISOString(),
      rating,
      feedbackText: feedbackText.trim(),
      type: feedbackType,
      aspects: specificAspects,
      messageContent: messageContent.substring(0, 500), // Store snippet for context
      reasoningUsed: !!reasoningData,
      validationScore: validationResult?.score || null,
      metadata: {
        messageLength: messageContent.length,
        hasReasoning: !!reasoningData,
        reasoningSteps: reasoningData?.reasoningSteps?.length || 0,
        complexity: reasoningData?.complexity?.level || 'simple'
      }
    }

    try {
      if (onFeedbackSubmit) {
        await onFeedbackSubmit(feedbackData)
      }
      
      setFeedbackState('submitted')
      setSubmitStatus({ type: 'success', message: 'Thank you for your feedback!' })
      
      // Clear status after 3 seconds
      setTimeout(() => setSubmitStatus(null), 3000)
      
    } catch (error) {
      console.error('Failed to submit feedback:', error)
      setSubmitStatus({ type: 'error', message: 'Failed to submit feedback. Please try again.' })
      setTimeout(() => setSubmitStatus(null), 5000)
    }
  }

  const renderStarRating = (currentRating, onRatingChange, size = 'small') => {
    const starSize = size === 'small' ? 'h-4 w-4' : 'h-5 w-5'
    
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onClick={() => onRatingChange(star)}
            className={`${starSize} transition-colors ${
              star <= currentRating 
                ? 'text-yellow-400 fill-current' 
                : 'text-gray-400 hover:text-yellow-300'
            }`}
            disabled={feedbackState === 'submitted'}
          >
            <Star className={starSize} />
          </button>
        ))}
        <span className="text-xs text-gray-400 ml-2">
          {currentRating > 0 ? `${currentRating}/5` : 'Rate'}
        </span>
      </div>
    )
  }

  if (compact && feedbackState === 'idle') {
    return (
      <div className="flex items-center gap-2 mt-2">
        <Button
          size="sm"
          variant="ghost"
          onClick={() => handleQuickFeedback('helpful')}
          className="text-green-400 hover:text-green-300 hover:bg-green-500/20 h-8 px-2"
        >
          <ThumbsUp className="h-3 w-3" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          onClick={() => handleQuickFeedback('unhelpful')}
          className="text-red-400 hover:text-red-300 hover:bg-red-500/20 h-8 px-2"
        >
          <ThumbsDown className="h-3 w-3" />
        </Button>
        <div className="flex items-center gap-1">
          {renderStarRating(rating, handleStarRating, 'small')}
        </div>
        {rating > 0 && (
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setIsExpanded(true)}
            className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/20 h-8 px-2"
          >
            <MessageSquare className="h-3 w-3" />
          </Button>
        )}
      </div>
    )
  }

  if (compact && feedbackState === 'submitted') {
    return (
      <div className="flex items-center gap-2 mt-2">
        <CheckCircle className="h-4 w-4 text-green-400" />
        <span className="text-xs text-green-300">Feedback submitted</span>
        <div className="flex items-center gap-1">
          {renderStarRating(rating, () => {}, 'small')}
        </div>
      </div>
    )
  }

  return (
    <Card className="bg-white/5 backdrop-blur-sm border-white/10 mt-3">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-white text-sm flex items-center gap-2">
            <Brain className="h-4 w-4 text-purple-400" />
            Response Feedback
            {feedbackState === 'submitted' && (
              <CheckCircle className="h-4 w-4 text-green-400" />
            )}
          </CardTitle>
          {!compact && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-400 hover:text-gray-300"
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </Button>
          )}
        </div>
        <CardDescription className="text-gray-300 text-xs">
          Help the AGI learn and improve by rating this response
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Quick Feedback Buttons */}
        {feedbackState === 'idle' && (
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              onClick={() => handleQuickFeedback('helpful')}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <ThumbsUp className="h-3 w-3 mr-1" />
              Helpful
            </Button>
            <Button
              size="sm"
              onClick={() => handleQuickFeedback('unhelpful')}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              <ThumbsDown className="h-3 w-3 mr-1" />
              Not Helpful
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleQuickFeedback('unclear')}
              className="border-yellow-400 text-yellow-300 hover:bg-yellow-600 hover:text-white"
            >
              <AlertCircle className="h-3 w-3 mr-1" />
              Unclear
            </Button>
          </div>
        )}

        {/* Overall Rating */}
        {feedbackState !== 'idle' && (
          <div>
            <label className="text-white text-sm font-medium mb-2 block">Overall Rating</label>
            {renderStarRating(rating, handleStarRating, 'normal')}
          </div>
        )}

        {/* Detailed Aspects (Expanded View) */}
        {(isExpanded || !compact) && feedbackState !== 'idle' && (
          <div className="space-y-3">
            <h4 className="text-white text-sm font-medium">Rate Specific Aspects</h4>
            
            {Object.entries({
              accuracy: { label: 'Accuracy', icon: Target },
              clarity: { label: 'Clarity', icon: Lightbulb },
              completeness: { label: 'Completeness', icon: CheckCircle },
              reasoning: { label: 'Reasoning', icon: Brain },
              usefulness: { label: 'Usefulness', icon: TrendingUp }
            }).map(([aspect, { label, icon: Icon }]) => (
              <div key={aspect} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Icon className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-300 text-sm">{label}</span>
                </div>
                {renderStarRating(
                  specificAspects[aspect], 
                  (value) => handleAspectRating(aspect, value),
                  'small'
                )}
              </div>
            ))}
          </div>
        )}

        {/* Feedback Text */}
        {feedbackState !== 'idle' && (
          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Additional Comments (Optional)
            </label>
            <Textarea
              value={feedbackText}
              onChange={(e) => setFeedbackText(e.target.value)}
              placeholder="What could be improved? What did you like?"
              className="bg-black/20 border-white/20 text-white placeholder-gray-400 text-sm"
              rows={3}
              disabled={feedbackState === 'submitted'}
            />
          </div>
        )}

        {/* Submit Button */}
        {feedbackState === 'collecting' && (
          <div className="flex items-center gap-2">
            <Button
              onClick={handleSubmitFeedback}
              className="bg-purple-600 hover:bg-purple-700"
              disabled={rating === 0 && !feedbackText.trim()}
            >
              <Send className="h-3 w-3 mr-1" />
              Submit Feedback
            </Button>
            <Button
              variant="ghost"
              onClick={() => {
                setFeedbackState('idle')
                setRating(0)
                setFeedbackText('')
                setFeedbackType(null)
                setIsExpanded(false)
              }}
              className="text-gray-400 hover:text-gray-300"
            >
              Cancel
            </Button>
          </div>
        )}

        {/* Status Messages */}
        {submitStatus && (
          <div className={`flex items-center gap-2 p-2 rounded-lg text-sm ${
            submitStatus.type === 'success' ? 'bg-green-500/20 text-green-300' :
            submitStatus.type === 'error' ? 'bg-red-500/20 text-red-300' :
            'bg-blue-500/20 text-blue-300'
          }`}>
            {submitStatus.type === 'success' && <CheckCircle className="h-4 w-4" />}
            {submitStatus.type === 'error' && <AlertCircle className="h-4 w-4" />}
            {submitStatus.type === 'loading' && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            )}
            <span>{submitStatus.message}</span>
          </div>
        )}

        {/* Feedback Summary (After Submission) */}
        {feedbackState === 'submitted' && (
          <div className="bg-black/20 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span className="text-green-300 text-sm font-medium">Feedback Recorded</span>
            </div>
            <div className="text-gray-300 text-xs space-y-1">
              <div>Rating: {rating}/5 stars</div>
              {feedbackType && (
                <div>Type: <Badge variant="outline" className="text-xs">{feedbackType}</Badge></div>
              )}
              {feedbackText && (
                <div>Comment: "{feedbackText.substring(0, 100)}{feedbackText.length > 100 ? '...' : ''}"</div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default FeedbackCollector
