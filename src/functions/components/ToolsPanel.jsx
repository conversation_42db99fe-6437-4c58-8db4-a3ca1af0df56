// Tools Panel Component for displaying tool capabilities and usage
import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { 
  Wrench, 
  Search, 
  Code, 
  FileText, 
  Activity, 
  CheckCircle, 
  XCircle,
  Clock,
  Zap,
  Globe,
  Play,
  Settings
} from 'lucide-react'
import toolService from '../services/toolService'

const ToolsPanel = ({ className = '' }) => {
  const [availableTools, setAvailableTools] = useState([])
  const [usageStats, setUsageStats] = useState(null)
  const [recentExecutions, setRecentExecutions] = useState([])

  // Load tool information on mount
  useEffect(() => {
    try {
      const tools = toolService.getAvailableTools()
      setAvailableTools(tools)
      
      const stats = toolService.getUsageStats()
      setUsageStats(stats)
      setRecentExecutions(stats.recentExecutions || [])
      
      console.log('🔧 Tools panel loaded:', tools.length, 'tools')
    } catch (error) {
      console.error('❌ Failed to load tool information:', error)
    }
  }, [])

  // Update usage stats periodically
  useEffect(() => {
    const updateStats = () => {
      try {
        const stats = toolService.getUsageStats()
        setUsageStats(stats)
        setRecentExecutions(stats.recentExecutions || [])
      } catch (error) {
        console.error('❌ Failed to update tool stats:', error)
      }
    }

    const interval = setInterval(updateStats, 5000) // Update every 5 seconds
    return () => clearInterval(interval)
  }, [])

  // Get icon for tool
  const getToolIcon = (toolName) => {
    switch (toolName) {
      case 'web_search':
        return <Search className="h-4 w-4" />
      case 'code_execution':
        return <Code className="h-4 w-4" />
      case 'file_operations':
        return <FileText className="h-4 w-4" />
      default:
        return <Wrench className="h-4 w-4" />
    }
  }

  // Get color for tool
  const getToolColor = (toolName) => {
    switch (toolName) {
      case 'web_search':
        return 'text-blue-400'
      case 'code_execution':
        return 'text-green-400'
      case 'file_operations':
        return 'text-orange-400'
      default:
        return 'text-gray-400'
    }
  }

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString()
  }

  // Clear tool history
  const handleClearHistory = () => {
    toolService.clearHistory()
    setUsageStats(toolService.getUsageStats())
    setRecentExecutions([])
  }

  return (
    <Card className={`bg-white/10 backdrop-blur-sm border-white/20 ${className}`}>
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Wrench className="h-5 w-5 text-cyan-400" />
          AGI Tools System
        </CardTitle>
        <CardDescription className="text-gray-300">
          External capabilities and integrations
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Available Tools */}
        <div>
          <h4 className="text-white text-sm font-medium mb-3 flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Available Tools ({availableTools.length})
          </h4>
          <div className="space-y-2">
            {availableTools.map((tool, index) => (
              <div key={index} className="bg-black/20 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <span className={getToolColor(tool.name)}>
                    {getToolIcon(tool.name)}
                  </span>
                  <span className="text-white text-sm font-medium">
                    {tool.name.replace('_', ' ').toUpperCase()}
                  </span>
                  <Badge variant="outline" className="border-green-400 text-green-300 text-xs">
                    Active
                  </Badge>
                </div>
                <p className="text-gray-300 text-xs leading-relaxed">
                  {tool.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Usage Statistics */}
        {usageStats && (
          <div>
            <h4 className="text-white text-sm font-medium mb-3 flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Usage Statistics
            </h4>
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-black/20 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <Zap className="h-4 w-4 text-yellow-400" />
                  <span className="text-yellow-400 text-sm font-medium">Total</span>
                </div>
                <p className="text-white text-lg font-bold">{usageStats.totalExecutions}</p>
                <p className="text-gray-400 text-xs">Executions</p>
              </div>

              <div className="bg-black/20 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <CheckCircle className="h-4 w-4 text-green-400" />
                  <span className="text-green-400 text-sm font-medium">Success</span>
                </div>
                <p className="text-white text-lg font-bold">{usageStats.successfulExecutions}</p>
                <p className="text-gray-400 text-xs">
                  {usageStats.totalExecutions > 0 
                    ? Math.round((usageStats.successfulExecutions / usageStats.totalExecutions) * 100)
                    : 0}% rate
                </p>
              </div>
            </div>

            {/* Tool Usage Breakdown */}
            {Object.keys(usageStats.toolUsageCounts).length > 0 && (
              <div className="mt-3">
                <p className="text-gray-400 text-xs mb-2">Tool Usage Breakdown:</p>
                <div className="space-y-1">
                  {Object.entries(usageStats.toolUsageCounts).map(([toolName, count]) => (
                    <div key={toolName} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className={getToolColor(toolName)}>
                          {getToolIcon(toolName)}
                        </span>
                        <span className="text-gray-300 text-xs">
                          {toolName.replace('_', ' ')}
                        </span>
                      </div>
                      <Badge variant="secondary" className="bg-purple-600/20 text-purple-300 text-xs">
                        {count}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Recent Executions */}
        {recentExecutions.length > 0 && (
          <div>
            <h4 className="text-white text-sm font-medium mb-3 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Recent Activity ({recentExecutions.length})
            </h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {recentExecutions.slice().reverse().map((execution, index) => (
                <div key={index} className="bg-black/20 rounded-lg p-2">
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center gap-2">
                      <span className={getToolColor(execution.toolName)}>
                        {getToolIcon(execution.toolName)}
                      </span>
                      <span className="text-white text-xs font-medium">
                        {execution.toolName.replace('_', ' ')}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      {execution.success ? (
                        <CheckCircle className="h-3 w-3 text-green-400" />
                      ) : (
                        <XCircle className="h-3 w-3 text-red-400" />
                      )}
                      <span className="text-gray-400 text-xs">
                        {formatTimestamp(execution.timestamp)}
                      </span>
                    </div>
                  </div>
                  {!execution.success && execution.result?.error && (
                    <p className="text-red-300 text-xs">
                      Error: {execution.result.error}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Tool Controls */}
        <div className="space-y-2">
          <h4 className="text-white text-sm font-medium flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Tool Controls
          </h4>
          
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={handleClearHistory}
              size="sm"
              variant="outline"
              className="border-gray-400 text-gray-300 hover:bg-gray-600 hover:text-white"
            >
              <XCircle className="h-3 w-3 mr-1" />
              Clear History
            </Button>
          </div>
        </div>

        {/* Tool Status */}
        <div className="flex items-center justify-between pt-2 border-t border-white/10">
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="bg-cyan-600/20 text-cyan-300">
              Tools Active
            </Badge>
            <Badge variant="outline" className="border-green-400 text-green-300">
              {availableTools.length} Available
            </Badge>
          </div>
          <span className="text-gray-400 text-xs">
            Auto-detection: ON
          </span>
        </div>
      </CardContent>
    </Card>
  )
}

export default ToolsPanel
