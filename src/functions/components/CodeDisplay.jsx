// Code Display Component with Syntax Highlighting
import React, { useState, useEffect } from 'react'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Copy, Check, AlertTriangle, CheckCircle, Info } from 'lucide-react'
import 'highlight.js/styles/github-dark.css' // Dark theme for code highlighting

const CodeDisplay = ({ codeData, className = '' }) => {
  const [copied, setCopied] = useState(false)

  // Handle copy to clipboard
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(codeData.code)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy code:', error)
    }
  }

  // Reset copied state when code changes
  useEffect(() => {
    setCopied(false)
  }, [codeData.code])

  if (!codeData) {
    return null
  }

  const { code, language, highlightedCode, validation, explanation, metadata } = codeData

  return (
    <div className={`bg-black/30 rounded-lg overflow-hidden ${className}`}>
      {/* Header with language and actions */}
      <div className="flex items-center justify-between p-3 bg-black/20 border-b border-white/10">
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="bg-purple-600/20 text-purple-300">
            {language.toUpperCase()}
          </Badge>
          <span className="text-gray-400 text-sm">
            {metadata.lineCount} lines • {metadata.characterCount} chars • {metadata.estimatedComplexity} complexity
          </span>
        </div>
        <Button
          onClick={handleCopy}
          size="sm"
          variant="ghost"
          className="text-gray-400 hover:text-white"
        >
          {copied ? (
            <>
              <Check className="h-4 w-4 mr-1" />
              Copied!
            </>
          ) : (
            <>
              <Copy className="h-4 w-4 mr-1" />
              Copy
            </>
          )}
        </Button>
      </div>

      {/* Code content */}
      <div className="relative">
        <pre className="p-4 overflow-x-auto text-sm">
          <code 
            className={`language-${language}`}
            dangerouslySetInnerHTML={{ __html: highlightedCode }}
          />
        </pre>
      </div>

      {/* Validation results */}
      {validation && (
        <div className="p-3 bg-black/20 border-t border-white/10 space-y-2">
          {/* Validation status */}
          <div className="flex items-center gap-2">
            {validation.isValid ? (
              <CheckCircle className="h-4 w-4 text-green-400" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-red-400" />
            )}
            <span className={`text-sm font-medium ${validation.isValid ? 'text-green-400' : 'text-red-400'}`}>
              {validation.isValid ? 'Code validation passed' : 'Code validation failed'}
            </span>
          </div>

          {/* Errors */}
          {validation.errors.length > 0 && (
            <div className="space-y-1">
              <p className="text-red-400 text-sm font-medium">Errors:</p>
              {validation.errors.map((error, index) => (
                <p key={index} className="text-red-300 text-sm ml-4">• {error}</p>
              ))}
            </div>
          )}

          {/* Warnings */}
          {validation.warnings.length > 0 && (
            <div className="space-y-1">
              <p className="text-yellow-400 text-sm font-medium">Warnings:</p>
              {validation.warnings.map((warning, index) => (
                <p key={index} className="text-yellow-300 text-sm ml-4">• {warning}</p>
              ))}
            </div>
          )}

          {/* Suggestions */}
          {validation.suggestions.length > 0 && (
            <div className="space-y-1">
              <p className="text-blue-400 text-sm font-medium">Suggestions:</p>
              {validation.suggestions.map((suggestion, index) => (
                <p key={index} className="text-blue-300 text-sm ml-4">• {suggestion}</p>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Explanation */}
      {explanation && (
        <div className="p-3 bg-blue-500/10 border-t border-blue-500/20">
          <div className="flex items-start gap-2">
            <Info className="h-4 w-4 text-blue-400 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-blue-400 text-sm font-medium mb-1">Explanation:</p>
              <p className="text-blue-200 text-sm leading-relaxed">{explanation}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CodeDisplay
