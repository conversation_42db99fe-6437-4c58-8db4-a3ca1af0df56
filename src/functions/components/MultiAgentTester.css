/* Multi-Agent Tester Styles */
.multi-agent-tester {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f8fafc;
  min-height: 100vh;
}

/* Header */
.tester-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tester-header h2 {
  margin: 0 0 10px 0;
  color: #1f2937;
  font-size: 28px;
  font-weight: 700;
}

.tester-header p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

/* Content */
.tester-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

/* Controls */
.tester-controls {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.test-selection {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-selection label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.test-selection select {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #1f2937;
  cursor: pointer;
}

.test-selection select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.test-selection optgroup {
  font-weight: 600;
  color: #6b7280;
}

.test-selection option {
  padding: 8px;
  font-weight: normal;
}

.test-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.run-button,
.coverage-button,
.clear-button {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.run-button {
  background: #3b82f6;
  color: white;
  flex: 1;
}

.run-button:hover:not(:disabled) {
  background: #2563eb;
}

.run-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.coverage-button {
  background: #10b981;
  color: white;
}

.coverage-button:hover:not(:disabled) {
  background: #059669;
}

.clear-button {
  background: #f3f4f6;
  color: #6b7280;
}

.clear-button:hover:not(:disabled) {
  background: #e5e7eb;
  color: #374151;
}

/* Output */
.tester-output {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.logs-section,
.results-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.logs-section h3,
.results-section h3 {
  margin: 0;
  padding: 16px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.logs-container,
.results-container {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

/* Logs */
.no-logs,
.no-results {
  text-align: center;
  color: #9ca3af;
  font-style: italic;
  padding: 40px 20px;
}

.log-entry {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
  font-size: 13px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-timestamp {
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', monospace;
  min-width: 80px;
  font-size: 11px;
}

.log-message {
  color: #374151;
  flex: 1;
}

.log-entry.info .log-message {
  color: #374151;
}

.log-entry.success .log-message {
  color: #059669;
  font-weight: 500;
}

.log-entry.error .log-message {
  color: #dc2626;
  font-weight: 500;
}

/* Test Results */
.test-results h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

/* Demo Results */
.demo-summary {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
}

.result-stat {
  font-weight: 600;
  color: #374151;
}

.demo-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.demo-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
}

.demo-item.success {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.demo-item.failed {
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.demo-icon {
  font-size: 14px;
}

.demo-name {
  font-weight: 500;
  color: #374151;
  flex: 1;
}

.demo-error {
  font-size: 11px;
  color: #dc2626;
}

/* Test Suite Results */
.test-summary {
  margin-bottom: 20px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.stat-label {
  font-size: 11px;
  color: #6b7280;
  text-transform: uppercase;
  font-weight: 500;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
}

.stat-value.success {
  color: #059669;
}

.stat-value.failed {
  color: #dc2626;
}

/* Test Suites */
.test-suites {
  margin-bottom: 20px;
}

.test-suites h4 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 14px;
  font-weight: 600;
}

.suite-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  margin-bottom: 6px;
  font-size: 13px;
}

.suite-item.success {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.suite-item.failed {
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.suite-icon {
  font-size: 14px;
}

.suite-name {
  flex: 1;
  font-weight: 500;
  color: #374151;
}

.suite-stats {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 11px;
  color: #6b7280;
}

.suite-duration {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 11px;
  color: #6b7280;
  min-width: 40px;
  text-align: right;
}

/* Failed Tests */
.failed-tests h4 {
  margin: 0 0 12px 0;
  color: #dc2626;
  font-size: 14px;
  font-weight: 600;
}

.failed-test {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px 12px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  margin-bottom: 8px;
  font-size: 12px;
}

.test-suite {
  font-weight: 600;
  color: #374151;
}

.test-name {
  color: #6b7280;
}

.test-error {
  color: #dc2626;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* Coverage Results */
.coverage-summary {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.coverage-components,
.coverage-types {
  margin-bottom: 20px;
}

.coverage-components h4,
.coverage-types h4 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 14px;
  font-weight: 600;
}

.coverage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  border-bottom: 1px solid #f3f4f6;
  font-size: 13px;
}

.coverage-item:last-child {
  border-bottom: none;
}

.component-name,
.type-name {
  color: #374151;
  font-weight: 500;
}

.component-tests,
.type-tests {
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 11px;
}

/* Simple Result */
.simple-result {
  text-align: center;
  padding: 20px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
}

.simple-result.success {
  background: #f0fdf4;
  color: #059669;
  border: 1px solid #bbf7d0;
}

.simple-result.failed {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

/* Info Section */
.tester-info {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tester-info h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 12px;
}

.info-item {
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  font-size: 13px;
  color: #374151;
}

.info-item strong {
  color: #1f2937;
}

/* Responsive Design */
@media (max-width: 768px) {
  .multi-agent-tester {
    padding: 16px;
  }

  .tester-output {
    grid-template-columns: 1fr;
  }

  .test-actions {
    flex-direction: column;
  }

  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}
