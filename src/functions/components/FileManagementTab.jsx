import React, { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Textarea } from '../../components/ui/textarea'
import { 
  FileText, 
  Upload, 
  Download, 
  Trash2, 
  File, 
  FileCode, 
  FileSpreadsheet,
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
  Edit3,
  Save
} from 'lucide-react'
import fileOperationsTool from '../tools/fileOperations'

const FileManagementTab = () => {
  const [uploadedFiles, setUploadedFiles] = useState([])
  const [selectedFile, setSelectedFile] = useState(null)
  const [fileContent, setFileContent] = useState('')
  const [isEditing, setIsEditing] = useState(false)
  const [uploadStatus, setUploadStatus] = useState(null)
  const [processingFile, setProcessingFile] = useState(null)
  const fileInputRef = useRef(null)

  // Load stored files on component mount
  useEffect(() => {
    loadStoredFiles()
  }, [])

  const loadStoredFiles = async () => {
    try {
      const result = await fileOperationsTool.handleFileOperation('list')
      if (result.success) {
        setUploadedFiles(result.files || [])
        console.log('📁 Loaded stored files:', result.files?.length || 0)
      }
    } catch (error) {
      console.error('❌ Failed to load stored files:', error)
    }
  }

  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files)
    if (files.length === 0) return

    setUploadStatus({ type: 'loading', message: 'Uploading files...' })

    try {
      for (const file of files) {
        setProcessingFile(file.name)
        
        // Read file content
        const readResult = await fileOperationsTool.handleFileOperation('read', file)
        
        if (readResult.success) {
          // Store file in localStorage
          const storeResult = await fileOperationsTool.handleFileOperation(
            'store', 
            file.name, 
            readResult.content
          )
          
          if (storeResult.success) {
            console.log('✅ File uploaded and stored:', file.name)
          }
        } else {
          throw new Error(readResult.error)
        }
      }

      setUploadStatus({ 
        type: 'success', 
        message: `Successfully uploaded ${files.length} file(s)` 
      })
      
      // Reload file list
      await loadStoredFiles()
      
    } catch (error) {
      console.error('❌ File upload failed:', error)
      setUploadStatus({ 
        type: 'error', 
        message: `Upload failed: ${error.message}` 
      })
    } finally {
      setProcessingFile(null)
      // Clear status after 3 seconds
      setTimeout(() => setUploadStatus(null), 3000)
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleFileSelect = async (fileName) => {
    try {
      const result = await fileOperationsTool.handleFileOperation('retrieve', fileName)
      if (result.success) {
        // The result contains the file data directly, not nested in fileData
        const fileData = {
          fileName: result.fileName,
          content: result.content,
          size: result.size,
          timestamp: result.timestamp
        }
        setSelectedFile(fileData)
        setFileContent(result.content)
        setIsEditing(false)
        console.log('📖 File selected:', fileName)
      } else {
        console.error('❌ Failed to load file:', result.error)
      }
    } catch (error) {
      console.error('❌ Error selecting file:', error)
    }
  }

  const handleFileSave = async () => {
    if (!selectedFile) return

    try {
      const result = await fileOperationsTool.handleFileOperation(
        'store',
        selectedFile.fileName,
        fileContent
      )
      
      if (result.success) {
        setIsEditing(false)
        setUploadStatus({ type: 'success', message: 'File saved successfully' })
        await loadStoredFiles()
        console.log('💾 File saved:', selectedFile.fileName)
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error('❌ Failed to save file:', error)
      setUploadStatus({ type: 'error', message: `Save failed: ${error.message}` })
    }
    
    setTimeout(() => setUploadStatus(null), 3000)
  }

  const handleFileDownload = async (fileName) => {
    try {
      const result = await fileOperationsTool.handleFileOperation('retrieve', fileName)
      if (result.success) {
        const downloadResult = await fileOperationsTool.handleFileOperation(
          'write',
          fileName,
          result.content,  // Fixed: use result.content directly
          'text/plain'
        )

        if (downloadResult.success) {
          setUploadStatus({ type: 'success', message: 'File downloaded successfully' })
          console.log('⬇️ File downloaded:', fileName)
        }
      }
    } catch (error) {
      console.error('❌ Download failed:', error)
      setUploadStatus({ type: 'error', message: `Download failed: ${error.message}` })
    }

    setTimeout(() => setUploadStatus(null), 3000)
  }

  const handleFileDelete = async (fileName) => {
    if (!confirm(`Are you sure you want to delete "${fileName}"?`)) return

    try {
      const result = await fileOperationsTool.handleFileOperation('delete', fileName)
      if (result.success) {
        setUploadStatus({ type: 'success', message: 'File deleted successfully' })
        await loadStoredFiles()
        
        // Clear selected file if it was deleted
        if (selectedFile && selectedFile.fileName === fileName) {
          setSelectedFile(null)
          setFileContent('')
          setIsEditing(false)
        }
        
        console.log('🗑️ File deleted:', fileName)
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error('❌ Delete failed:', error)
      setUploadStatus({ type: 'error', message: `Delete failed: ${error.message}` })
    }
    
    setTimeout(() => setUploadStatus(null), 3000)
  }

  const getFileIcon = (fileName) => {
    const extension = fileName.split('.').pop()?.toLowerCase()
    switch (extension) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
      case 'py':
      case 'html':
      case 'css':
        return <FileCode className="h-4 w-4" />
      case 'csv':
      case 'xlsx':
        return <FileSpreadsheet className="h-4 w-4" />
      default:
        return <File className="h-4 w-4" />
    }
  }

  const formatFileSize = (size) => {
    if (size < 1024) return `${size} B`
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
    return `${(size / (1024 * 1024)).toFixed(1)} MB`
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* File Upload and List */}
      <Card className="bg-white/10 backdrop-blur-sm border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <FileText className="h-5 w-5" />
            File Management
          </CardTitle>
          <CardDescription className="text-gray-300">
            Upload, manage, and process your files
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Upload Area */}
          <div className="border-2 border-dashed border-white/20 rounded-lg p-6 text-center">
            <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-300 mb-2">Drag & drop files here or click to browse</p>
            <p className="text-gray-400 text-sm mb-4">
              Supported: .txt, .csv, .json, .md, .html, .css, .js (max 1MB)
            </p>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".txt,.csv,.json,.md,.html,.css,.js,.jsx,.ts,.tsx,.py"
              onChange={handleFileUpload}
              className="hidden"
            />
            <Button
              onClick={() => fileInputRef.current?.click()}
              className="bg-purple-600 hover:bg-purple-700"
              disabled={!!processingFile}
            >
              {processingFile ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Processing {processingFile}...
                </div>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Choose Files
                </>
              )}
            </Button>
          </div>

          {/* Upload Status */}
          {uploadStatus && (
            <div className={`flex items-center gap-2 p-3 rounded-lg ${
              uploadStatus.type === 'success' ? 'bg-green-500/20 text-green-300' :
              uploadStatus.type === 'error' ? 'bg-red-500/20 text-red-300' :
              'bg-blue-500/20 text-blue-300'
            }`}>
              {uploadStatus.type === 'success' && <CheckCircle className="h-4 w-4" />}
              {uploadStatus.type === 'error' && <AlertCircle className="h-4 w-4" />}
              {uploadStatus.type === 'loading' && <Clock className="h-4 w-4 animate-spin" />}
              <span className="text-sm">{uploadStatus.message}</span>
            </div>
          )}

          {/* File List */}
          <div>
            <h4 className="text-white text-sm font-medium mb-3">
              Uploaded Files ({uploadedFiles.length})
            </h4>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {uploadedFiles.length === 0 ? (
                <p className="text-gray-400 text-sm text-center py-4">
                  No files uploaded yet
                </p>
              ) : (
                uploadedFiles.map((file, index) => (
                  <div
                    key={index}
                    className={`flex items-center justify-between p-3 rounded-lg border transition-colors cursor-pointer ${
                      selectedFile?.fileName === file.fileName
                        ? 'bg-purple-500/20 border-purple-400'
                        : 'bg-black/20 border-white/10 hover:bg-white/5'
                    }`}
                    onClick={() => handleFileSelect(file.fileName)}
                  >
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      {getFileIcon(file.fileName)}
                      <div className="min-w-0 flex-1">
                        <p className="text-white text-sm font-medium truncate">
                          {file.fileName}
                        </p>
                        <p className="text-gray-400 text-xs">
                          {formatFileSize(file.size)} • {new Date(file.timestamp).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleFileDownload(file.fileName)
                        }}
                        className="h-8 w-8 p-0 text-blue-400 hover:text-blue-300 hover:bg-blue-500/20"
                      >
                        <Download className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleFileDelete(file.fileName)
                        }}
                        className="h-8 w-8 p-0 text-red-400 hover:text-red-300 hover:bg-red-500/20"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* File Viewer/Editor */}
      <Card className="bg-white/10 backdrop-blur-sm border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Eye className="h-5 w-5" />
            File Viewer
            {selectedFile && (
              <Badge variant="outline" className="ml-2 border-purple-400 text-purple-300">
                {selectedFile.fileName}
              </Badge>
            )}
          </CardTitle>
          <CardDescription className="text-gray-300">
            View and edit file contents
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {selectedFile ? (
            <>
              {/* File Info */}
              <div className="bg-black/20 rounded-lg p-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Size:</span>
                    <span className="text-white ml-2">{formatFileSize(selectedFile.size)}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Modified:</span>
                    <span className="text-white ml-2">
                      {new Date(selectedFile.timestamp).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Editor Controls */}
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsEditing(!isEditing)}
                  className="border-blue-400 text-blue-300 hover:bg-blue-600 hover:text-white"
                >
                  {isEditing ? (
                    <>
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </>
                  ) : (
                    <>
                      <Edit3 className="h-3 w-3 mr-1" />
                      Edit
                    </>
                  )}
                </Button>
                
                {isEditing && (
                  <Button
                    size="sm"
                    onClick={handleFileSave}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Save className="h-3 w-3 mr-1" />
                    Save
                  </Button>
                )}
              </div>

              {/* File Content */}
              <div className="bg-black/30 rounded-lg p-4">
                {isEditing ? (
                  <Textarea
                    value={fileContent}
                    onChange={(e) => setFileContent(e.target.value)}
                    className="bg-transparent border-none text-white font-mono text-sm resize-none"
                    rows={20}
                    placeholder="File content..."
                  />
                ) : (
                  <pre className="text-white font-mono text-sm whitespace-pre-wrap overflow-auto max-h-96">
                    {fileContent}
                  </pre>
                )}
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-400">Select a file to view its contents</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* File Viewer/Editor */}
      <Card className="bg-white/10 backdrop-blur-sm border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Eye className="h-5 w-5" />
            File Viewer
            {selectedFile && (
              <Badge variant="outline" className="ml-2 border-purple-400 text-purple-300">
                {selectedFile.fileName}
              </Badge>
            )}
          </CardTitle>
          <CardDescription className="text-gray-300">
            View and edit file contents
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {selectedFile ? (
            <>
              {/* File Info */}
              <div className="bg-black/20 rounded-lg p-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Size:</span>
                    <span className="text-white ml-2">{formatFileSize(selectedFile.size)}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Modified:</span>
                    <span className="text-white ml-2">
                      {new Date(selectedFile.timestamp).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Editor Controls */}
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsEditing(!isEditing)}
                  className="border-blue-400 text-blue-300 hover:bg-blue-600 hover:text-white"
                >
                  {isEditing ? (
                    <>
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </>
                  ) : (
                    <>
                      <Edit3 className="h-3 w-3 mr-1" />
                      Edit
                    </>
                  )}
                </Button>

                {isEditing && (
                  <Button
                    size="sm"
                    onClick={handleFileSave}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Save className="h-3 w-3 mr-1" />
                    Save
                  </Button>
                )}
              </div>

              {/* File Content */}
              <div className="bg-black/30 rounded-lg p-4">
                {isEditing ? (
                  <Textarea
                    value={fileContent}
                    onChange={(e) => setFileContent(e.target.value)}
                    className="bg-transparent border-none text-white font-mono text-sm resize-none"
                    rows={20}
                    placeholder="File content..."
                  />
                ) : (
                  <pre className="text-white font-mono text-sm whitespace-pre-wrap overflow-auto max-h-96">
                    {fileContent}
                  </pre>
                )}
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-400">Select a file to view its contents</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default FileManagementTab
