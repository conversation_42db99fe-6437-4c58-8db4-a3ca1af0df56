// Multi-Agent Visualization - UI components for visualizing agent collaboration
import React, { useState, useEffect, useRef } from 'react'
import './MultiAgentVisualization.css'

const MultiAgentVisualization = ({ 
  agentData = [], 
  collaborationData = {}, 
  performanceData = {},
  onAgentSelect = () => {},
  showPerformanceMetrics = true,
  showCollaborationFlow = true 
}) => {
  const [selectedAgent, setSelectedAgent] = useState(null)
  const [viewMode, setViewMode] = useState('overview') // overview, performance, collaboration, flow
  const [animationEnabled, setAnimationEnabled] = useState(true)
  const canvasRef = useRef(null)

  // Agent status colors
  const getAgentStatusColor = (status) => {
    const colors = {
      'idle': '#6b7280',
      'working': '#3b82f6',
      'collaborating': '#10b981',
      'error': '#ef4444',
      'completed': '#8b5cf6'
    }
    return colors[status] || colors.idle
  }

  // Agent type icons
  const getAgentIcon = (type) => {
    const icons = {
      'research': '🔍',
      'coding': '💻',
      'analysis': '📊',
      'creative': '🎨',
      'coordinator': '🎯'
    }
    return icons[type] || '🤖'
  }

  // Handle agent selection
  const handleAgentClick = (agent) => {
    setSelectedAgent(agent)
    onAgentSelect(agent)
  }

  // Render agent card
  const renderAgentCard = (agent) => (
    <div
      key={agent.id}
      className={`agent-card ${selectedAgent?.id === agent.id ? 'selected' : ''}`}
      onClick={() => handleAgentClick(agent)}
      style={{ borderColor: getAgentStatusColor(agent.status) }}
    >
      <div className="agent-header">
        <span className="agent-icon">{getAgentIcon(agent.type)}</span>
        <div className="agent-info">
          <h4 className="agent-name">{agent.name || `${agent.type} Agent`}</h4>
          <span className="agent-type">{agent.type}</span>
        </div>
        <div 
          className="agent-status-indicator"
          style={{ backgroundColor: getAgentStatusColor(agent.status) }}
        />
      </div>
      
      <div className="agent-metrics">
        <div className="metric">
          <span className="metric-label">Tasks</span>
          <span className="metric-value">{agent.currentTasks || 0}</span>
        </div>
        <div className="metric">
          <span className="metric-label">Success</span>
          <span className="metric-value">{Math.round((agent.performance?.successRate || 0) * 100)}%</span>
        </div>
        <div className="metric">
          <span className="metric-label">Collab</span>
          <span className="metric-value">{agent.collaborations?.length || 0}</span>
        </div>
      </div>
      
      {agent.currentTask && (
        <div className="current-task">
          <span className="task-label">Current:</span>
          <span className="task-description">{agent.currentTask.substring(0, 30)}...</span>
        </div>
      )}
    </div>
  )

  // Render collaboration flow
  const renderCollaborationFlow = () => {
    if (!collaborationData.activeCollaborations) return null

    return (
      <div className="collaboration-flow">
        <h3>Active Collaborations</h3>
        {Object.entries(collaborationData.activeCollaborations).map(([id, collab]) => (
          <div key={id} className="collaboration-item">
            <div className="collaboration-header">
              <span className="collaboration-id">#{id.slice(-6)}</span>
              <span className="collaboration-status">{collab.status}</span>
            </div>
            <div className="collaboration-participants">
              {collab.participants?.map(participantId => {
                const agent = agentData.find(a => a.id === participantId)
                return agent ? (
                  <div key={participantId} className="participant">
                    <span className="participant-icon">{getAgentIcon(agent.type)}</span>
                    <span className="participant-name">{agent.type}</span>
                  </div>
                ) : null
              })}
            </div>
            <div className="collaboration-progress">
              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ width: `${(collab.progress || 0) * 100}%` }}
                />
              </div>
              <span className="progress-text">{Math.round((collab.progress || 0) * 100)}%</span>
            </div>
          </div>
        ))}
      </div>
    )
  }

  // Render performance dashboard
  const renderPerformanceDashboard = () => (
    <div className="performance-dashboard">
      <h3>System Performance</h3>
      <div className="performance-grid">
        <div className="performance-metric">
          <div className="metric-header">
            <span className="metric-title">Success Rate</span>
            <span className="metric-trend">
              {performanceData.trends?.successRate === 'improving' ? '📈' : 
               performanceData.trends?.successRate === 'declining' ? '📉' : '➡️'}
            </span>
          </div>
          <div className="metric-value-large">
            {Math.round((performanceData.successRate || 0) * 100)}%
          </div>
          <div className="metric-bar">
            <div 
              className="metric-bar-fill success"
              style={{ width: `${(performanceData.successRate || 0) * 100}%` }}
            />
          </div>
        </div>

        <div className="performance-metric">
          <div className="metric-header">
            <span className="metric-title">Response Time</span>
            <span className="metric-trend">
              {performanceData.trends?.responseTime === 'improving' ? '📈' : 
               performanceData.trends?.responseTime === 'declining' ? '📉' : '➡️'}
            </span>
          </div>
          <div className="metric-value-large">
            {Math.round((performanceData.averageResponseTime || 0) / 1000)}s
          </div>
          <div className="metric-bar">
            <div 
              className="metric-bar-fill response-time"
              style={{ width: `${Math.min(100, (performanceData.averageResponseTime || 0) / 200)}%` }}
            />
          </div>
        </div>

        <div className="performance-metric">
          <div className="metric-header">
            <span className="metric-title">Collaboration</span>
            <span className="metric-trend">
              {performanceData.trends?.collaboration === 'improving' ? '📈' : 
               performanceData.trends?.collaboration === 'declining' ? '📉' : '➡️'}
            </span>
          </div>
          <div className="metric-value-large">
            {Math.round((performanceData.collaborationEfficiency || 0) * 100)}%
          </div>
          <div className="metric-bar">
            <div 
              className="metric-bar-fill collaboration"
              style={{ width: `${(performanceData.collaborationEfficiency || 0) * 100}%` }}
            />
          </div>
        </div>

        <div className="performance-metric">
          <div className="metric-header">
            <span className="metric-title">Active Agents</span>
            <span className="metric-trend">🤖</span>
          </div>
          <div className="metric-value-large">
            {performanceData.activeAgents || 0}
          </div>
          <div className="metric-subtitle">
            {agentData.filter(a => a.status === 'working').length} working
          </div>
        </div>
      </div>

      {performanceData.alerts && performanceData.alerts.length > 0 && (
        <div className="alerts-section">
          <h4>System Alerts</h4>
          {performanceData.alerts.slice(0, 3).map((alert, index) => (
            <div key={index} className={`alert alert-${alert.severity}`}>
              <span className="alert-icon">
                {alert.severity === 'critical' ? '🚨' : 
                 alert.severity === 'warning' ? '⚠️' : 'ℹ️'}
              </span>
              <span className="alert-message">{alert.message}</span>
              <span className="alert-time">
                {new Date(alert.timestamp).toLocaleTimeString()}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  )

  // Render agent details panel
  const renderAgentDetails = () => {
    if (!selectedAgent) return null

    return (
      <div className="agent-details-panel">
        <div className="agent-details-header">
          <div className="agent-details-title">
            <span className="agent-icon-large">{getAgentIcon(selectedAgent.type)}</span>
            <div>
              <h3>{selectedAgent.name || `${selectedAgent.type} Agent`}</h3>
              <span className="agent-id">ID: {selectedAgent.id}</span>
            </div>
          </div>
          <button 
            className="close-details"
            onClick={() => setSelectedAgent(null)}
          >
            ✕
          </button>
        </div>

        <div className="agent-details-content">
          <div className="details-section">
            <h4>Status</h4>
            <div className="status-info">
              <span 
                className="status-indicator"
                style={{ backgroundColor: getAgentStatusColor(selectedAgent.status) }}
              />
              <span className="status-text">{selectedAgent.status}</span>
            </div>
          </div>

          <div className="details-section">
            <h4>Performance</h4>
            <div className="performance-stats">
              <div className="stat">
                <span className="stat-label">Success Rate:</span>
                <span className="stat-value">
                  {Math.round((selectedAgent.performance?.successRate || 0) * 100)}%
                </span>
              </div>
              <div className="stat">
                <span className="stat-label">Avg Completion:</span>
                <span className="stat-value">
                  {Math.round((selectedAgent.performance?.averageCompletionTime || 0) / 1000)}s
                </span>
              </div>
              <div className="stat">
                <span className="stat-label">Tasks Completed:</span>
                <span className="stat-value">
                  {selectedAgent.performance?.tasksCompleted || 0}
                </span>
              </div>
              <div className="stat">
                <span className="stat-label">Collaboration Rating:</span>
                <span className="stat-value">
                  {Math.round((selectedAgent.performance?.collaborationRating || 0) * 100)}%
                </span>
              </div>
            </div>
          </div>

          <div className="details-section">
            <h4>Capabilities</h4>
            <div className="capabilities-list">
              {selectedAgent.capabilities?.map(capability => (
                <span key={capability} className="capability-tag">
                  {capability}
                </span>
              )) || <span className="no-data">No capabilities data</span>}
            </div>
          </div>

          <div className="details-section">
            <h4>Current Tasks</h4>
            <div className="current-tasks-list">
              {selectedAgent.currentTasks?.length > 0 ? 
                selectedAgent.currentTasks.map((task, index) => (
                  <div key={index} className="task-item">
                    <span className="task-priority">{task.priority || 'medium'}</span>
                    <span className="task-description">{task.description}</span>
                  </div>
                )) : 
                <span className="no-data">No active tasks</span>
              }
            </div>
          </div>

          <div className="details-section">
            <h4>Recent Activity</h4>
            <div className="activity-list">
              {selectedAgent.recentActivity?.slice(0, 5).map((activity, index) => (
                <div key={index} className="activity-item">
                  <span className="activity-time">
                    {new Date(activity.timestamp).toLocaleTimeString()}
                  </span>
                  <span className="activity-description">{activity.description}</span>
                </div>
              )) || <span className="no-data">No recent activity</span>}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Render view mode selector
  const renderViewModeSelector = () => (
    <div className="view-mode-selector">
      <button 
        className={`mode-button ${viewMode === 'overview' ? 'active' : ''}`}
        onClick={() => setViewMode('overview')}
      >
        📊 Overview
      </button>
      <button 
        className={`mode-button ${viewMode === 'performance' ? 'active' : ''}`}
        onClick={() => setViewMode('performance')}
      >
        📈 Performance
      </button>
      <button 
        className={`mode-button ${viewMode === 'collaboration' ? 'active' : ''}`}
        onClick={() => setViewMode('collaboration')}
      >
        🤝 Collaboration
      </button>
      <button 
        className={`mode-button ${viewMode === 'flow' ? 'active' : ''}`}
        onClick={() => setViewMode('flow')}
      >
        🔄 Flow
      </button>
    </div>
  )

  // Render network visualization
  const renderNetworkVisualization = () => {
    useEffect(() => {
      if (viewMode === 'flow' && canvasRef.current) {
        drawNetworkVisualization()
      }
    }, [viewMode, agentData, collaborationData])

    const drawNetworkVisualization = () => {
      const canvas = canvasRef.current
      if (!canvas) return

      const ctx = canvas.getContext('2d')
      const rect = canvas.getBoundingClientRect()
      canvas.width = rect.width
      canvas.height = rect.height

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw agents as nodes
      const nodeRadius = 30
      const centerX = canvas.width / 2
      const centerY = canvas.height / 2
      const radius = Math.min(canvas.width, canvas.height) / 3

      agentData.forEach((agent, index) => {
        const angle = (index / agentData.length) * 2 * Math.PI
        const x = centerX + Math.cos(angle) * radius
        const y = centerY + Math.sin(angle) * radius

        // Draw connections to collaborating agents
        if (agent.collaborations) {
          agent.collaborations.forEach(collabId => {
            const collaboratingAgents = agentData.filter(a => 
              a.collaborations?.includes(collabId) && a.id !== agent.id
            )
            
            collaboratingAgents.forEach(collabAgent => {
              const collabIndex = agentData.findIndex(a => a.id === collabAgent.id)
              if (collabIndex !== -1) {
                const collabAngle = (collabIndex / agentData.length) * 2 * Math.PI
                const collabX = centerX + Math.cos(collabAngle) * radius
                const collabY = centerY + Math.sin(collabAngle) * radius

                // Draw connection line
                ctx.beginPath()
                ctx.moveTo(x, y)
                ctx.lineTo(collabX, collabY)
                ctx.strokeStyle = '#10b981'
                ctx.lineWidth = 2
                ctx.stroke()
              }
            })
          })
        }

        // Draw agent node
        ctx.beginPath()
        ctx.arc(x, y, nodeRadius, 0, 2 * Math.PI)
        ctx.fillStyle = getAgentStatusColor(agent.status)
        ctx.fill()
        ctx.strokeStyle = '#ffffff'
        ctx.lineWidth = 3
        ctx.stroke()

        // Draw agent icon
        ctx.fillStyle = '#ffffff'
        ctx.font = '20px Arial'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillText(getAgentIcon(agent.type), x, y)

        // Draw agent label
        ctx.fillStyle = '#374151'
        ctx.font = '12px Arial'
        ctx.fillText(agent.type, x, y + nodeRadius + 15)
      })
    }

    return (
      <div className="network-visualization">
        <h3>Agent Collaboration Network</h3>
        <canvas 
          ref={canvasRef}
          className="network-canvas"
          style={{ width: '100%', height: '400px' }}
        />
        <div className="network-legend">
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: '#6b7280' }}></div>
            <span>Idle</span>
          </div>
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: '#3b82f6' }}></div>
            <span>Working</span>
          </div>
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: '#10b981' }}></div>
            <span>Collaborating</span>
          </div>
          <div className="legend-item">
            <div className="legend-line"></div>
            <span>Active Collaboration</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="multi-agent-visualization">
      <div className="visualization-header">
        <h2>Multi-Agent System</h2>
        {renderViewModeSelector()}
        <div className="header-controls">
          <label className="animation-toggle">
            <input 
              type="checkbox" 
              checked={animationEnabled}
              onChange={(e) => setAnimationEnabled(e.target.checked)}
            />
            Animations
          </label>
        </div>
      </div>

      <div className="visualization-content">
        {viewMode === 'overview' && (
          <div className="overview-mode">
            <div className="agents-grid">
              {agentData.map(renderAgentCard)}
            </div>
            {showPerformanceMetrics && (
              <div className="overview-metrics">
                <div className="metric-summary">
                  <div className="summary-item">
                    <span className="summary-label">Total Agents:</span>
                    <span className="summary-value">{agentData.length}</span>
                  </div>
                  <div className="summary-item">
                    <span className="summary-label">Active:</span>
                    <span className="summary-value">
                      {agentData.filter(a => a.status === 'working' || a.status === 'collaborating').length}
                    </span>
                  </div>
                  <div className="summary-item">
                    <span className="summary-label">Collaborations:</span>
                    <span className="summary-value">
                      {Object.keys(collaborationData.activeCollaborations || {}).length}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {viewMode === 'performance' && renderPerformanceDashboard()}

        {viewMode === 'collaboration' && (
          <div className="collaboration-mode">
            {renderCollaborationFlow()}
            <div className="collaboration-stats">
              <h3>Collaboration Statistics</h3>
              <div className="stats-grid">
                <div className="stat-card">
                  <span className="stat-title">Active Collaborations</span>
                  <span className="stat-number">
                    {Object.keys(collaborationData.activeCollaborations || {}).length}
                  </span>
                </div>
                <div className="stat-card">
                  <span className="stat-title">Success Rate</span>
                  <span className="stat-number">
                    {Math.round((collaborationData.successRate || 0) * 100)}%
                  </span>
                </div>
                <div className="stat-card">
                  <span className="stat-title">Avg Duration</span>
                  <span className="stat-number">
                    {Math.round((collaborationData.averageDuration || 0) / 1000)}s
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {viewMode === 'flow' && renderNetworkVisualization()}
      </div>

      {selectedAgent && renderAgentDetails()}
    </div>
  )
}

export default MultiAgentVisualization
