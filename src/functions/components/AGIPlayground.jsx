import { Button } from '../../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Textarea } from '../../components/ui/textarea'
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../../components/ui/tabs'
import { Badge } from '../../components/ui/badge'
import { Brain, Zap, Clock, MessageSquare, Code, Image, BarChart3, Wifi, WifiOff, AlertCircle, Settings, FileText, Upload, Download, Trash2 } from 'lucide-react'
import { useAGIPlayground } from '../logic/useAGIPlayground'
import { checkAPIConfiguration, getAPIUsageStats, testAPIConnection } from '../logic/agiPlaygroundLogic'
import APISetupGuide from './APISetupGuide'
import CodeDisplay from './CodeDisplay'
import AnalysisDisplay from './AnalysisDisplay'
import MemoryPanel from './MemoryPanel'
import ToolsPanel from './ToolsPanel'
import FileManagementTab from './FileManagementTab'
import ReasoningDisplay from './ReasoningDisplay'
import FeedbackCollector from './FeedbackCollector'
import LearningDashboard from './LearningDashboard'
import learningService from '../services/learningService'
import { useState, useEffect } from 'react'

const AGIPlayground = ({ NavigationHeader }) => {
  const {
    playgroundMode,
    playgroundInput,
    playgroundOutput,
    isProcessing,
    conversationHistory,
    memoryStats,
    setPlaygroundMode,
    setPlaygroundInput,
    handleSubmit,
    clearMemory,
    exportMemory,
    importMemory
  } = useAGIPlayground()

  // API status monitoring
  const [apiStatus, setApiStatus] = useState({ isConfigured: false, hasKey: false })
  const [usageStats, setUsageStats] = useState({ requestCount: 0 })
  const [showSetupGuide, setShowSetupGuide] = useState(false)
  const [testResult, setTestResult] = useState(null)

  // Reasoning display state
  const [visibleReasoning, setVisibleReasoning] = useState(new Set())

  // Learning dashboard state
  const [showLearningDashboard, setShowLearningDashboard] = useState(false)

  // Toggle reasoning visibility for a specific message
  const toggleReasoningVisibility = (messageIndex) => {
    const newVisible = new Set(visibleReasoning)
    if (newVisible.has(messageIndex)) {
      newVisible.delete(messageIndex)
    } else {
      newVisible.add(messageIndex)
    }
    setVisibleReasoning(newVisible)
  }

  // Handle feedback submission
  const handleFeedbackSubmit = async (feedbackData) => {
    console.log('📝 Submitting feedback:', feedbackData)

    try {
      const result = await learningService.storeFeedback(feedbackData)

      if (result.success) {
        console.log('✅ Feedback submitted successfully')
        // Could show a toast notification here
      } else {
        console.error('❌ Failed to submit feedback:', result.error)
        throw new Error(result.error)
      }

      return result
    } catch (error) {
      console.error('❌ Error submitting feedback:', error)
      throw error
    }
  }

  // Test API connection
  const handleTestAPI = async () => {
    setTestResult({ testing: true })
    try {
      const result = await testAPIConnection()
      setTestResult(result)
      console.log('🧪 API Test Result:', result)
    } catch (error) {
      setTestResult({ success: false, message: 'Test failed', error: error.message })
      console.error('🧪 API Test Error:', error)
    }
  }

  useEffect(() => {
    // Check API configuration on component mount
    const config = checkAPIConfiguration()
    setApiStatus(config)

    // Initialize learning service
    const initializeLearning = async () => {
      try {
        await learningService.initialize()
        console.log('🧠 Learning service initialized in AGI Playground')
      } catch (error) {
        console.error('❌ Failed to initialize learning service:', error)
      }
    }

    initializeLearning()

    // Update usage stats periodically
    const updateStats = () => {
      const stats = getAPIUsageStats()
      setUsageStats(stats)
    }

    updateStats()
    const interval = setInterval(updateStats, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      <NavigationHeader />
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Brain className="h-12 w-12 text-purple-400 mr-4" />
            <div className="flex flex-col items-center">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
                AGI Playground
              </h1>
              {/* API Status Indicator */}
              <div className="flex items-center gap-2 mt-2">
                {apiStatus.isConfigured ? (
                  <Badge variant="outline" className="border-green-400 text-green-300 flex items-center gap-1">
                    <Wifi className="h-3 w-3" />
                    Gemini API Connected
                  </Badge>
                ) : (
                  <Badge
                    variant="outline"
                    className="border-red-400 text-red-300 flex items-center gap-1 cursor-pointer hover:bg-red-900/20"
                    onClick={() => setShowSetupGuide(true)}
                  >
                    <WifiOff className="h-3 w-3" />
                    API Not Configured
                  </Badge>
                )}
                <Badge variant="outline" className="border-blue-400 text-blue-300">
                  {usageStats.requestCount} requests
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSetupGuide(true)}
                  className="text-gray-400 hover:text-white h-6 px-2"
                >
                  <Settings className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            {apiStatus.isConfigured
              ? "Powered by Google Gemini - Real AGI Intelligence"
              : "Configure your Gemini API key to enable real AGI responses"
            }
          </p>
          {!apiStatus.isConfigured && (
            <div className="mt-4 p-4 bg-yellow-900/20 border border-yellow-600/30 rounded-lg max-w-md mx-auto">
              <div className="flex items-center gap-2 text-yellow-300">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">
                  Add your Gemini API key to .env file to enable real AGI responses
                </span>
              </div>
            </div>
          )}

          {/* API Test Section */}
          {apiStatus.hasKey && (
            <div className="mt-4 flex flex-col items-center gap-2">
              <Button
                onClick={handleTestAPI}
                disabled={testResult?.testing}
                variant="outline"
                size="sm"
                className="border-purple-400 text-purple-300 hover:bg-purple-600 hover:text-white"
              >
                {testResult?.testing ? 'Testing API...' : 'Test API Connection'}
              </Button>

              {testResult && !testResult.testing && (
                <div className={`text-sm p-2 rounded ${
                  testResult.success
                    ? 'bg-green-900/20 text-green-300 border border-green-600/30'
                    : 'bg-red-900/20 text-red-300 border border-red-600/30'
                }`}>
                  {testResult.success ? '✅ API Working!' : '❌ API Error: ' + testResult.message}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Mode Selection */}
        <div className="mb-8">
          <Tabs value={playgroundMode} onValueChange={setPlaygroundMode} className="w-full">
            <TabsList className="grid w-full grid-cols-5 bg-white/10 backdrop-blur-sm">
              <TabsTrigger value="chat" className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Chat
              </TabsTrigger>
              <TabsTrigger value="code" className="flex items-center gap-2">
                <Code className="h-4 w-4" />
                Code
              </TabsTrigger>
              <TabsTrigger value="image" className="flex items-center gap-2">
                <Image className="h-4 w-4" />
                Image
              </TabsTrigger>
              <TabsTrigger value="analysis" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Analyse
              </TabsTrigger>
              <TabsTrigger value="files" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Files
              </TabsTrigger>
            </TabsList>

            <div className="mt-6">
              <TabsContent value="chat" className="space-y-4">
                <Card className="bg-white/10 backdrop-blur-sm border-white/20">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <MessageSquare className="h-5 w-5" />
                      Conversation AGI
                    </CardTitle>
                    <CardDescription className="text-gray-300">
                      Discutez avec un système AGI avancé
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Conversation History */}
                    <div className="bg-black/20 rounded-lg p-4 h-64 overflow-y-auto space-y-3">
                      {conversationHistory.length === 0 ? (
                        <p className="text-gray-400 text-center">Commencez une conversation avec l'AGI...</p>
                      ) : (
                        conversationHistory.map((message, index) => (
                          <div key={index} className="space-y-2">
                            <div className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                              <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                message.role === 'user'
                                  ? 'bg-purple-600 text-white'
                                  : 'bg-gray-700 text-gray-100'
                              }`}>
                                <p className="text-sm">{message.content}</p>
                                <p className="text-xs opacity-70 mt-1">
                                  {new Date(message.timestamp).toLocaleTimeString()}
                                </p>
                              </div>
                            </div>

                            {/* Show reasoning for assistant messages */}
                            {message.role === 'assistant' && message.reasoningData && (
                              <div className="ml-4">
                                <ReasoningDisplay
                                  reasoningData={message.reasoningData}
                                  isVisible={visibleReasoning.has(index)}
                                  onToggleVisibility={() => toggleReasoningVisibility(index)}
                                />
                              </div>
                            )}

                            {/* Show feedback collector for assistant messages */}
                            {message.role === 'assistant' && (
                              <div className="ml-4">
                                <FeedbackCollector
                                  messageId={message.requestId || `msg_${index}`}
                                  messageContent={message.content}
                                  reasoningData={message.reasoningData}
                                  validationResult={message.validationResult}
                                  onFeedbackSubmit={handleFeedbackSubmit}
                                  compact={true}
                                />
                              </div>
                            )}
                          </div>
                        ))
                      )}
                    </div>
                    
                    {/* Input Area */}
                    <div className="flex gap-2">
                      <Textarea
                        placeholder="Posez votre question à l'AGI..."
                        value={playgroundInput}
                        onChange={(e) => setPlaygroundInput(e.target.value)}
                        className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                        rows={2}
                      />
                      <Button 
                        onClick={handleSubmit}
                        disabled={isProcessing || !playgroundInput.trim()}
                        className="bg-purple-600 hover:bg-purple-700 px-6"
                      >
                        {isProcessing ? (
                          <div className="flex items-center gap-2">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            AGI...
                          </div>
                        ) : (
                          'Envoyer'
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="code" className="space-y-4">
                <Card className="bg-white/10 backdrop-blur-sm border-white/20">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Code className="h-5 w-5" />
                      Génération de Code AGI
                    </CardTitle>
                    <CardDescription className="text-gray-300">
                      Générez du code intelligent avec l'AGI
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Textarea
                      placeholder="Décrivez le code que vous souhaitez générer..."
                      value={playgroundInput}
                      onChange={(e) => setPlaygroundInput(e.target.value)}
                      className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                      rows={3}
                    />
                    <Button 
                      onClick={handleSubmit}
                      disabled={isProcessing}
                      className="bg-purple-600 hover:bg-purple-700"
                    >
                      {isProcessing ? 'Génération...' : 'Générer le Code'}
                    </Button>
                    {/* Display code output with syntax highlighting */}
                    {conversationHistory.length > 0 && (
                      <div className="space-y-4">
                        {conversationHistory
                          .filter(msg => msg.role === 'assistant' && msg.mode === 'code')
                          .slice(-1) // Show only the latest code response
                          .map((message, index) => (
                            <div key={index}>
                              {message.additionalData ? (
                                <CodeDisplay codeData={message.additionalData} />
                              ) : (
                                <div className="bg-black/30 rounded-lg p-4">
                                  <pre className="text-green-400 text-sm overflow-x-auto whitespace-pre-wrap">
                                    <code>{message.content}</code>
                                  </pre>
                                </div>
                              )}
                            </div>
                          ))
                        }
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="image" className="space-y-4">
                <Card className="bg-white/10 backdrop-blur-sm border-white/20">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Image className="h-5 w-5" />
                      Descriptions d'Images AGI
                    </CardTitle>
                    <CardDescription className="text-gray-300">
                      Générez des descriptions détaillées pour la création d'images
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-3 mb-4">
                      <p className="text-blue-200 text-sm">
                        💡 <strong>Mode Description:</strong> L'AGI génère des descriptions détaillées que vous pouvez utiliser avec des outils comme DALL-E, Midjourney, ou Stable Diffusion.
                      </p>
                    </div>
                    <Textarea
                      placeholder="Décrivez l'image que vous souhaitez créer..."
                      value={playgroundInput}
                      onChange={(e) => setPlaygroundInput(e.target.value)}
                      className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                      rows={3}
                    />
                    <Button
                      onClick={handleSubmit}
                      disabled={isProcessing}
                      className="bg-purple-600 hover:bg-purple-700"
                    >
                      {isProcessing ? 'Génération...' : 'Créer la Description'}
                    </Button>
                    {playgroundOutput && (
                      <div className="bg-black/30 rounded-lg p-4">
                        <div className="flex items-start gap-2 mb-2">
                          <div className="bg-green-500 rounded-full w-2 h-2 mt-2 flex-shrink-0"></div>
                          <p className="text-green-400 font-semibold text-sm">Description d'image générée:</p>
                        </div>
                        <p className="text-gray-100 leading-relaxed">{playgroundOutput}</p>
                        <div className="mt-3 pt-3 border-t border-gray-600">
                          <p className="text-gray-400 text-xs">
                            💡 Copiez cette description et utilisez-la dans votre outil de génération d'images préféré
                          </p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="analysis" className="space-y-4">
                <Card className="bg-white/10 backdrop-blur-sm border-white/20">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <BarChart3 className="h-5 w-5" />
                      Analyse AGI Avancée
                    </CardTitle>
                    <CardDescription className="text-gray-300">
                      Obtenez des analyses approfondies avec l'AGI
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Textarea
                      placeholder="Sujet à analyser en profondeur..."
                      value={playgroundInput}
                      onChange={(e) => setPlaygroundInput(e.target.value)}
                      className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                      rows={3}
                    />
                    <Button 
                      onClick={handleSubmit}
                      disabled={isProcessing}
                      className="bg-purple-600 hover:bg-purple-700"
                    >
                      {isProcessing ? 'Analyse...' : 'Analyser'}
                    </Button>
                    {/* Display analysis output with structured formatting */}
                    {conversationHistory.length > 0 && (
                      <div className="space-y-4">
                        {conversationHistory
                          .filter(msg => msg.role === 'assistant' && msg.mode === 'analysis')
                          .slice(-1) // Show only the latest analysis response
                          .map((message, index) => (
                            <AnalysisDisplay key={index} content={message.content} />
                          ))
                        }
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="files" className="space-y-4">
                <FileManagementTab />
              </TabsContent>
            </div>
          </Tabs>
        </div>

        {/* Statistics and Panels */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Statistics */}
          <div className="grid grid-cols-2 gap-4">
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-4 text-center">
                <MessageSquare className="h-6 w-6 text-purple-400 mx-auto mb-1" />
                <p className="text-xl font-bold text-white">{conversationHistory.length}</p>
                <p className="text-gray-300 text-sm">Current Session</p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-4 text-center">
                <Zap className="h-6 w-6 text-cyan-400 mx-auto mb-1" />
                <p className="text-xl font-bold text-white">{usageStats.requestCount}</p>
                <p className="text-gray-300 text-sm">API Requests</p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-4 text-center">
                <Clock className="h-6 w-6 text-green-400 mx-auto mb-1" />
                <p className="text-xl font-bold text-white">
                  {apiStatus.isConfigured ? 'Tool-Enhanced' : 'Offline'}
                </p>
                <p className="text-gray-300 text-sm">
                  {apiStatus.isConfigured ? 'AGI + Tools' : 'Simulation Mode'}
                </p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-4 text-center">
                <Brain className="h-6 w-6 text-orange-400 mx-auto mb-1" />
                <p className="text-xl font-bold text-white">
                  {memoryStats?.totalConversations || 0}
                </p>
                <p className="text-gray-300 text-sm">Stored Conversations</p>
              </CardContent>
            </Card>

            {/* Learning Dashboard Button */}
            <Card
              className="bg-white/10 backdrop-blur-sm border-white/20 cursor-pointer hover:bg-white/20 transition-colors col-span-2"
              onClick={() => setShowLearningDashboard(true)}
            >
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Brain className="h-6 w-6 text-purple-400" />
                  <span className="text-lg font-bold text-white">Learning Dashboard</span>
                </div>
                <p className="text-gray-300 text-sm">View AGI adaptation & feedback insights</p>
              </CardContent>
            </Card>
          </div>

          {/* Memory Panel */}
          <MemoryPanel
            memoryStats={memoryStats}
            onClearMemory={clearMemory}
            onExportMemory={exportMemory}
            onImportMemory={importMemory}
          />

          {/* Tools Panel */}
          <ToolsPanel />
        </div>
      </div>

      {/* API Setup Guide Modal */}
      {showSetupGuide && (
        <APISetupGuide
          apiStatus={apiStatus}
          onClose={() => setShowSetupGuide(false)}
        />
      )}

      {/* Learning Dashboard Modal */}
      <LearningDashboard
        isVisible={showLearningDashboard}
        onClose={() => setShowLearningDashboard(false)}
      />
    </div>
  )
}

export default AGIPlayground
