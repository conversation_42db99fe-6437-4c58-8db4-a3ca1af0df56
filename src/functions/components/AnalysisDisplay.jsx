// Analysis Display Component with Structured Formatting
import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { 
  Target, 
  BarChart3, 
  Search, 
  Lightbulb, 
  Rocket, 
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

const AnalysisDisplay = ({ content, className = '' }) => {
  // Parse the structured analysis content
  const parseAnalysis = (text) => {
    const sections = {}
    const lines = text.split('\n')
    let currentSection = null
    let currentContent = []

    lines.forEach(line => {
      const trimmed = line.trim()
      
      // Check for main headers (##)
      if (trimmed.startsWith('## ')) {
        if (currentSection) {
          sections[currentSection] = currentContent.join('\n')
        }
        currentSection = trimmed.replace('## ', '').replace(/[🎯📊🔍💡🚀📈]/g, '').trim()
        currentContent = []
      } else if (trimmed.length > 0) {
        currentContent.push(line)
      }
    })

    // Add the last section
    if (currentSection) {
      sections[currentSection] = currentContent.join('\n')
    }

    return sections
  }

  const sections = parseAnalysis(content)

  // Helper function to render section content with proper formatting
  const renderSectionContent = (content) => {
    const lines = content.split('\n')
    const elements = []
    let currentList = []
    let inSubsection = false

    lines.forEach((line, index) => {
      const trimmed = line.trim()
      
      if (trimmed.startsWith('### ')) {
        // Subsection header
        if (currentList.length > 0) {
          elements.push(
            <ul key={`list-${index}`} className="list-disc list-inside space-y-1 ml-4 text-gray-200">
              {currentList.map((item, i) => (
                <li key={i} className="text-sm">{item}</li>
              ))}
            </ul>
          )
          currentList = []
        }
        elements.push(
          <h4 key={index} className="font-semibold text-purple-300 mt-4 mb-2 text-sm">
            {trimmed.replace('### ', '')}
          </h4>
        )
        inSubsection = true
      } else if (trimmed.startsWith('- ')) {
        // List item
        currentList.push(trimmed.replace('- ', ''))
      } else if (trimmed.length > 0 && !trimmed.startsWith('#')) {
        // Regular paragraph
        if (currentList.length > 0) {
          elements.push(
            <ul key={`list-${index}`} className="list-disc list-inside space-y-1 ml-4 text-gray-200">
              {currentList.map((item, i) => (
                <li key={i} className="text-sm">{item}</li>
              ))}
            </ul>
          )
          currentList = []
        }
        elements.push(
          <p key={index} className="text-gray-200 text-sm leading-relaxed mb-2">
            {trimmed}
          </p>
        )
      }
    })

    // Add any remaining list items
    if (currentList.length > 0) {
      elements.push(
        <ul key="final-list" className="list-disc list-inside space-y-1 ml-4 text-gray-200">
          {currentList.map((item, i) => (
            <li key={i} className="text-sm">{item}</li>
          ))}
        </ul>
      )
    }

    return elements
  }

  // Section configuration with icons and colors
  const sectionConfig = {
    'EXECUTIVE SUMMARY': { icon: Target, color: 'text-purple-400', bg: 'bg-purple-500/10' },
    'SITUATION ANALYSIS': { icon: BarChart3, color: 'text-blue-400', bg: 'bg-blue-500/10' },
    'DETAILED ANALYSIS': { icon: Search, color: 'text-cyan-400', bg: 'bg-cyan-500/10' },
    'KEY INSIGHTS': { icon: Lightbulb, color: 'text-yellow-400', bg: 'bg-yellow-500/10' },
    'RECOMMENDATIONS': { icon: Rocket, color: 'text-green-400', bg: 'bg-green-500/10' },
    'SUCCESS METRICS': { icon: TrendingUp, color: 'text-orange-400', bg: 'bg-orange-500/10' }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {Object.entries(sections).map(([sectionName, sectionContent]) => {
        const config = sectionConfig[sectionName] || { 
          icon: CheckCircle, 
          color: 'text-gray-400', 
          bg: 'bg-gray-500/10' 
        }
        const IconComponent = config.icon

        return (
          <Card key={sectionName} className={`bg-black/30 border-white/10 ${config.bg}`}>
            <CardHeader className="pb-3">
              <CardTitle className={`flex items-center gap-2 text-white text-base ${config.color}`}>
                <IconComponent className="h-5 w-5" />
                {sectionName}
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                {renderSectionContent(sectionContent)}
              </div>
            </CardContent>
          </Card>
        )
      })}

      {/* Analysis Metadata */}
      <div className="flex items-center justify-between pt-4 border-t border-white/10">
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="bg-purple-600/20 text-purple-300">
            Strategic Analysis
          </Badge>
          <span className="text-gray-400 text-sm">
            {Object.keys(sections).length} sections analyzed
          </span>
        </div>
        <div className="flex items-center gap-1 text-gray-400 text-sm">
          <Clock className="h-3 w-3" />
          Generated {new Date().toLocaleTimeString()}
        </div>
      </div>
    </div>
  )
}

export default AnalysisDisplay
