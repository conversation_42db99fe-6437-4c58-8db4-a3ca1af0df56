/* Multi-Agent Dashboard Styles */
.multi-agent-dashboard {
  width: 100%;
  height: 100vh;
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
}

/* Loading State */
.multi-agent-dashboard.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  text-align: center;
  color: #6b7280;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.multi-agent-dashboard.error {
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-message {
  text-align: center;
  padding: 32px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 400px;
}

.error-message h3 {
  margin: 0 0 16px 0;
  color: #dc2626;
  font-size: 18px;
}

.error-message p {
  margin: 0 0 20px 0;
  color: #6b7280;
  font-size: 14px;
}

.retry-button {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s ease;
}

.retry-button:hover {
  background: #2563eb;
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dashboard-header h1 {
  margin: 0;
  color: #1f2937;
  font-size: 28px;
  font-weight: 700;
}

.header-info {
  display: flex;
  gap: 20px;
  align-items: center;
}

.agent-count,
.active-count,
.collaboration-count {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.agent-count {
  background: #e0e7ff;
  color: #3730a3;
}

.active-count {
  background: #d1fae5;
  color: #065f46;
}

.collaboration-count {
  background: #fef3c7;
  color: #92400e;
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  height: calc(100vh - 80px);
}

/* Dashboard Sidebar */
.dashboard-sidebar {
  width: 350px;
  background: white;
  border-right: 1px solid #e5e7eb;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Dashboard Main */
.dashboard-main {
  flex: 1;
  overflow: hidden;
}

/* System Status */
.system-status {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.status-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  flex: 1;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.status-text {
  font-size: 12px;
  color: #6b7280;
  text-transform: capitalize;
  font-weight: 500;
}

.status-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.status-metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 11px;
  color: #6b7280;
  text-transform: uppercase;
  font-weight: 500;
}

.metric-value {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
}

.last-updated {
  font-size: 11px;
  color: #9ca3af;
  text-align: center;
  margin-top: 8px;
}

/* Task Form */
.task-form {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
}

.task-form h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 13px;
  color: #374151;
  font-weight: 500;
}

.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #1f2937;
  background: white;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease;
}

.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.selected-agents {
  margin-bottom: 16px;
}

.selected-agents label {
  display: block;
  margin-bottom: 8px;
  font-size: 13px;
  color: #374151;
  font-weight: 500;
}

.agent-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.agent-chip {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #e0e7ff;
  color: #3730a3;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.agent-chip-icon {
  font-size: 14px;
}

.agent-chip-name {
  text-transform: capitalize;
}

.agent-chip-remove {
  background: none;
  border: none;
  color: #6366f1;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  padding: 0;
  margin-left: 2px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s ease;
}

.agent-chip-remove:hover {
  background: rgba(99, 102, 241, 0.2);
}

.form-actions {
  display: flex;
  gap: 8px;
}

.submit-button,
.clear-selection-button,
.create-button,
.cancel-button {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-button {
  background: #3b82f6;
  color: white;
  flex: 1;
}

.submit-button:hover:not(:disabled) {
  background: #2563eb;
}

.submit-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.clear-selection-button,
.cancel-button {
  background: #f3f4f6;
  color: #6b7280;
}

.clear-selection-button:hover:not(:disabled),
.cancel-button:hover {
  background: #e5e7eb;
  color: #374151;
}

.create-button {
  background: #10b981;
  color: white;
  flex: 1;
}

.create-button:hover {
  background: #059669;
}

/* Create Agent Form */
.create-agent-form {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 12px;
  padding: 20px;
}

.create-agent-form h3 {
  margin: 0 0 16px 0;
  color: #065f46;
  font-size: 16px;
  font-weight: 600;
}

/* Quick Actions */
.quick-actions {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
}

.quick-actions h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  transition: all 0.2s ease;
}

.action-button:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #d1d5db;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-icon {
  font-size: 16px;
}

/* Performance Summary */
.performance-summary {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
}

.performance-summary h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.performance-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.performance-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.card-title {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
}

.card-icon {
  font-size: 16px;
}

.card-value {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-trend {
  font-size: 11px;
  color: #6b7280;
}

/* Dashboard Alerts */
.dashboard-alerts {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 350px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  padding: 16px;
  z-index: 1000;
}

.dashboard-alerts h3 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 14px;
  font-weight: 600;
}

.dashboard-alert {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 10px 12px;
  border-radius: 6px;
  margin-bottom: 8px;
}

.dashboard-alert:last-child {
  margin-bottom: 0;
}

.dashboard-alert.alert-critical {
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.dashboard-alert.alert-warning {
  background: #fffbeb;
  border: 1px solid #fed7aa;
}

.dashboard-alert.alert-info {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
}

.dashboard-alert .alert-icon {
  font-size: 16px;
  margin-top: 2px;
}

.alert-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.dashboard-alert .alert-message {
  font-size: 13px;
  font-weight: 500;
  color: #1f2937;
}

.dashboard-alert .alert-time {
  font-size: 11px;
  color: #6b7280;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-content {
    flex-direction: column;
  }

  .dashboard-sidebar {
    width: 100%;
    height: auto;
    max-height: 40vh;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }

  .dashboard-main {
    height: 60vh;
  }

  .dashboard-alerts {
    position: relative;
    bottom: auto;
    right: auto;
    width: 100%;
    margin-top: 20px;
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
}
