// Custom hook for AGI Playground state management
import { useState, useEffect } from 'react'
import { handlePlaygroundSubmit, validateInput, createMessage } from './agiPlaygroundLogic'
import memoryService from '../services/memoryService'

export const useAGIPlayground = () => {
  // États pour l'AGI Playground
  const [playgroundMode, setPlaygroundMode] = useState('chat')
  const [playgroundInput, setPlaygroundInput] = useState('')
  const [playgroundOutput, setPlaygroundOutput] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [conversationHistory, setConversationHistory] = useState([])
  const [memoryStats, setMemoryStats] = useState(null)

  // Load conversation history and memory stats on component mount
  useEffect(() => {
    try {
      // Load recent conversation for current mode
      const recentConversations = memoryService.getRecentConversations(1)
      if (recentConversations.length > 0) {
        const lastConversation = recentConversations[0]
        if (lastConversation.mode === playgroundMode) {
          setConversationHistory(lastConversation.messages || [])
          console.log('📚 Loaded conversation history:', lastConversation.messages.length, 'messages')
        }
      }

      // Load memory statistics
      const stats = memoryService.getMemoryStats()
      setMemoryStats(stats)
      console.log('🧠 Memory stats loaded:', stats)
    } catch (error) {
      console.error('❌ Failed to load memory data:', error)
    }
  }, [playgroundMode])

  // Update memory stats periodically
  useEffect(() => {
    const updateMemoryStats = () => {
      const stats = memoryService.getMemoryStats()
      setMemoryStats(stats)
    }

    const interval = setInterval(updateMemoryStats, 10000) // Update every 10 seconds
    return () => clearInterval(interval)
  }, [])

  // Submit handler
  const handleSubmit = async () => {
    if (!validateInput(playgroundInput)) return
    
    await handlePlaygroundSubmit(
      playgroundInput,
      playgroundMode,
      setIsProcessing,
      setConversationHistory,
      setPlaygroundOutput,
      setPlaygroundInput
    )
  }

  // Clear conversation
  const clearConversation = () => {
    setConversationHistory([])
    setPlaygroundOutput('')
  }

  // Reset playground
  const resetPlayground = () => {
    setPlaygroundInput('')
    setPlaygroundOutput('')
    setConversationHistory([])
    setIsProcessing(false)
  }

  // Clear all memory
  const clearMemory = () => {
    const success = memoryService.clearMemory()
    if (success) {
      setConversationHistory([])
      setPlaygroundOutput('')
      setMemoryStats(memoryService.getMemoryStats())
      console.log('🗑️ All memory cleared')
    }
    return success
  }

  // Export memory data
  const exportMemory = () => {
    return memoryService.exportMemory()
  }

  // Import memory data
  const importMemory = (memoryData) => {
    const success = memoryService.importMemory(memoryData)
    if (success) {
      setMemoryStats(memoryService.getMemoryStats())
      console.log('📥 Memory imported successfully')
    }
    return success
  }

  return {
    // State
    playgroundMode,
    playgroundInput,
    playgroundOutput,
    isProcessing,
    conversationHistory,
    memoryStats,

    // Setters
    setPlaygroundMode,
    setPlaygroundInput,
    setPlaygroundOutput,
    setIsProcessing,
    setConversationHistory,

    // Actions
    handleSubmit,
    clearConversation,
    resetPlayground,
    clearMemory,
    exportMemory,
    importMemory
  }
}
