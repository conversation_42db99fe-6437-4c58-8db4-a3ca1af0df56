// AGI Playground Logic Functions
// This file contains all the business logic for the AGI Playground
import geminiService from '../services/geminiService.js'
import codeService from '../services/codeService.js'
import memoryService from '../services/memoryService.js'
import contextService from '../services/contextService.js'
import reasoningService from '../services/reasoningService.js'
import solutionValidator from '../services/solutionValidation.js'
import reasoningMetrics from '../services/reasoningMetrics.js'
import learningService from '../services/learningService.js'

// AGI Models configuration (now using real Gemini)
export const agiModels = [
  { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: 'Fast and efficient model for general intelligence', active: true },
  { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', description: 'Advanced model for complex reasoning tasks', active: false },
  { id: 'gemini-1.0-pro', name: 'Gemini 1.0 Pro', description: 'Stable model for production use', active: false }
]

// Handle playground submission logic with real Gemini API and memory
export const handlePlaygroundSubmit = async (playgroundInput, playgroundMode, setIsProcessing, setConversationHistory, setPlaygroundOutput, setPlaygroundInput) => {
  if (!playgroundInput.trim()) return

  setIsProcessing(true)
  const userMessage = { role: 'user', content: playgroundInput, timestamp: new Date() }

  // Add user message to conversation history
  setConversationHistory(prev => {
    const newHistory = [...prev, userMessage]

    // Auto-save conversation if enabled
    const preferences = memoryService.getPreferences()
    if (preferences.autoSave && newHistory.length > 1) {
      memoryService.saveConversation(newHistory, playgroundMode)
    }

    return newHistory
  })

  // Clear input immediately
  setPlaygroundInput('')

  // Generate real AGI response using Gemini with context
  await generateRealAGIResponse(playgroundInput, playgroundMode, setConversationHistory, setPlaygroundOutput, setIsProcessing)
}

// Generate real AGI response using Gemini API with context and memory
const generateRealAGIResponse = async (input, mode, setConversationHistory, setPlaygroundOutput, setIsProcessing) => {
  try {
    console.log(`🚀 Generating context-aware AGI response for mode: ${mode}`)

    // Get current conversation history for context
    let currentHistory = []
    setConversationHistory(prev => {
      currentHistory = prev
      return prev
    })

    // Build enhanced context using memory and conversation history
    const enhancedContext = contextService.buildEnhancedContext(input, currentHistory, mode)

    // Step 1: Get adaptive behavior suggestions from learning service
    const adaptiveBehavior = learningService.getAdaptiveBehaviorSuggestions({
      input,
      mode,
      conversationHistory: currentHistory
    })

    if (adaptiveBehavior.adaptations.length > 0) {
      console.log(`🎯 Applying adaptive behaviors:`, adaptiveBehavior.adaptations)
    }

    // Step 2: Analyze if reasoning is needed (considering adaptive preferences)
    const reasoningAnalysis = await reasoningService.analyzeAndReason(input, {
      ...enhancedContext,
      adaptiveBehavior
    })

    // Step 3: Use reasoning-enhanced prompt if reasoning is beneficial
    const finalInput = reasoningAnalysis.useReasoning
      ? reasoningAnalysis.enhancedPrompt
      : input

    console.log(`🧠 Reasoning ${reasoningAnalysis.useReasoning ? 'ENABLED' : 'DISABLED'} for this query`)

    // Add adaptive behavior to enhanced context
    const adaptiveContext = {
      ...enhancedContext,
      adaptiveBehavior,
      learningInsights: {
        responseStyle: adaptiveBehavior.responseStyle,
        detailLevel: adaptiveBehavior.detailLevel,
        includeExamples: adaptiveBehavior.includeExamples,
        formatPreference: adaptiveBehavior.formatPreference
      }
    }

    // Use Gemini service to generate tool-enhanced response
    const response = await geminiService.generateToolEnhancedResponse(finalInput, mode, currentHistory, adaptiveContext)

    if (response.error) {
      console.error('❌ AGI response error:', response.originalError)
    } else {
      console.log('✅ AGI response generated successfully')
    }

    // Process response based on mode
    let processedContent = response.content
    let additionalData = null

    if (mode === 'code') {
      // Extract code from response and process it
      const codeMatch = response.content.match(/```(\w+)?\n([\s\S]*?)```/)
      if (codeMatch) {
        const language = codeMatch[1] || null
        const code = codeMatch[2].trim()
        const explanation = response.content.replace(/```(\w+)?\n[\s\S]*?```/, '').trim()

        additionalData = codeService.formatCodeResponse(code, language, explanation)
      }
    }

    // Step 4: Validate solution quality if reasoning was used
    let validationResult = null
    if (reasoningAnalysis.useReasoning) {
      validationResult = await solutionValidator.validateSolution(input, processedContent, reasoningAnalysis)
      console.log(`🔍 Solution validation: ${validationResult.passed ? 'PASSED' : 'FAILED'} (${validationResult.score}%)`)
    }

    // Step 5: Collect reasoning quality metrics
    let metricsData = null
    if (reasoningAnalysis.useReasoning) {
      metricsData = reasoningMetrics.collectMetrics(reasoningAnalysis, validationResult)
    }

    const agiMessage = {
      role: 'assistant',
      content: processedContent,
      timestamp: response.timestamp,
      mode: response.mode,
      requestId: response.requestId,
      additionalData,
      reasoningData: reasoningAnalysis.useReasoning ? reasoningAnalysis : null,
      validationResult: validationResult,
      metricsData: metricsData,
      adaptiveBehavior: adaptiveBehavior.adaptations.length > 0 ? adaptiveBehavior : null
    }

    setConversationHistory(prev => {
      const newHistory = [...prev, agiMessage]

      // Save updated conversation with AI response
      const preferences = memoryService.getPreferences()
      if (preferences.autoSave) {
        memoryService.saveConversation(newHistory, mode)
      }

      // Learn from this interaction
      memoryService.learnFromInteraction({
        userMessage: input,
        aiResponse: response.content,
        mode: mode,
        detectedLanguage: additionalData?.language,
        timestamp: new Date().toISOString()
      })

      return newHistory
    })

    setPlaygroundOutput(response.content)

  } catch (error) {
    console.error('❌ Failed to generate AGI response:', error)

    // Fallback error message
    const errorMessage = {
      role: 'assistant',
      content: 'I apologize, but I\'m experiencing technical difficulties connecting to my AGI systems. Please check your API configuration and try again.',
      timestamp: new Date(),
      error: true
    }

    setConversationHistory(prev => [...prev, errorMessage])
    setPlaygroundOutput(errorMessage.content)
  } finally {
    setIsProcessing(false)
  }
}

// Generate response based on mode using real Gemini API
export const generateResponse = async (input, mode, conversationHistory = []) => {
  try {
    const response = await geminiService.generateResponse(input, mode, conversationHistory)
    return response.content
  } catch (error) {
    console.error('❌ Error generating response:', error)
    return 'I apologize, but I\'m experiencing technical difficulties. Please try again.'
  }
}

// Generate streaming response for real-time feel
export const generateStreamingResponse = async (input, mode, conversationHistory = [], onChunk) => {
  try {
    const response = await geminiService.generateStreamingResponse(input, mode, conversationHistory, onChunk)
    return response.content
  } catch (error) {
    console.error('❌ Error generating streaming response:', error)
    return 'I apologize, but I\'m experiencing technical difficulties. Please try again.'
  }
}

// Create message object
export const createMessage = (role, content) => ({
  role,
  content,
  timestamp: new Date()
})

// Validate input
export const validateInput = (input) => {
  return input && input.trim().length > 0
}

// Get API usage statistics
export const getAPIUsageStats = () => {
  return geminiService.getUsageStats()
}

// Reset API usage statistics
export const resetAPIStats = () => {
  return geminiService.resetStats()
}

// Check if API is properly configured
export const checkAPIConfiguration = () => {
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY
  return {
    isConfigured: !!apiKey,
    hasKey: !!apiKey,
    keyLength: apiKey ? apiKey.length : 0,
    keyPreview: apiKey ? `${apiKey.substring(0, 8)}...` : 'Not set'
  }
}

// Test API connection
export const testAPIConnection = async () => {
  try {
    console.log('🧪 Starting API connection test...')
    const testResponse = await geminiService.generateResponse('Hello', 'chat', [])
    console.log('🧪 Test response received:', testResponse)

    if (testResponse.error) {
      return {
        success: false,
        message: 'API returned error',
        error: testResponse.originalError,
        details: testResponse.errorDetails
      }
    }

    return {
      success: true,
      message: 'API connection successful',
      response: testResponse.content.substring(0, 100) + '...'
    }
  } catch (error) {
    console.error('🧪 Test connection error:', error)
    return {
      success: false,
      message: 'API connection failed',
      error: error.message
    }
  }
}
