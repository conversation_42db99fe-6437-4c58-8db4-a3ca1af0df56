// useMultiAgentData Hook - React hook for managing multi-agent system data
import { useState, useEffect, useCallback, useRef } from 'react'
import agentFramework from '../services/agentFramework.js'
import agentCoordinationSystem from '../services/agentCoordination.js'
import agentPerformanceMonitoring from '../services/agentPerformanceMonitoring.js'
import agentResultSynthesis from '../services/agentResultSynthesis.js'

const useMultiAgentData = (options = {}) => {
  const {
    refreshInterval = 5000, // 5 seconds
    enableRealTimeUpdates = true,
    includePerformanceData = true,
    includeCollaborationData = true
  } = options

  // State
  const [agentData, setAgentData] = useState([])
  const [collaborationData, setCollaborationData] = useState({})
  const [performanceData, setPerformanceData] = useState({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [lastUpdated, setLastUpdated] = useState(null)

  // Refs
  const intervalRef = useRef(null)
  const mountedRef = useRef(true)

  // Initialize multi-agent systems
  const initializeSystems = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Initialize all systems
      await agentFramework.initialize()
      await agentCoordinationSystem.initialize()
      
      if (includePerformanceData) {
        await agentPerformanceMonitoring.initialize()
      }

      console.log('✅ Multi-agent systems initialized')
    } catch (err) {
      console.error('❌ Failed to initialize multi-agent systems:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }, [includePerformanceData])

  // Fetch agent data
  const fetchAgentData = useCallback(async () => {
    try {
      const frameworkStats = agentFramework.getFrameworkStats()
      const agents = []

      // Convert framework stats to agent data format
      Object.entries(frameworkStats.agentTypeStats || {}).forEach(([type, stats]) => {
        // Create agent instances based on stats
        for (let i = 0; i < (stats.instances || 0); i++) {
          const agentId = `${type}_${i + 1}`
          const isActive = i < (stats.activeInstances || 0)
          
          agents.push({
            id: agentId,
            name: `${type.charAt(0).toUpperCase() + type.slice(1)} Agent ${i + 1}`,
            type: type,
            status: isActive ? (Math.random() > 0.7 ? 'collaborating' : 'working') : 'idle',
            currentTasks: isActive ? Math.floor(Math.random() * 3) + 1 : 0,
            performance: {
              successRate: stats.averagePerformance?.successRate || Math.random() * 0.3 + 0.7,
              averageCompletionTime: stats.averagePerformance?.completionTime || Math.random() * 10000 + 5000,
              tasksCompleted: stats.averagePerformance?.tasksCompleted || Math.floor(Math.random() * 50) + 10,
              collaborationRating: Math.random() * 0.3 + 0.7
            },
            capabilities: getAgentCapabilities(type),
            collaborations: isActive && Math.random() > 0.6 ? [`collab_${Math.floor(Math.random() * 3) + 1}`] : [],
            currentTask: isActive ? generateCurrentTask(type) : null,
            recentActivity: generateRecentActivity(type, isActive)
          })
        }
      })

      if (mountedRef.current) {
        setAgentData(agents)
      }
    } catch (err) {
      console.error('❌ Failed to fetch agent data:', err)
      if (mountedRef.current) {
        setError(err.message)
      }
    }
  }, [])

  // Fetch collaboration data
  const fetchCollaborationData = useCallback(async () => {
    if (!includeCollaborationData) return

    try {
      const coordinationStats = agentCoordinationSystem.getCoordinationStats()
      const synthesisStats = agentResultSynthesis.getSynthesisStats()

      const collaborationData = {
        activeCollaborations: generateActiveCollaborations(),
        totalCollaborations: coordinationStats.totalCoordinations || 0,
        successRate: coordinationStats.successRate || 0.85,
        averageDuration: coordinationStats.averageCompletionTime || 15000,
        synthesisQuality: synthesisStats.averageQualityScore || 82,
        recentCollaborations: coordinationStats.recentHistory || []
      }

      if (mountedRef.current) {
        setCollaborationData(collaborationData)
      }
    } catch (err) {
      console.error('❌ Failed to fetch collaboration data:', err)
      if (mountedRef.current) {
        setError(err.message)
      }
    }
  }, [includeCollaborationData])

  // Fetch performance data
  const fetchPerformanceData = useCallback(async () => {
    if (!includePerformanceData) return

    try {
      const performanceReport = agentPerformanceMonitoring.getPerformanceReport('1h')
      
      const performanceData = {
        ...performanceReport.summary,
        trends: performanceReport.trends || {},
        alerts: performanceReport.alerts || [],
        recommendations: performanceReport.recommendations || [],
        systemHealth: performanceReport.systemHealth || { status: 'healthy', score: 85 }
      }

      if (mountedRef.current) {
        setPerformanceData(performanceData)
      }
    } catch (err) {
      console.error('❌ Failed to fetch performance data:', err)
      if (mountedRef.current) {
        setError(err.message)
      }
    }
  }, [includePerformanceData])

  // Fetch all data
  const fetchAllData = useCallback(async () => {
    if (!mountedRef.current) return

    try {
      await Promise.all([
        fetchAgentData(),
        fetchCollaborationData(),
        fetchPerformanceData()
      ])

      if (mountedRef.current) {
        setLastUpdated(new Date())
      }
    } catch (err) {
      console.error('❌ Failed to fetch all data:', err)
      if (mountedRef.current) {
        setError(err.message)
      }
    }
  }, [fetchAgentData, fetchCollaborationData, fetchPerformanceData])

  // Start real-time updates
  const startRealTimeUpdates = useCallback(() => {
    if (!enableRealTimeUpdates) return

    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    intervalRef.current = setInterval(() => {
      fetchAllData()
    }, refreshInterval)

    console.log(`🔄 Started real-time updates (${refreshInterval}ms interval)`)
  }, [enableRealTimeUpdates, refreshInterval, fetchAllData])

  // Stop real-time updates
  const stopRealTimeUpdates = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
      console.log('⏹️ Stopped real-time updates')
    }
  }, [])

  // Create new agent
  const createAgent = useCallback(async (agentType) => {
    try {
      const newAgent = agentFramework.createAgent(agentType)
      await fetchAgentData() // Refresh data
      return newAgent
    } catch (err) {
      console.error(`❌ Failed to create ${agentType} agent:`, err)
      setError(err.message)
      return null
    }
  }, [fetchAgentData])

  // Start collaboration
  const startCollaboration = useCallback(async (agentIds, task) => {
    try {
      const result = await agentCoordinationSystem.coordinateTask(task, {
        participantIds: agentIds,
        multiAgent: true
      })
      
      await fetchAllData() // Refresh all data
      return result
    } catch (err) {
      console.error('❌ Failed to start collaboration:', err)
      setError(err.message)
      return null
    }
  }, [fetchAllData])

  // Get agent by ID
  const getAgentById = useCallback((agentId) => {
    return agentData.find(agent => agent.id === agentId)
  }, [agentData])

  // Get agents by type
  const getAgentsByType = useCallback((agentType) => {
    return agentData.filter(agent => agent.type === agentType)
  }, [agentData])

  // Get active agents
  const getActiveAgents = useCallback(() => {
    return agentData.filter(agent => 
      agent.status === 'working' || agent.status === 'collaborating'
    )
  }, [agentData])

  // Get collaboration by ID
  const getCollaborationById = useCallback((collaborationId) => {
    return collaborationData.activeCollaborations?.[collaborationId]
  }, [collaborationData])

  // Initialize on mount
  useEffect(() => {
    mountedRef.current = true
    initializeSystems().then(() => {
      fetchAllData().then(() => {
        startRealTimeUpdates()
      })
    })

    return () => {
      mountedRef.current = false
      stopRealTimeUpdates()
    }
  }, [initializeSystems, fetchAllData, startRealTimeUpdates, stopRealTimeUpdates])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopRealTimeUpdates()
    }
  }, [stopRealTimeUpdates])

  return {
    // Data
    agentData,
    collaborationData,
    performanceData,
    
    // State
    loading,
    error,
    lastUpdated,
    
    // Actions
    fetchAllData,
    createAgent,
    startCollaboration,
    
    // Getters
    getAgentById,
    getAgentsByType,
    getActiveAgents,
    getCollaborationById,
    
    // Controls
    startRealTimeUpdates,
    stopRealTimeUpdates,
    
    // Computed values
    totalAgents: agentData.length,
    activeAgents: getActiveAgents().length,
    idleAgents: agentData.filter(a => a.status === 'idle').length,
    collaboratingAgents: agentData.filter(a => a.status === 'collaborating').length,
    activeCollaborations: Object.keys(collaborationData.activeCollaborations || {}).length,
    systemHealth: performanceData.systemHealth?.status || 'unknown'
  }
}

// Helper functions
const getAgentCapabilities = (type) => {
  const capabilities = {
    'research': ['web_search', 'data_gathering', 'fact_checking', 'source_verification'],
    'coding': ['code_generation', 'debugging', 'testing', 'documentation'],
    'analysis': ['data_analysis', 'pattern_recognition', 'logical_reasoning', 'problem_solving'],
    'creative': ['content_creation', 'brainstorming', 'design_thinking', 'storytelling'],
    'coordinator': ['task_coordination', 'result_synthesis', 'workflow_management', 'optimization']
  }
  
  return capabilities[type] || ['general_processing']
}

const generateCurrentTask = (type) => {
  const tasks = {
    'research': [
      'Researching market trends for Q4 analysis',
      'Gathering information about competitor strategies',
      'Fact-checking recent industry reports',
      'Verifying sources for upcoming presentation'
    ],
    'coding': [
      'Implementing new authentication system',
      'Debugging performance issues in API',
      'Writing unit tests for user module',
      'Optimizing database query performance'
    ],
    'analysis': [
      'Analyzing user behavior patterns',
      'Processing sales data for insights',
      'Evaluating system performance metrics',
      'Identifying optimization opportunities'
    ],
    'creative': [
      'Creating content for marketing campaign',
      'Designing user interface mockups',
      'Brainstorming product feature ideas',
      'Writing technical documentation'
    ],
    'coordinator': [
      'Coordinating multi-team project delivery',
      'Synthesizing research findings',
      'Managing workflow optimization',
      'Orchestrating agent collaboration'
    ]
  }
  
  const typeTasks = tasks[type] || ['Processing general task']
  return typeTasks[Math.floor(Math.random() * typeTasks.length)]
}

const generateRecentActivity = (type, isActive) => {
  if (!isActive) return []
  
  const activities = []
  const now = new Date()
  
  for (let i = 0; i < Math.floor(Math.random() * 5) + 1; i++) {
    const timestamp = new Date(now.getTime() - (i * 5 + Math.random() * 10) * 60000)
    activities.push({
      timestamp: timestamp.toISOString(),
      description: generateActivityDescription(type)
    })
  }
  
  return activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
}

const generateActivityDescription = (type) => {
  const activities = {
    'research': [
      'Completed web search for industry data',
      'Verified source credibility',
      'Updated research findings',
      'Shared results with analysis team'
    ],
    'coding': [
      'Committed code changes to repository',
      'Ran automated test suite',
      'Fixed critical bug in payment system',
      'Deployed hotfix to production'
    ],
    'analysis': [
      'Processed new dataset',
      'Generated performance report',
      'Identified key trends',
      'Updated dashboard metrics'
    ],
    'creative': [
      'Created new design concepts',
      'Refined user interface elements',
      'Generated content variations',
      'Collaborated on brand guidelines'
    ],
    'coordinator': [
      'Orchestrated team collaboration',
      'Synthesized project results',
      'Optimized workflow processes',
      'Resolved resource conflicts'
    ]
  }
  
  const typeActivities = activities[type] || ['Completed assigned task']
  return typeActivities[Math.floor(Math.random() * typeActivities.length)]
}

const generateActiveCollaborations = () => {
  const collaborations = {}
  const collaborationCount = Math.floor(Math.random() * 3) + 1
  
  for (let i = 0; i < collaborationCount; i++) {
    const id = `collab_${i + 1}`
    collaborations[id] = {
      id,
      status: Math.random() > 0.3 ? 'active' : 'completing',
      participants: generateCollaborationParticipants(),
      progress: Math.random() * 0.4 + 0.3, // 30-70% progress
      startTime: new Date(Date.now() - Math.random() * 3600000).toISOString(),
      estimatedCompletion: new Date(Date.now() + Math.random() * 1800000).toISOString()
    }
  }
  
  return collaborations
}

const generateCollaborationParticipants = () => {
  const agentTypes = ['research', 'coding', 'analysis', 'creative', 'coordinator']
  const participantCount = Math.floor(Math.random() * 3) + 2 // 2-4 participants
  const participants = []
  
  for (let i = 0; i < participantCount; i++) {
    const type = agentTypes[Math.floor(Math.random() * agentTypes.length)]
    const agentId = `${type}_${Math.floor(Math.random() * 3) + 1}`
    if (!participants.includes(agentId)) {
      participants.push(agentId)
    }
  }
  
  return participants
}

export default useMultiAgentData
