// Multi-Agent System Demo - Interactive demonstration of agent collaboration
import agentFramework from '../services/agentFramework.js'
import agentCoordinationSystem from '../services/agentCoordination.js'
import specializedAgents from '../services/specializedAgents.js'
import agentResultSynthesis from '../services/agentResultSynthesis.js'
import agentCommunicationProtocol from '../services/agentCommunicationProtocol.js'
import multiAgentTaskDecomposition from '../services/multiAgentTaskDecomposition.js'

class MultiAgentDemo {
  constructor() {
    this.initialized = false
    this.demoResults = []
  }

  // Initialize all systems
  async initialize() {
    if (this.initialized) return

    console.log('🚀 Initializing Multi-Agent System Demo...')
    
    try {
      await agentFramework.initialize()
      await specializedAgents.initialize()
      await agentCoordinationSystem.initialize()
      await agentResultSynthesis.initialize()
      await agentCommunicationProtocol.initialize()
      await multiAgentTaskDecomposition.initialize()
      
      this.initialized = true
      console.log('✅ Multi-Agent System initialized successfully!')
      
    } catch (error) {
      console.error('❌ Initialization failed:', error)
      throw error
    }
  }

  // Demo 1: Basic Agent Creation and Communication
  async demo1_BasicAgentOperations() {
    console.log('\n🤖 DEMO 1: Basic Agent Operations')
    console.log('=' .repeat(50))
    
    try {
      // Create different types of agents
      console.log('Creating specialized agents...')
      const researchAgent = agentFramework.createAgent('research')
      const codingAgent = agentFramework.createAgent('coding')
      const analysisAgent = agentFramework.createAgent('analysis')
      
      console.log(`✅ Created agents:`)
      console.log(`  🔍 Research Agent: ${researchAgent.id}`)
      console.log(`  💻 Coding Agent: ${codingAgent.id}`)
      console.log(`  📊 Analysis Agent: ${analysisAgent.id}`)
      
      // Test communication between agents
      console.log('\nTesting agent communication...')
      const messageResult = await agentCommunicationProtocol.sendMessage(
        researchAgent.id,
        analysisAgent.id,
        'task_assignment',
        {
          taskId: 'demo_task_1',
          description: 'Analyze research findings',
          priority: 'medium',
          deadline: new Date(Date.now() + 3600000).toISOString()
        }
      )
      
      console.log(`📡 Message sent: ${messageResult.success ? '✅' : '❌'}`)
      if (messageResult.success) {
        console.log(`  Message ID: ${messageResult.messageId}`)
        console.log(`  Channel: ${messageResult.channel}`)
        console.log(`  Delivery Time: ${messageResult.deliveryTime}ms`)
      }
      
      // Show framework stats
      const stats = agentFramework.getFrameworkStats()
      console.log(`\n📊 Framework Stats:`)
      console.log(`  Total Agents: ${stats.totalAgentInstances}`)
      console.log(`  Agent Types: ${Object.keys(stats.agentTypeStats || {}).length}`)
      
      return { success: true, agents: [researchAgent, codingAgent, analysisAgent] }
      
    } catch (error) {
      console.error('❌ Demo 1 failed:', error)
      return { success: false, error: error.message }
    }
  }

  // Demo 2: Task Decomposition and Multi-Agent Planning
  async demo2_TaskDecomposition() {
    console.log('\n🔄 DEMO 2: Task Decomposition')
    console.log('=' .repeat(50))
    
    try {
      const complexTask = 'Create a comprehensive market analysis report for AI startups, including competitive landscape, growth projections, and investment recommendations'
      
      console.log(`📋 Complex Task: ${complexTask}`)
      console.log('\nAnalyzing task for multi-agent decomposition...')
      
      const decomposition = await multiAgentTaskDecomposition.decomposeForMultiAgent(complexTask, {
        priority: 'high',
        qualityRequirements: 'high'
      })
      
      console.log(`\n🎯 Decomposition Results:`)
      console.log(`  Multi-Agent Recommended: ${decomposition.multiAgentBenefit?.recommended ? '✅' : '❌'}`)
      console.log(`  Complexity: ${decomposition.analysis?.complexity}`)
      console.log(`  Domains: ${decomposition.analysis?.domains?.join(', ')}`)
      console.log(`  Estimated Effort: ${decomposition.analysis?.estimatedEffort} minutes`)
      
      if (decomposition.decomposition) {
        console.log(`\n📝 Subtasks (${decomposition.decomposition.subtasks?.length || 0}):`)
        decomposition.decomposition.subtasks?.forEach((subtask, index) => {
          console.log(`  ${index + 1}. ${subtask.description} (${subtask.domain})`)
        })
        
        console.log(`\n🤖 Agent Assignments:`)
        decomposition.decomposition.agentAssignments?.forEach((agentType, taskId) => {
          console.log(`  ${taskId} → ${agentType} agent`)
        })
      }
      
      return { success: true, decomposition }
      
    } catch (error) {
      console.error('❌ Demo 2 failed:', error)
      return { success: false, error: error.message }
    }
  }

  // Demo 3: Multi-Agent Coordination and Execution
  async demo3_MultiAgentCoordination() {
    console.log('\n🤝 DEMO 3: Multi-Agent Coordination')
    console.log('=' .repeat(50))
    
    try {
      const task = {
        description: 'Research and analyze the latest trends in artificial intelligence and machine learning',
        requirements: {
          domains: ['research', 'analysis'],
          priority: 'high',
          multiAgent: true
        }
      }
      
      console.log(`📋 Task: ${task.description}`)
      console.log('\nCoordinating multi-agent execution...')
      
      const coordination = await agentCoordinationSystem.coordinateTask(task)
      
      console.log(`\n🎯 Coordination Results:`)
      console.log(`  Success: ${coordination.success ? '✅' : '❌'}`)
      console.log(`  Coordination ID: ${coordination.coordinationId}`)
      
      if (coordination.allocation) {
        console.log(`  Agents Allocated: ${coordination.allocation.agents?.length || 0}`)
        console.log(`  Strategy: ${coordination.allocation.allocation_strategy}`)
        console.log(`  Coordinator: ${coordination.allocation.coordinator}`)
      }
      
      if (coordination.executionPlan) {
        console.log(`\n📅 Execution Plan:`)
        console.log(`  Strategy: ${coordination.executionPlan.strategy}`)
        console.log(`  Phases: ${coordination.executionPlan.phases?.length || 0}`)
        console.log(`  Estimated Duration: ${Math.round((coordination.executionPlan.timeline?.total_duration || 0) / 60000)} minutes`)
      }
      
      return { success: true, coordination }
      
    } catch (error) {
      console.error('❌ Demo 3 failed:', error)
      return { success: false, error: error.message }
    }
  }

  // Demo 4: Specialized Agent Execution
  async demo4_SpecializedAgents() {
    console.log('\n🎨 DEMO 4: Specialized Agent Execution')
    console.log('=' .repeat(50))
    
    try {
      const results = []
      
      // Test Research Agent
      console.log('🔍 Testing Research Agent...')
      const researchTask = {
        type: 'research_synthesis',
        description: 'Research the current state of AI in healthcare',
        requirements: { depth: 'comprehensive' }
      }
      
      const researchResult = await specializedAgents.executeTask('research', researchTask)
      console.log(`  Research Agent: ${researchResult.success ? '✅' : '❌'}`)
      if (researchResult.success) {
        console.log(`    Content Length: ${researchResult.content?.length || 0} chars`)
        console.log(`    Confidence: ${researchResult.confidence || 'N/A'}%`)
      }
      results.push(researchResult)
      
      // Test Analysis Agent
      console.log('\n📊 Testing Analysis Agent...')
      const analysisTask = {
        type: 'data_analysis',
        description: 'Analyze healthcare AI adoption trends',
        requirements: { 
          data: [10, 25, 40, 65, 85, 120, 150], // Sample growth data
          analysisType: 'trend_analysis'
        }
      }
      
      const analysisResult = await specializedAgents.executeTask('analysis', analysisTask)
      console.log(`  Analysis Agent: ${analysisResult.success ? '✅' : '❌'}`)
      if (analysisResult.success) {
        console.log(`    Analysis Type: ${analysisResult.analysis_type || 'N/A'}`)
        console.log(`    Insights: ${analysisResult.insights?.length || 0}`)
      }
      results.push(analysisResult)
      
      // Test Creative Agent
      console.log('\n🎨 Testing Creative Agent...')
      const creativeTask = {
        type: 'content_creation',
        description: 'Create an executive summary for the healthcare AI research',
        requirements: { style: 'executive', length: 'medium' }
      }
      
      const creativeResult = await specializedAgents.executeTask('creative', creativeTask)
      console.log(`  Creative Agent: ${creativeResult.success ? '✅' : '❌'}`)
      if (creativeResult.success) {
        console.log(`    Content Length: ${creativeResult.content?.length || 0} chars`)
        console.log(`    Creativity Score: ${creativeResult.creativity_score || 'N/A'}`)
      }
      results.push(creativeResult)
      
      return { success: true, results }
      
    } catch (error) {
      console.error('❌ Demo 4 failed:', error)
      return { success: false, error: error.message }
    }
  }

  // Demo 5: Result Synthesis
  async demo5_ResultSynthesis() {
    console.log('\n🔬 DEMO 5: Result Synthesis')
    console.log('=' .repeat(50))
    
    try {
      // Simulate agent results from different agents
      const agentResults = [
        {
          agentType: 'research',
          success: true,
          content: 'Healthcare AI market is experiencing rapid growth with 35% annual increase. Key areas include diagnostic imaging, drug discovery, and patient monitoring.',
          confidence: 88
        },
        {
          agentType: 'analysis',
          success: true,
          content: 'Statistical analysis shows exponential adoption curve with healthcare AI investments reaching $45B by 2024. ROI averages 300% within 2 years.',
          confidence: 92
        },
        {
          agentType: 'creative',
          success: true,
          content: 'Executive Summary: Healthcare AI represents a transformative opportunity with unprecedented growth potential and measurable patient outcomes.',
          confidence: 85
        }
      ]
      
      const originalTask = 'Create comprehensive healthcare AI market analysis'
      
      console.log(`📋 Original Task: ${originalTask}`)
      console.log(`🤖 Agent Results: ${agentResults.length}`)
      
      console.log('\nSynthesizing results...')
      const synthesis = await agentResultSynthesis.synthesizeResults(agentResults, originalTask)
      
      console.log(`\n🔬 Synthesis Results:`)
      console.log(`  Success: ${synthesis.synthesis ? '✅' : '❌'}`)
      console.log(`  Strategy: ${synthesis.strategy}`)
      console.log(`  Quality Score: ${Math.round((synthesis.quality?.overall || 0) * 100)}%`)
      console.log(`  Conflicts Resolved: ${synthesis.conflictResolution?.conflicts?.length || 0}`)
      
      if (synthesis.synthesis?.content) {
        console.log(`\n📄 Synthesized Content Preview:`)
        console.log(`  "${synthesis.synthesis.content.substring(0, 150)}..."`)
        console.log(`  Length: ${synthesis.synthesis.content.length} characters`)
      }
      
      return { success: true, synthesis }
      
    } catch (error) {
      console.error('❌ Demo 5 failed:', error)
      return { success: false, error: error.message }
    }
  }

  // Run all demos
  async runAllDemos() {
    console.log('🎬 MULTI-AGENT SYSTEM DEMONSTRATION')
    console.log('=' .repeat(60))
    
    await this.initialize()
    
    const demos = [
      { name: 'Basic Agent Operations', method: this.demo1_BasicAgentOperations },
      { name: 'Task Decomposition', method: this.demo2_TaskDecomposition },
      { name: 'Multi-Agent Coordination', method: this.demo3_MultiAgentCoordination },
      { name: 'Specialized Agent Execution', method: this.demo4_SpecializedAgents },
      { name: 'Result Synthesis', method: this.demo5_ResultSynthesis }
    ]
    
    const results = []
    
    for (const demo of demos) {
      try {
        const result = await demo.method.call(this)
        results.push({ name: demo.name, ...result })
        
        if (result.success) {
          console.log(`\n✅ ${demo.name} completed successfully!`)
        } else {
          console.log(`\n❌ ${demo.name} failed: ${result.error}`)
        }
        
        // Small delay between demos
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.error(`❌ ${demo.name} threw error:`, error)
        results.push({ name: demo.name, success: false, error: error.message })
      }
    }
    
    // Summary
    console.log('\n🎯 DEMONSTRATION SUMMARY')
    console.log('=' .repeat(60))
    const successful = results.filter(r => r.success).length
    const total = results.length
    
    console.log(`Successful Demos: ${successful}/${total}`)
    console.log(`Success Rate: ${Math.round((successful / total) * 100)}%`)
    
    results.forEach(result => {
      const status = result.success ? '✅' : '❌'
      console.log(`${status} ${result.name}`)
    })
    
    if (successful === total) {
      console.log('\n🎉 All demonstrations completed successfully!')
      console.log('The Multi-Agent Collaboration System is working perfectly!')
    } else {
      console.log(`\n⚠️ ${total - successful} demonstration(s) had issues that need attention.`)
    }
    
    return results
  }

  // Quick demo for testing
  async quickDemo() {
    console.log('⚡ QUICK MULTI-AGENT DEMO')
    console.log('=' .repeat(40))
    
    await this.initialize()
    
    // Create agents
    const research = agentFramework.createAgent('research')
    const analysis = agentFramework.createAgent('analysis')
    console.log(`✅ Created agents: ${research.id}, ${analysis.id}`)
    
    // Test coordination
    const task = 'Quick test: analyze AI market trends'
    const coordination = await agentCoordinationSystem.coordinateTask(task)
    console.log(`✅ Coordination: ${coordination.success ? 'Success' : 'Failed'}`)
    
    // Test synthesis
    const mockResults = [
      { agentType: 'research', success: true, content: 'AI market growing rapidly', confidence: 85 },
      { agentType: 'analysis', success: true, content: 'Growth rate is 40% annually', confidence: 90 }
    ]
    const synthesis = await agentResultSynthesis.synthesizeResults(mockResults, task)
    console.log(`✅ Synthesis: ${synthesis.synthesis ? 'Success' : 'Failed'}`)
    
    console.log('\n🎉 Quick demo completed!')
    return true
  }
}

// Export demo class and convenience functions
const multiAgentDemo = new MultiAgentDemo()

export default multiAgentDemo
export const runFullDemo = () => multiAgentDemo.runAllDemos()
export const runQuickDemo = () => multiAgentDemo.quickDemo()
