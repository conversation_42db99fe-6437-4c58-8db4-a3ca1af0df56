// File Operations Tool for secure file handling
class FileOperationsTool {
  constructor() {
    this.name = 'file_operations'
    this.description = 'Handle file operations like reading, writing, and processing documents'
    this.allowedFileTypes = [
      'text/plain', 'text/csv', 'application/json', 'text/markdown',
      'text/html', 'text/css', 'text/javascript', 'application/javascript'
    ]
    this.maxFileSize = 1024 * 1024 // 1MB max file size
    this.storagePrefix = 'agi_files_'
  }

  // Check if file type is allowed
  isFileTypeAllowed(file) {
    return this.allowedFileTypes.includes(file.type) || 
           file.name.match(/\.(txt|csv|json|md|html|css|js)$/i)
  }

  // Check if file size is acceptable
  isFileSizeAcceptable(file) {
    return file.size <= this.maxFileSize
  }

  // Read file content
  async readFile(file) {
    try {
      console.log('📖 Reading file:', file.name)

      // Security checks
      if (!this.isFileTypeAllowed(file)) {
        throw new Error(`File type not allowed: ${file.type}`)
      }

      if (!this.isFileSizeAcceptable(file)) {
        throw new Error(`File too large: ${file.size} bytes (max: ${this.maxFileSize})`)
      }

      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        
        reader.onload = (event) => {
          const content = event.target.result
          resolve({
            success: true,
            fileName: file.name,
            fileSize: file.size,
            fileType: file.type,
            content: content,
            timestamp: new Date().toISOString()
          })
        }

        reader.onerror = () => {
          reject(new Error('Failed to read file'))
        }

        reader.readAsText(file)
      })

    } catch (error) {
      console.error('❌ File read failed:', error)
      return {
        success: false,
        error: error.message,
        fileName: file.name
      }
    }
  }

  // Write/save file content
  async writeFile(fileName, content, fileType = 'text/plain') {
    try {
      console.log('💾 Writing file:', fileName)

      // Create blob with content
      const blob = new Blob([content], { type: fileType })
      
      // Create download link
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      console.log('✅ File saved successfully')

      return {
        success: true,
        fileName: fileName,
        fileSize: blob.size,
        fileType: fileType,
        timestamp: new Date().toISOString(),
        action: 'download'
      }

    } catch (error) {
      console.error('❌ File write failed:', error)
      return {
        success: false,
        error: error.message,
        fileName: fileName
      }
    }
  }

  // Store file content in localStorage (temporary storage)
  async storeFile(fileName, content) {
    try {
      console.log('🗄️ Storing file in temporary storage:', fileName)

      const fileData = {
        fileName: fileName,
        content: content,
        timestamp: new Date().toISOString(),
        size: content.length
      }

      const storageKey = this.storagePrefix + fileName
      localStorage.setItem(storageKey, JSON.stringify(fileData))

      return {
        success: true,
        fileName: fileName,
        storageKey: storageKey,
        size: content.length,
        timestamp: fileData.timestamp
      }

    } catch (error) {
      console.error('❌ File storage failed:', error)
      return {
        success: false,
        error: error.message,
        fileName: fileName
      }
    }
  }

  // Retrieve file from localStorage
  async retrieveFile(fileName) {
    try {
      console.log('📂 Retrieving file from storage:', fileName)

      const storageKey = this.storagePrefix + fileName
      const storedData = localStorage.getItem(storageKey)

      if (!storedData) {
        throw new Error('File not found in storage')
      }

      const fileData = JSON.parse(storedData)

      return {
        success: true,
        fileName: fileData.fileName,
        content: fileData.content,
        timestamp: fileData.timestamp,
        size: fileData.size
      }

    } catch (error) {
      console.error('❌ File retrieval failed:', error)
      return {
        success: false,
        error: error.message,
        fileName: fileName
      }
    }
  }

  // List stored files
  async listStoredFiles() {
    try {
      console.log('📋 Listing stored files...')

      const files = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key.startsWith(this.storagePrefix)) {
          try {
            const fileData = JSON.parse(localStorage.getItem(key))
            files.push({
              fileName: fileData.fileName,
              size: fileData.size,
              timestamp: fileData.timestamp,
              storageKey: key
            })
          } catch (error) {
            console.warn('Invalid file data in storage:', key)
          }
        }
      }

      return {
        success: true,
        files: files,
        count: files.length
      }

    } catch (error) {
      console.error('❌ File listing failed:', error)
      return {
        success: false,
        error: error.message,
        files: []
      }
    }
  }

  // Delete stored file
  async deleteFile(fileName) {
    try {
      console.log('🗑️ Deleting file:', fileName)

      const storageKey = this.storagePrefix + fileName
      localStorage.removeItem(storageKey)

      return {
        success: true,
        fileName: fileName,
        action: 'deleted'
      }

    } catch (error) {
      console.error('❌ File deletion failed:', error)
      return {
        success: false,
        error: error.message,
        fileName: fileName
      }
    }
  }

  // Process CSV file
  async processCSV(content) {
    try {
      console.log('📊 Processing CSV content...')

      const lines = content.split('\n').filter(line => line.trim())
      if (lines.length === 0) {
        throw new Error('CSV file is empty')
      }

      // Parse CSV (simple implementation)
      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
      const rows = []

      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''))
        if (values.length === headers.length) {
          const row = {}
          headers.forEach((header, index) => {
            row[header] = values[index]
          })
          rows.push(row)
        }
      }

      return {
        success: true,
        headers: headers,
        rows: rows,
        rowCount: rows.length,
        columnCount: headers.length
      }

    } catch (error) {
      console.error('❌ CSV processing failed:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // Process JSON file
  async processJSON(content) {
    try {
      console.log('📄 Processing JSON content...')

      const data = JSON.parse(content)
      
      return {
        success: true,
        data: data,
        type: Array.isArray(data) ? 'array' : typeof data,
        size: Array.isArray(data) ? data.length : Object.keys(data).length
      }

    } catch (error) {
      console.error('❌ JSON processing failed:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // Main file operation handler
  async handleFileOperation(operation, ...args) {
    console.log(`📁 Handling file operation: ${operation}`)

    switch (operation) {
      case 'read':
        return await this.readFile(...args)
      case 'write':
        return await this.writeFile(...args)
      case 'store':
        return await this.storeFile(...args)
      case 'retrieve':
        return await this.retrieveFile(...args)
      case 'list':
        return await this.listStoredFiles()
      case 'delete':
        return await this.deleteFile(...args)
      case 'process_csv':
        return await this.processCSV(...args)
      case 'process_json':
        return await this.processJSON(...args)
      default:
        return {
          success: false,
          error: `Unknown operation: ${operation}`
        }
    }
  }

  // Format results for AI consumption
  formatResultsForAI(result, operation) {
    if (!result.success) {
      return `File operation failed: ${result.error}`
    }

    let formatted = `File Operation Results (${operation}):\n\n`

    switch (operation) {
      case 'read':
        formatted += `File: ${result.fileName}\n`
        formatted += `Size: ${result.fileSize} bytes\n`
        formatted += `Type: ${result.fileType}\n`
        formatted += `Content preview: ${result.content.substring(0, 200)}${result.content.length > 200 ? '...' : ''}\n`
        break
      case 'write':
        formatted += `File saved: ${result.fileName}\n`
        formatted += `Size: ${result.fileSize} bytes\n`
        break
      case 'list':
        formatted += `Found ${result.count} stored files:\n`
        result.files.forEach((file, index) => {
          formatted += `${index + 1}. ${file.fileName} (${file.size} bytes, ${file.timestamp})\n`
        })
        break
      case 'process_csv':
        formatted += `CSV processed successfully:\n`
        formatted += `Rows: ${result.rowCount}\n`
        formatted += `Columns: ${result.columnCount}\n`
        formatted += `Headers: ${result.headers.join(', ')}\n`
        break
      case 'process_json':
        formatted += `JSON processed successfully:\n`
        formatted += `Type: ${result.type}\n`
        formatted += `Size: ${result.size} items\n`
        break
      default:
        formatted += JSON.stringify(result, null, 2)
    }

    return formatted
  }

  // Get tool metadata
  getMetadata() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        operation: {
          type: 'string',
          description: 'File operation to perform',
          enum: ['read', 'write', 'store', 'retrieve', 'list', 'delete', 'process_csv', 'process_json'],
          required: true
        }
      },
      allowedFileTypes: this.allowedFileTypes,
      maxFileSize: this.maxFileSize,
      examples: [
        'Read this CSV file',
        'Save this data to a file',
        'Process this JSON data',
        'List stored files'
      ]
    }
  }

  // Check if tool should be used for given input
  shouldUse(input) {
    const fileIndicators = [
      'file', 'read', 'write', 'save', 'load', 'csv', 'json', 'document',
      'upload', 'download', 'process', 'parse', 'export', 'import'
    ]

    const inputLower = input.toLowerCase()
    return fileIndicators.some(indicator => inputLower.includes(indicator))
  }
}

// Create singleton instance
const fileOperationsTool = new FileOperationsTool()
export default fileOperationsTool
