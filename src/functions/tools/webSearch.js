// Web Search Tool for accessing current information
class WebSearchTool {
  constructor() {
    this.name = 'web_search'
    this.description = 'Search the web for current information, news, and real-time data'
    this.rateLimitDelay = 1000 // 1 second between requests
    this.lastRequestTime = 0
    this.maxResults = 5
  }

  // Check if search query is appropriate
  isSearchAppropriate(query) {
    const searchIndicators = [
      'current', 'latest', 'recent', 'news', 'today', 'now', 'what is happening',
      'price of', 'weather', 'stock', 'update', 'new', 'trending', 'breaking',
      'when did', 'who is', 'what happened', 'how much', 'where is'
    ]

    const queryLower = query.toLowerCase()
    return searchIndicators.some(indicator => queryLower.includes(indicator))
  }

  // Rate limiting
  async enforceRateLimit() {
    const now = Date.now()
    const timeSinceLastRequest = now - this.lastRequestTime
    
    if (timeSinceLastRequest < this.rateLimitDelay) {
      const waitTime = this.rateLimitDelay - timeSinceLastRequest
      await new Promise(resolve => setTimeout(resolve, waitTime))
    }
    
    this.lastRequestTime = Date.now()
  }

  // Search using SerpAPI (Google Search API) - requires API key but provides real results
  async searchSerpAPI(query) {
    try {
      await this.enforceRateLimit()

      // Check if API key is available
      const apiKey = import.meta.env.VITE_SERPAPI_KEY
      if (!apiKey) {
        console.log('⚠️ SerpAPI key not found, falling back to alternative search')
        return { success: false, error: 'No API key' }
      }

      // Note: Direct SerpAPI calls from browser will cause CORS issues
      // This would work in a backend environment or with a proxy
      console.log('⚠️ SerpAPI requires backend proxy to avoid CORS issues')
      console.log('🔄 Falling back to alternative search methods...')

      return {
        success: false,
        error: 'CORS restriction - SerpAPI requires backend proxy',
        query: query,
        timestamp: new Date().toISOString()
      }

    } catch (error) {
      console.error('❌ SerpAPI search failed:', error)
      return {
        success: false,
        query: query,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  // Search using DuckDuckGo HTML scraping (CORS-free alternative)
  async searchDuckDuckGoHTML(query) {
    try {
      await this.enforceRateLimit()

      console.log('🔍 Attempting DuckDuckGo search for:', query)

      // Try multiple CORS proxy services for better reliability
      const proxyServices = [
        'https://api.allorigins.win/get?url=',
        'https://corsproxy.io/?',
        'https://cors-anywhere.herokuapp.com/'
      ]

      for (const proxyUrl of proxyServices) {
        try {
          const searchUrl = `https://duckduckgo.com/html/?q=${encodeURIComponent(query)}`
          const url = proxyUrl + encodeURIComponent(searchUrl)

          console.log(`🔄 Trying proxy: ${proxyUrl.split('//')[1].split('/')[0]}`)

          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            },
            timeout: 10000 // 10 second timeout
          })

          if (!response.ok) {
            console.log(`❌ Proxy failed with status: ${response.status}`)
            continue
          }

          const data = await response.json()
          const htmlContent = data.contents || data

          if (!htmlContent || typeof htmlContent !== 'string') {
            console.log('❌ Invalid response format from proxy')
            continue
          }

          // Parse HTML to extract search results (simplified parsing)
          const results = this.parseSearchResults(htmlContent)

          if (results.length > 0) {
            console.log('✅ DuckDuckGo search completed, found', results.length, 'results')
            return {
              success: true,
              query: query,
              results: results,
              timestamp: new Date().toISOString(),
              source: 'DuckDuckGo'
            }
          }

        } catch (proxyError) {
          console.log(`❌ Proxy error: ${proxyError.message}`)
          continue
        }
      }

      // If all proxies failed
      throw new Error('All CORS proxy services failed')

    } catch (error) {
      console.error('❌ DuckDuckGo HTML search failed:', error)
      return {
        success: false,
        query: query,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  // Parse HTML search results (simplified)
  parseSearchResults(html) {
    const results = []

    try {
      // Create a temporary DOM parser
      const parser = new DOMParser()
      const doc = parser.parseFromString(html, 'text/html')

      // Find search result elements (DuckDuckGo specific selectors)
      const resultElements = doc.querySelectorAll('.result')

      resultElements.forEach((element, index) => {
        if (index >= 5) return // Limit to 5 results

        const titleElement = element.querySelector('.result__title a')
        const snippetElement = element.querySelector('.result__snippet')
        const urlElement = element.querySelector('.result__url')

        if (titleElement) {
          results.push({
            title: titleElement.textContent?.trim() || `Result ${index + 1}`,
            content: snippetElement?.textContent?.trim() || 'No description available',
            source: urlElement?.textContent?.trim() || 'Web',
            url: titleElement.href || '#',
            type: 'web'
          })
        }
      })
    } catch (error) {
      console.error('❌ Failed to parse search results:', error)
    }

    return results
  }

  // Fallback search with more realistic simulated results
  async searchFallback(query) {
    try {
      console.log('🔍 Using fallback search for:', query)

      // Generate more realistic simulated results based on query type
      const simulatedResults = this.generateSimulatedResults(query)

      return {
        success: true,
        query: query,
        results: simulatedResults,
        timestamp: new Date().toISOString(),
        source: 'Simulated (Fallback)'
      }

    } catch (error) {
      console.error('❌ Fallback search failed:', error)
      return {
        success: false,
        query: query,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  // Generate realistic simulated search results
  generateSimulatedResults(query) {
    const queryLower = query.toLowerCase()
    const results = []

    // Weather queries
    if (queryLower.includes('weather') || queryLower.includes('temperature') || queryLower.includes('forecast')) {
      const location = this.extractLocation(query) || 'your location'
      results.push(
        {
          title: `Weather in ${location}`,
          content: `Current weather conditions, temperature, and forecast for ${location}. Check real-time weather data and extended forecasts.`,
          source: 'Weather.com',
          url: 'https://weather.com',
          type: 'weather'
        },
        {
          title: `${location} Weather Forecast`,
          content: `7-day weather forecast including temperature, precipitation, and weather alerts for ${location}.`,
          source: 'AccuWeather',
          url: 'https://accuweather.com',
          type: 'weather'
        }
      )
    }

    // AI/Technology related queries
    else if (queryLower.includes('ai') || queryLower.includes('artificial intelligence') || queryLower.includes('technology') || queryLower.includes('machine learning')) {
      results.push(
        {
          title: 'Latest AI Developments - Tech News',
          content: 'Recent breakthroughs in artificial intelligence include advances in large language models, computer vision, and autonomous systems. Major tech companies continue to invest heavily in AI research.',
          source: 'TechCrunch',
          url: 'https://techcrunch.com/ai-news',
          type: 'news'
        },
        {
          title: 'AI Research Papers - arXiv',
          content: 'Latest research papers on artificial intelligence, machine learning, and deep learning from leading researchers worldwide.',
          source: 'arXiv.org',
          url: 'https://arxiv.org/list/cs.AI/recent',
          type: 'research'
        },
        {
          title: 'AI Tools and Applications',
          content: 'Comprehensive guide to AI tools, applications, and platforms for developers, researchers, and businesses.',
          source: 'GitHub',
          url: 'https://github.com/topics/artificial-intelligence',
          type: 'tools'
        }
      )
    }

    // Financial/Crypto queries
    else if (queryLower.includes('bitcoin') || queryLower.includes('crypto') || queryLower.includes('stock') || queryLower.includes('price')) {
      const asset = this.extractAsset(query) || 'financial markets'
      results.push(
        {
          title: `${asset} Price and Market Data`,
          content: `Real-time price, market cap, trading volume, and price charts for ${asset}. Track market trends and analysis.`,
          source: 'CoinMarketCap',
          url: 'https://coinmarketcap.com',
          type: 'finance'
        },
        {
          title: `${asset} News and Analysis`,
          content: `Latest news, market analysis, and expert opinions about ${asset} and related market movements.`,
          source: 'CoinDesk',
          url: 'https://coindesk.com',
          type: 'news'
        }
      )
    }

    // News related queries
    else if (queryLower.includes('news') || queryLower.includes('latest') || queryLower.includes('current') || queryLower.includes('today')) {
      results.push(
        {
          title: 'Breaking News Today',
          content: 'Stay updated with the latest breaking news, current events, and trending stories from around the world.',
          source: 'Reuters',
          url: 'https://reuters.com',
          type: 'news'
        },
        {
          title: 'Current Events Summary',
          content: 'A comprehensive overview of today\'s most important news stories and developments across various sectors.',
          source: 'Associated Press',
          url: 'https://apnews.com',
          type: 'news'
        },
        {
          title: 'Technology News',
          content: 'Latest technology news, product launches, and industry developments from leading tech companies.',
          source: 'The Verge',
          url: 'https://theverge.com',
          type: 'tech'
        }
      )
    }

    // Programming/Development queries
    else if (queryLower.includes('programming') || queryLower.includes('code') || queryLower.includes('javascript') || queryLower.includes('python') || queryLower.includes('react')) {
      const language = this.extractProgrammingLanguage(query) || 'programming'
      results.push(
        {
          title: `${language} Documentation and Tutorials`,
          content: `Official documentation, tutorials, and best practices for ${language} development.`,
          source: 'MDN Web Docs',
          url: 'https://developer.mozilla.org',
          type: 'documentation'
        },
        {
          title: `${language} Community and Forums`,
          content: `Active community discussions, Q&A, and problem-solving resources for ${language} developers.`,
          source: 'Stack Overflow',
          url: 'https://stackoverflow.com',
          type: 'community'
        }
      )
    }

    // Default general results
    else {
      results.push(
        {
          title: `Information about "${query}"`,
          content: `Comprehensive information and resources related to ${query}. This includes definitions, explanations, and relevant context.`,
          source: 'Wikipedia',
          url: 'https://wikipedia.org',
          type: 'reference'
        },
        {
          title: `${query} - Latest Updates`,
          content: `Recent news, updates, and developments related to ${query}. Stay informed with the latest information.`,
          source: 'Google News',
          url: 'https://news.google.com',
          type: 'news'
        },
        {
          title: `${query} - Research and Analysis`,
          content: `In-depth research, analysis, and expert insights about ${query} from academic and industry sources.`,
          source: 'ResearchGate',
          url: 'https://researchgate.net',
          type: 'research'
        }
      )
    }

    // Add a helpful disclaimer result
    results.push({
      title: '💡 Real Search Results Available',
      content: 'These are simulated search results for demonstration. For real-time search results, configure a search API key (SerpAPI recommended) in your environment variables. See SEARCH_API_SETUP.md for instructions.',
      source: 'AGI Playground',
      url: '#setup',
      type: 'notice'
    })

    return results
  }

  // Helper method to extract location from weather queries
  extractLocation(query) {
    const locationPatterns = [
      /weather in ([^,\s]+)/i,
      /([^,\s]+) weather/i,
      /temperature in ([^,\s]+)/i
    ]

    for (const pattern of locationPatterns) {
      const match = query.match(pattern)
      if (match) return match[1]
    }
    return null
  }

  // Helper method to extract asset from financial queries
  extractAsset(query) {
    const assets = ['bitcoin', 'ethereum', 'btc', 'eth', 'dogecoin', 'tesla', 'apple', 'google', 'microsoft']
    const queryLower = query.toLowerCase()

    for (const asset of assets) {
      if (queryLower.includes(asset)) {
        return asset.charAt(0).toUpperCase() + asset.slice(1)
      }
    }
    return null
  }

  // Helper method to extract programming language
  extractProgrammingLanguage(query) {
    const languages = ['javascript', 'python', 'react', 'node', 'typescript', 'java', 'c++', 'go', 'rust']
    const queryLower = query.toLowerCase()

    for (const lang of languages) {
      if (queryLower.includes(lang)) {
        return lang.charAt(0).toUpperCase() + lang.slice(1)
      }
    }
    return null
  }

  // Main search function
  async search(query) {
    if (!query || query.trim().length === 0) {
      return {
        success: false,
        error: 'Search query cannot be empty',
        timestamp: new Date().toISOString()
      }
    }

    console.log('🌐 Starting web search for:', query)

    // Try SerpAPI first (best results but requires API key)
    let result = await this.searchSerpAPI(query)

    // If SerpAPI fails, try DuckDuckGo HTML scraping
    if (!result.success || result.results.length === 0) {
      console.log('🔄 Trying DuckDuckGo HTML search...')
      result = await this.searchDuckDuckGoHTML(query)
    }

    // If both fail, use fallback
    if (!result.success || result.results.length === 0) {
      console.log('🔄 Trying fallback search...')
      result = await this.searchFallback(query)
    }

    return result
  }

  // Format search results for AI consumption
  formatResultsForAI(searchResult) {
    if (!searchResult.success) {
      return `Search failed: ${searchResult.error}`
    }

    if (searchResult.results.length === 0) {
      return `No search results found for "${searchResult.query}"`
    }

    let formatted = `Search Results for "${searchResult.query}":\n\n`
    
    searchResult.results.forEach((result, index) => {
      formatted += `${index + 1}. ${result.title}\n`
      formatted += `   ${result.content}\n`
      formatted += `   Source: ${result.source}\n`
      if (result.url) {
        formatted += `   URL: ${result.url}\n`
      }
      formatted += '\n'
    })
    
    formatted += `Search completed at: ${searchResult.timestamp}\n`
    formatted += `Source: ${searchResult.source}`
    
    return formatted
  }

  // Get tool metadata
  getMetadata() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        query: {
          type: 'string',
          description: 'The search query to execute',
          required: true
        }
      },
      examples: [
        'Search for current weather in Paris',
        'Find latest news about artificial intelligence',
        'What is the current price of Bitcoin?',
        'Who won the latest Nobel Prize in Physics?'
      ]
    }
  }

  // Check if tool should be used for given input
  shouldUse(input) {
    return this.isSearchAppropriate(input)
  }
}

// Create singleton instance
const webSearchTool = new WebSearchTool()
export default webSearchTool
