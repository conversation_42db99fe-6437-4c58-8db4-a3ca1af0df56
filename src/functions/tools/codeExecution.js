// Safe Code Execution Tool for running generated code
class CodeExecutionTool {
  constructor() {
    this.name = 'code_execution'
    this.description = 'Execute code safely in a sandboxed environment'
    this.supportedLanguages = ['javascript', 'python', 'html', 'css']
    this.executionTimeout = 5000 // 5 seconds max execution time
    this.maxOutputLength = 1000 // Max output characters
  }

  // Check if code execution is appropriate
  shouldExecuteCode(code, language) {
    // Security checks
    const dangerousPatterns = [
      /eval\s*\(/i,
      /Function\s*\(/i,
      /setTimeout\s*\(/i,
      /setInterval\s*\(/i,
      /XMLHttpRequest/i,
      /fetch\s*\(/i,
      /import\s+/i,
      /require\s*\(/i,
      /process\./i,
      /fs\./i,
      /child_process/i,
      /exec\s*\(/i,
      /spawn\s*\(/i,
      /\.innerHTML\s*=/i,
      /document\./i,
      /window\./i,
      /localStorage/i,
      /sessionStorage/i
    ]

    const codeStr = code.toString()
    for (const pattern of dangerousPatterns) {
      if (pattern.test(codeStr)) {
        return {
          safe: false,
          reason: `Potentially dangerous pattern detected: ${pattern.source}`
        }
      }
    }

    return { safe: true }
  }

  // Execute JavaScript code safely
  async executeJavaScript(code) {
    try {
      console.log('🔧 Executing JavaScript code...')
      
      // Security check
      const safetyCheck = this.shouldExecuteCode(code, 'javascript')
      if (!safetyCheck.safe) {
        return {
          success: false,
          error: `Security violation: ${safetyCheck.reason}`,
          output: null
        }
      }

      // Create a sandboxed execution environment
      const sandbox = {
        console: {
          log: (...args) => args.join(' '),
          error: (...args) => args.join(' '),
          warn: (...args) => args.join(' ')
        },
        Math: Math,
        Date: Date,
        JSON: JSON,
        Array: Array,
        Object: Object,
        String: String,
        Number: Number,
        Boolean: Boolean,
        parseInt: parseInt,
        parseFloat: parseFloat,
        isNaN: isNaN,
        isFinite: isFinite
      }

      // Capture console output
      let output = []
      const originalConsole = sandbox.console
      sandbox.console = {
        log: (...args) => {
          const message = args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
          ).join(' ')
          output.push(message)
          return message
        },
        error: (...args) => {
          const message = 'ERROR: ' + args.join(' ')
          output.push(message)
          return message
        },
        warn: (...args) => {
          const message = 'WARNING: ' + args.join(' ')
          output.push(message)
          return message
        }
      }

      // Execute code with timeout
      const executeWithTimeout = () => {
        return new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Execution timeout'))
          }, this.executionTimeout)

          try {
            // Create function with sandbox context
            // Wrap code in return statement if it's an expression
            let wrappedCode = code

            // Check if code is a simple expression (no semicolons, returns, etc.)
            const isExpression = !code.includes(';') &&
                                !code.includes('return') &&
                                !code.includes('function') &&
                                !code.includes('const') &&
                                !code.includes('let') &&
                                !code.includes('var') &&
                                !code.includes('{') &&
                                !code.includes('console.log')

            if (isExpression) {
              wrappedCode = `return (${code})`
            }

            const func = new Function(...Object.keys(sandbox), wrappedCode)
            const result = func(...Object.values(sandbox))
            
            clearTimeout(timeout)
            resolve(result)
          } catch (error) {
            clearTimeout(timeout)
            reject(error)
          }
        })
      }

      const result = await executeWithTimeout()
      
      // Combine output and result
      let finalOutput = output.join('\n')
      if (result !== undefined) {
        finalOutput += (finalOutput ? '\n' : '') + 'Return value: ' + 
          (typeof result === 'object' ? JSON.stringify(result, null, 2) : String(result))
      }

      // Limit output length
      if (finalOutput.length > this.maxOutputLength) {
        finalOutput = finalOutput.substring(0, this.maxOutputLength) + '\n... (output truncated)'
      }

      console.log('✅ JavaScript execution completed')
      
      return {
        success: true,
        output: finalOutput || 'Code executed successfully (no output)',
        language: 'javascript',
        executionTime: Date.now()
      }

    } catch (error) {
      console.error('❌ JavaScript execution failed:', error)
      return {
        success: false,
        error: error.message,
        output: null,
        language: 'javascript'
      }
    }
  }

  // Execute Python code (simulated - would need a real Python interpreter)
  async executePython(code) {
    try {
      console.log('🐍 Simulating Python execution...')
      
      // For demo purposes, we'll simulate Python execution
      // In production, you would use a service like Pyodide or a backend Python interpreter
      
      const safetyCheck = this.shouldExecuteCode(code, 'python')
      if (!safetyCheck.safe) {
        return {
          success: false,
          error: `Security violation: ${safetyCheck.reason}`,
          output: null
        }
      }

      // Simulate basic Python operations
      let output = 'Python execution simulated:\n'
      
      if (code.includes('print(')) {
        const printMatches = code.match(/print\((.*?)\)/g)
        if (printMatches) {
          printMatches.forEach(match => {
            const content = match.replace(/print\((.*?)\)/, '$1').replace(/['"]/g, '')
            output += content + '\n'
          })
        }
      } else {
        output += 'Code would execute in Python environment\n'
        output += 'Note: This is a simulation. Real Python execution requires backend integration.'
      }

      return {
        success: true,
        output: output,
        language: 'python',
        executionTime: Date.now(),
        simulated: true
      }

    } catch (error) {
      return {
        success: false,
        error: error.message,
        output: null,
        language: 'python'
      }
    }
  }

  // Execute HTML/CSS (render preview)
  async executeHTML(code) {
    try {
      console.log('🌐 Processing HTML/CSS...')
      
      // For HTML/CSS, we'll create a preview description
      // In production, you might render this in an iframe
      
      const hasCSS = code.includes('<style>') || code.includes('css')
      const hasJS = code.includes('<script>') || code.includes('javascript')
      
      let output = 'HTML Preview Generated:\n'
      output += `- Contains CSS: ${hasCSS ? 'Yes' : 'No'}\n`
      output += `- Contains JavaScript: ${hasJS ? 'Yes' : 'No'}\n`
      output += `- Code length: ${code.length} characters\n`
      output += '\nNote: In production, this would render a live preview.'

      return {
        success: true,
        output: output,
        language: 'html',
        executionTime: Date.now(),
        preview: true
      }

    } catch (error) {
      return {
        success: false,
        error: error.message,
        output: null,
        language: 'html'
      }
    }
  }

  // Main execution function
  async execute(code, language = 'javascript') {
    if (!code || code.trim().length === 0) {
      return {
        success: false,
        error: 'Code cannot be empty',
        output: null
      }
    }

    if (!this.supportedLanguages.includes(language.toLowerCase())) {
      return {
        success: false,
        error: `Language "${language}" is not supported. Supported: ${this.supportedLanguages.join(', ')}`,
        output: null
      }
    }

    console.log(`🚀 Executing ${language} code...`)

    switch (language.toLowerCase()) {
      case 'javascript':
        return await this.executeJavaScript(code)
      case 'python':
        return await this.executePython(code)
      case 'html':
      case 'css':
        return await this.executeHTML(code)
      default:
        return {
          success: false,
          error: `Execution not implemented for ${language}`,
          output: null
        }
    }
  }

  // Format execution results for AI consumption
  formatResultsForAI(executionResult) {
    if (!executionResult.success) {
      return `Code execution failed: ${executionResult.error}`
    }

    let formatted = `Code Execution Results (${executionResult.language}):\n\n`
    formatted += `Output:\n${executionResult.output}\n\n`
    
    if (executionResult.simulated) {
      formatted += 'Note: This was a simulated execution.\n'
    }
    
    if (executionResult.preview) {
      formatted += 'Note: HTML/CSS preview generated.\n'
    }
    
    formatted += `Executed at: ${new Date(executionResult.executionTime).toISOString()}`
    
    return formatted
  }

  // Get tool metadata
  getMetadata() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        code: {
          type: 'string',
          description: 'The code to execute',
          required: true
        },
        language: {
          type: 'string',
          description: 'Programming language',
          enum: this.supportedLanguages,
          default: 'javascript'
        }
      },
      examples: [
        'Execute this JavaScript function',
        'Run this Python script',
        'Test this HTML/CSS code',
        'Validate this algorithm'
      ]
    }
  }

  // Check if tool should be used for given input
  shouldUse(input) {
    const executionIndicators = [
      'run', 'execute', 'test', 'try', 'check', 'validate', 'demo',
      'show result', 'what does this do', 'does this work'
    ]

    const inputLower = input.toLowerCase()

    // Check for explicit execution keywords
    if (executionIndicators.some(indicator => inputLower.includes(indicator))) {
      return true
    }

    // Check for code patterns that suggest executable content
    const codePatterns = [
      /function\s+\w+\s*\(/,           // JavaScript functions
      /console\.log\s*\(/,            // Console output
      /def\s+\w+\s*\(/,              // Python functions
      /print\s*\(/,                  // Python print
      /class\s+\w+/,                 // Class definitions
      /import\s+\w+/,                // Import statements
      /from\s+\w+\s+import/,         // Python imports
      /const\s+\w+\s*=/,             // JavaScript const
      /let\s+\w+\s*=/,               // JavaScript let
      /var\s+\w+\s*=/,               // JavaScript var
      /if\s*\(/,                     // Conditional statements
      /for\s*\(/,                    // For loops
      /while\s*\(/,                  // While loops
      /return\s+/,                   // Return statements
      /\w+\(\)/,                     // Function calls
    ]

    // Check if input contains executable code patterns
    return codePatterns.some(pattern => pattern.test(input))
  }
}

// Create singleton instance
const codeExecutionTool = new CodeExecutionTool()
export default codeExecutionTool
