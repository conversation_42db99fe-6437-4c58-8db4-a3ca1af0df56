{"project": "AGI Playground", "version": "1.0.0", "description": "Advanced AI playground with file management capabilities", "features": ["Chat with AGI", "Code generation", "Image description", "Data analysis", "File management", "Memory system", "Tool integration"], "users": [{"id": 1, "name": "<PERSON>", "role": "Developer", "permissions": ["read", "write", "execute"], "lastLogin": "2025-01-24T10:30:00Z"}, {"id": 2, "name": "<PERSON>", "role": "Analyst", "permissions": ["read", "write"], "lastLogin": "2025-01-24T09:15:00Z"}, {"id": 3, "name": "<PERSON>", "role": "Admin", "permissions": ["read", "write", "execute", "admin"], "lastLogin": "2025-01-24T11:45:00Z"}], "settings": {"theme": "dark", "language": "en", "autoSave": true, "maxFileSize": "1MB", "allowedFileTypes": [".txt", ".csv", ".json", ".md", ".html", ".css", ".js"]}, "statistics": {"totalFiles": 0, "totalUsers": 3, "totalSessions": 156, "averageSessionDuration": "25 minutes"}}