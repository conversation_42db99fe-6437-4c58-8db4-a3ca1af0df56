<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Management Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .file-list {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📁 File Management Test</h1>
        <p>This page tests the file management functionality of the AGI Playground</p>
        
        <button class="test-button" onclick="testFileOperations()">Test File Operations</button>
        <button class="test-button" onclick="testFileStorage()">Test File Storage</button>
        <button class="test-button" onclick="listStoredFiles()">List Stored Files</button>
        <button class="test-button" onclick="clearAllFiles()">Clear All Files</button>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
        
        <div id="results"></div>
        
        <div id="fileList" class="file-list" style="display: none;">
            <h4>Stored Files:</h4>
            <div id="fileListContent"></div>
        </div>
    </div>

    <script>
        // Import the file operations tool (simulated)
        const fileOperationsTool = {
            storagePrefix: 'agi_files_',
            
            async handleFileOperation(operation, ...args) {
                console.log(`📁 Handling file operation: ${operation}`, args);
                
                switch (operation) {
                    case 'store':
                        return await this.storeFile(...args);
                    case 'retrieve':
                        return await this.retrieveFile(...args);
                    case 'list':
                        return await this.listStoredFiles();
                    case 'delete':
                        return await this.deleteFile(...args);
                    default:
                        return { success: false, error: `Unknown operation: ${operation}` };
                }
            },
            
            async storeFile(fileName, content) {
                try {
                    const fileData = {
                        fileName: fileName,
                        content: content,
                        timestamp: new Date().toISOString(),
                        size: content.length
                    };
                    
                    const storageKey = this.storagePrefix + fileName;
                    localStorage.setItem(storageKey, JSON.stringify(fileData));
                    
                    return {
                        success: true,
                        fileName: fileName,
                        storageKey: storageKey,
                        size: content.length,
                        timestamp: fileData.timestamp
                    };
                } catch (error) {
                    return { success: false, error: error.message, fileName: fileName };
                }
            },
            
            async retrieveFile(fileName) {
                try {
                    const storageKey = this.storagePrefix + fileName;
                    const storedData = localStorage.getItem(storageKey);
                    
                    if (!storedData) {
                        throw new Error('File not found in storage');
                    }
                    
                    const fileData = JSON.parse(storedData);
                    
                    return {
                        success: true,
                        fileName: fileData.fileName,
                        content: fileData.content,
                        timestamp: fileData.timestamp,
                        size: fileData.size
                    };
                } catch (error) {
                    return { success: false, error: error.message, fileName: fileName };
                }
            },
            
            async listStoredFiles() {
                try {
                    const files = [];
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key.startsWith(this.storagePrefix)) {
                            try {
                                const fileData = JSON.parse(localStorage.getItem(key));
                                files.push({
                                    fileName: fileData.fileName,
                                    size: fileData.size,
                                    timestamp: fileData.timestamp,
                                    storageKey: key
                                });
                            } catch (error) {
                                console.warn('Invalid file data in storage:', key);
                            }
                        }
                    }
                    
                    return { success: true, files: files, count: files.length };
                } catch (error) {
                    return { success: false, error: error.message, files: [] };
                }
            },
            
            async deleteFile(fileName) {
                try {
                    const storageKey = this.storagePrefix + fileName;
                    localStorage.removeItem(storageKey);
                    return { success: true, fileName: fileName };
                } catch (error) {
                    return { success: false, error: error.message, fileName: fileName };
                }
            }
        };

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultElement = document.createElement('div');
            resultElement.className = `status ${type}`;
            resultElement.innerHTML = message;
            resultsDiv.appendChild(resultElement);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('fileList').style.display = 'none';
        }

        async function testFileOperations() {
            addResult('🔍 Testing file operations...', 'info');
            
            try {
                // Test storing a file
                const testContent = 'This is a test file for the AGI Playground file management system.\n\nFeatures tested:\n- File storage\n- File retrieval\n- File listing\n- File deletion';
                const storeResult = await fileOperationsTool.handleFileOperation('store', 'test-file.txt', testContent);
                
                if (storeResult.success) {
                    addResult('✅ File storage test passed', 'success');
                    
                    // Test retrieving the file
                    const retrieveResult = await fileOperationsTool.handleFileOperation('retrieve', 'test-file.txt');
                    
                    if (retrieveResult.success && retrieveResult.content === testContent) {
                        addResult('✅ File retrieval test passed', 'success');
                        
                        // Test file listing
                        const listResult = await fileOperationsTool.handleFileOperation('list');
                        
                        if (listResult.success && listResult.files.length > 0) {
                            addResult(`✅ File listing test passed (${listResult.files.length} files found)`, 'success');
                        } else {
                            addResult('❌ File listing test failed', 'error');
                        }
                        
                    } else {
                        addResult('❌ File retrieval test failed', 'error');
                    }
                } else {
                    addResult('❌ File storage test failed: ' + storeResult.error, 'error');
                }
                
            } catch (error) {
                addResult('❌ File operations test failed: ' + error.message, 'error');
            }
        }

        async function testFileStorage() {
            addResult('🔍 Testing file storage with multiple files...', 'info');
            
            try {
                const testFiles = [
                    { name: 'sample.json', content: '{"test": true, "data": [1, 2, 3]}' },
                    { name: 'readme.md', content: '# Test File\n\nThis is a markdown test file.' },
                    { name: 'script.js', content: 'console.log("Hello from AGI Playground!");' }
                ];
                
                for (const file of testFiles) {
                    const result = await fileOperationsTool.handleFileOperation('store', file.name, file.content);
                    if (result.success) {
                        addResult(`✅ Stored ${file.name} (${result.size} bytes)`, 'success');
                    } else {
                        addResult(`❌ Failed to store ${file.name}: ${result.error}`, 'error');
                    }
                }
                
                addResult('📊 File storage test completed', 'info');
                
            } catch (error) {
                addResult('❌ File storage test failed: ' + error.message, 'error');
            }
        }

        async function listStoredFiles() {
            try {
                const result = await fileOperationsTool.handleFileOperation('list');
                
                if (result.success) {
                    const fileListDiv = document.getElementById('fileList');
                    const fileListContent = document.getElementById('fileListContent');
                    
                    if (result.files.length === 0) {
                        fileListContent.innerHTML = '<p>No files stored</p>';
                    } else {
                        let html = '<ul>';
                        result.files.forEach(file => {
                            const date = new Date(file.timestamp).toLocaleString();
                            const size = file.size < 1024 ? `${file.size} B` : `${(file.size / 1024).toFixed(1)} KB`;
                            html += `<li><strong>${file.fileName}</strong> - ${size} - ${date}</li>`;
                        });
                        html += '</ul>';
                        fileListContent.innerHTML = html;
                    }
                    
                    fileListDiv.style.display = 'block';
                    addResult(`📋 Found ${result.files.length} stored files`, 'info');
                } else {
                    addResult('❌ Failed to list files: ' + result.error, 'error');
                }
            } catch (error) {
                addResult('❌ File listing failed: ' + error.message, 'error');
            }
        }

        async function clearAllFiles() {
            if (!confirm('Are you sure you want to delete all stored files?')) return;
            
            try {
                const listResult = await fileOperationsTool.handleFileOperation('list');
                
                if (listResult.success) {
                    let deletedCount = 0;
                    
                    for (const file of listResult.files) {
                        const deleteResult = await fileOperationsTool.handleFileOperation('delete', file.fileName);
                        if (deleteResult.success) {
                            deletedCount++;
                        }
                    }
                    
                    addResult(`🗑️ Deleted ${deletedCount} files`, 'success');
                    document.getElementById('fileList').style.display = 'none';
                } else {
                    addResult('❌ Failed to clear files: ' + listResult.error, 'error');
                }
            } catch (error) {
                addResult('❌ Clear files failed: ' + error.message, 'error');
            }
        }

        // Auto-test on page load
        window.addEventListener('load', function() {
            addResult('🚀 File management test page loaded', 'info');
            addResult('💡 Click the test buttons to verify file operations', 'info');
        });
    </script>
</body>
</html>
